import React from 'react';
import { Provider } from 'react-redux';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { store } from './store';
import AppRouter from './router';
import MemoryMonitor from './components/Debug/MemoryMonitor';
import 'leaflet/dist/leaflet.css';
import './App.css';

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ConfigProvider locale={zhCN}>
        <AppRouter />
        {/* 开发环境下显示内存监控 */}
        {process.env.NODE_ENV === 'development' && (
          <MemoryMonitor
            visible={!window.location.search.includes('hide-memory-monitor')}
            compact={true}
            position="bottom-right"
          />
        )}
      </ConfigProvider>
    </Provider>
  );
};

export default App;
