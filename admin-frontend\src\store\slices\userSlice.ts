import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { userApi, type UserSearchParams } from '../../services/api/userApi';
import type { User, Role, UserFormData, PaginatedResponse } from '../../types';

interface UserState {
  // 用户列表
  users: User[];
  searchParams: UserSearchParams;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  
  // 当前用户详情
  currentUser: User | null;
  
  // 角色列表
  roles: Role[];
  
  // 加载状态
  loading: {
    list: boolean;
    detail: boolean;
    create: boolean;
    update: boolean;
    delete: boolean;
    roles: boolean;
  };
  
  error: string | null;
}

const initialState: UserState = {
  users: [],
  searchParams: {
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'DESC',
  },
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  },
  currentUser: null,
  roles: [],
  loading: {
    list: false,
    detail: false,
    create: false,
    update: false,
    delete: false,
    roles: false,
  },
  error: null,
};

// 获取用户列表
export const fetchUsers = createAsyncThunk(
  'user/fetchUsers',
  async (params: UserSearchParams = {}, { rejectWithValue }) => {
    try {
      const response = await userApi.getUsers(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '获取用户列表失败');
    }
  }
);

// 获取用户详情
export const fetchUser = createAsyncThunk(
  'user/fetchUser',
  async (id: string, { rejectWithValue }) => {
    try {
      const user = await userApi.getUser(id);
      return user;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '获取用户详情失败');
    }
  }
);

// 创建用户
export const createUser = createAsyncThunk(
  'user/createUser',
  async (data: UserFormData, { rejectWithValue }) => {
    try {
      const user = await userApi.createUser(data);
      return user;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '创建用户失败');
    }
  }
);

// 更新用户
export const updateUser = createAsyncThunk(
  'user/updateUser',
  async ({ id, data }: { id: string; data: Partial<UserFormData> }, { rejectWithValue }) => {
    try {
      const user = await userApi.updateUser(id, data);
      return user;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '更新用户失败');
    }
  }
);

// 删除用户
export const deleteUser = createAsyncThunk(
  'user/deleteUser',
  async (id: string, { rejectWithValue }) => {
    try {
      await userApi.deleteUser(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '删除用户失败');
    }
  }
);

// 批量删除用户
export const deleteUsers = createAsyncThunk(
  'user/deleteUsers',
  async (ids: string[], { rejectWithValue }) => {
    try {
      await userApi.deleteUsers(ids);
      return ids;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '批量删除用户失败');
    }
  }
);

// 切换用户状态
export const toggleUserStatus = createAsyncThunk(
  'user/toggleUserStatus',
  async ({ id, status }: { id: string; status: 'active' | 'disabled' }, { rejectWithValue }) => {
    try {
      const user = await userApi.toggleUserStatus(id, status);
      return user;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '切换用户状态失败');
    }
  }
);

// 获取角色列表
export const fetchRoles = createAsyncThunk(
  'user/fetchRoles',
  async (_, { rejectWithValue }) => {
    try {
      const roles = await userApi.getRoles();
      return roles;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '获取角色列表失败');
    }
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setSearchParams: (state, action) => {
      state.searchParams = { ...state.searchParams, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentUser: (state) => {
      state.currentUser = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取用户列表
      .addCase(fetchUsers.pending, (state) => {
        state.loading.list = true;
        state.error = null;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.loading.list = false;
        state.users = action.payload.data;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.loading.list = false;
        state.error = action.payload as string;
      })
      // 获取用户详情
      .addCase(fetchUser.pending, (state) => {
        state.loading.detail = true;
      })
      .addCase(fetchUser.fulfilled, (state, action) => {
        state.loading.detail = false;
        state.currentUser = action.payload;
      })
      .addCase(fetchUser.rejected, (state, action) => {
        state.loading.detail = false;
        state.error = action.payload as string;
      })
      // 创建用户
      .addCase(createUser.pending, (state) => {
        state.loading.create = true;
      })
      .addCase(createUser.fulfilled, (state, action) => {
        state.loading.create = false;
        state.users.unshift(action.payload);
      })
      .addCase(createUser.rejected, (state, action) => {
        state.loading.create = false;
        state.error = action.payload as string;
      })
      // 更新用户
      .addCase(updateUser.pending, (state) => {
        state.loading.update = true;
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        state.loading.update = false;
        const index = state.users.findIndex(user => user.id === action.payload.id);
        if (index !== -1) {
          state.users[index] = action.payload;
        }
        if (state.currentUser?.id === action.payload.id) {
          state.currentUser = action.payload;
        }
      })
      .addCase(updateUser.rejected, (state, action) => {
        state.loading.update = false;
        state.error = action.payload as string;
      })
      // 删除用户
      .addCase(deleteUser.pending, (state) => {
        state.loading.delete = true;
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.loading.delete = false;
        state.users = state.users.filter(user => user.id !== action.payload);
      })
      .addCase(deleteUser.rejected, (state, action) => {
        state.loading.delete = false;
        state.error = action.payload as string;
      })
      // 批量删除用户
      .addCase(deleteUsers.fulfilled, (state, action) => {
        state.users = state.users.filter(user => !action.payload.includes(user.id));
      })
      // 切换用户状态
      .addCase(toggleUserStatus.fulfilled, (state, action) => {
        const index = state.users.findIndex(user => user.id === action.payload.id);
        if (index !== -1) {
          state.users[index] = action.payload;
        }
      })
      // 获取角色列表
      .addCase(fetchRoles.pending, (state) => {
        state.loading.roles = true;
      })
      .addCase(fetchRoles.fulfilled, (state, action) => {
        state.loading.roles = false;
        state.roles = action.payload;
      })
      .addCase(fetchRoles.rejected, (state, action) => {
        state.loading.roles = false;
        state.error = action.payload as string;
      });
  },
});

export const { setSearchParams, clearError, clearCurrentUser } = userSlice.actions;
export default userSlice.reducer;
