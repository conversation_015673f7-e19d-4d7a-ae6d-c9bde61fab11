import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  OneToOne,
  Jo<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { Species } from './Species';

@Entity('taxonomic_classifications')
export class TaxonomicClassification {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  kingdom?: string; // 界

  @Column({ nullable: true })
  phylum?: string; // 门

  @Column({ nullable: true })
  class?: string; // 纲

  @Column({ nullable: true })
  order?: string; // 目

  @Column({ nullable: true })
  family?: string; // 科

  @Column({ nullable: true })
  genus?: string; // 属

  @Column({ nullable: true })
  speciesName?: string; // 种名

  @OneToOne(() => Species, species => species.classification)
  @JoinColumn({ name: 'species_id' })
  species: Species;
}
