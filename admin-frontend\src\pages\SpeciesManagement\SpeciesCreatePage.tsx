import React from 'react';
import { Card, Typography, Result, Button } from 'antd';
import { useNavigate } from 'react-router-dom';

const { Title } = Typography;

const SpeciesCreatePage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>新增物种</Title>
      </div>

      <Card>
        <Result
          status="info"
          title="功能开发中"
          subTitle="新增物种功能正在开发中，敬请期待。"
          extra={
            <Button type="primary" onClick={() => navigate('/species')}>
              返回物种列表
            </Button>
          }
        />
      </Card>
    </div>
  );
};

export default SpeciesCreatePage;
