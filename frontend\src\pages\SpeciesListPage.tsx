import React, { useState } from 'react';
import { 
  Card, 
  Input, 
  Select, 
  Row, 
  Col, 
  List, 
  Avatar, 
  Tag, 
  Pagination,
  Space,
  Typography,
  Button,
  Divider
} from 'antd';
import { SearchOutlined, FilterOutlined, EyeOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';

const { Search } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

const SpeciesListPage: React.FC = () => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [conservationStatus, setConservationStatus] = useState<string | undefined>();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);

  // 模拟数据
  const mockSpecies = Array.from({ length: 20 }, (_, index) => ({
    id: `species-${index + 1}`,
    chineseName: `海洋生物 ${index + 1}`,
    englishName: `Marine Species ${index + 1}`,
    latinName: `Marinus species ${index + 1}`,
    conservationStatus: ['LC', 'NT', 'VU', 'EN', 'CR'][index % 5],
    description: '这是一种生活在深海中的神秘生物，具有独特的发声能力...',
    imageUrl: null,
    audioCount: Math.floor(Math.random() * 20) + 1,
    classification: {
      kingdom: '动物界',
      phylum: '脊索动物门',
      class: '哺乳纲',
      order: '鲸目',
      family: '须鲸科'
    }
  }));

  const conservationStatusMap = {
    'LC': { text: '无危', color: 'green' },
    'NT': { text: '近危', color: 'blue' },
    'VU': { text: '易危', color: 'orange' },
    'EN': { text: '濒危', color: 'red' },
    'CR': { text: '极危', color: 'purple' }
  };

  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setCurrentPage(1);
    // TODO: 实际搜索逻辑
  };

  const handleFilterChange = (value: string) => {
    setConservationStatus(value);
    setCurrentPage(1);
    // TODO: 实际筛选逻辑
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ marginBottom: '24px' }}>
          物种数据库
        </Title>
        
        {/* 搜索和筛选 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} md={12}>
            <Search
              placeholder="搜索物种名称（中文名、英文名或拉丁学名）"
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              onSearch={handleSearch}
            />
          </Col>
          <Col xs={24} md={6}>
            <Select
              placeholder="保护状态"
              allowClear
              style={{ width: '100%' }}
              size="large"
              onChange={handleFilterChange}
            >
              <Option value="LC">无危 (LC)</Option>
              <Option value="NT">近危 (NT)</Option>
              <Option value="VU">易危 (VU)</Option>
              <Option value="EN">濒危 (EN)</Option>
              <Option value="CR">极危 (CR)</Option>
            </Select>
          </Col>
          <Col xs={24} md={6}>
            <Button 
              icon={<FilterOutlined />} 
              size="large"
              style={{ width: '100%' }}
            >
              高级筛选
            </Button>
          </Col>
        </Row>

        {/* 搜索结果统计 */}
        <div style={{ marginBottom: '16px' }}>
          <Text type="secondary">
            找到 {mockSpecies.length} 个物种
            {searchKeyword && ` · 关键词: "${searchKeyword}"`}
            {conservationStatus && ` · 保护状态: ${conservationStatusMap[conservationStatus as keyof typeof conservationStatusMap]?.text}`}
          </Text>
        </div>
      </Card>

      {/* 物种列表 */}
      <Card>
        <List
          itemLayout="vertical"
          size="large"
          dataSource={mockSpecies}
          renderItem={(species) => (
            <List.Item
              key={species.id}
              actions={[
                <Link to={`/species/${species.id}`} key="view">
                  <Button type="primary" icon={<EyeOutlined />}>
                    查看详情
                  </Button>
                </Link>
              ]}
              extra={
                species.imageUrl ? (
                  <img
                    width={200}
                    height={120}
                    alt={species.chineseName}
                    src={species.imageUrl}
                    style={{ objectFit: 'cover', borderRadius: '6px' }}
                  />
                ) : (
                  <div style={{
                    width: 200,
                    height: 120,
                    background: '#f5f5f5',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '6px',
                    color: '#999'
                  }}>
                    暂无图片
                  </div>
                )
              }
            >
              <List.Item.Meta
                avatar={
                  <Avatar 
                    size={64} 
                    style={{ 
                      backgroundColor: '#1890ff',
                      fontSize: '24px'
                    }}
                  >
                    {species.chineseName.charAt(0)}
                  </Avatar>
                }
                title={
                  <Space direction="vertical" size={4}>
                    <div>
                      <Text strong style={{ fontSize: '18px' }}>
                        {species.chineseName}
                      </Text>
                      {species.conservationStatus && (
                        <Tag 
                          color={conservationStatusMap[species.conservationStatus as keyof typeof conservationStatusMap]?.color}
                          style={{ marginLeft: '8px' }}
                        >
                          {conservationStatusMap[species.conservationStatus as keyof typeof conservationStatusMap]?.text}
                        </Tag>
                      )}
                    </div>
                    <Text type="secondary">{species.englishName}</Text>
                    <Text type="secondary" italic>{species.latinName}</Text>
                  </Space>
                }
                description={
                  <div style={{ marginTop: '8px' }}>
                    <Text>{species.description}</Text>
                    <Divider type="vertical" />
                    <Text type="secondary">
                      音频文件: {species.audioCount} 个
                    </Text>
                    <div style={{ marginTop: '8px' }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {species.classification.kingdom} → {species.classification.phylum} → {species.classification.class} → {species.classification.order} → {species.classification.family}
                      </Text>
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />

        {/* 分页 */}
        <div style={{ textAlign: 'center', marginTop: '32px' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={200} // 模拟总数
            showSizeChanger={false}
            showQuickJumper
            showTotal={(total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }
            onChange={(page) => setCurrentPage(page)}
          />
        </div>
      </Card>
    </div>
  );
};

export default SpeciesListPage;
