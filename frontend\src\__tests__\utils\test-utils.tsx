import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { configureStore } from '@reduxjs/toolkit';
import authSlice from '../../store/slices/authSlice';
import speciesSlice from '../../store/slices/speciesSlice';
import uiSlice from '../../store/slices/uiSlice';

// 创建测试用的store
export const createTestStore = (preloadedState?: any) => {
  return configureStore({
    reducer: {
      auth: authSlice,
      species: speciesSlice,
      ui: uiSlice,
    },
    preloadedState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  });
};

// 包装器组件
interface AllTheProvidersProps {
  children: React.ReactNode;
  store?: ReturnType<typeof createTestStore>;
}

const AllTheProviders: React.FC<AllTheProvidersProps> = ({ 
  children, 
  store = createTestStore() 
}) => {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <ConfigProvider locale={zhCN}>
          {children}
        </ConfigProvider>
      </BrowserRouter>
    </Provider>
  );
};

// 自定义render函数
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  preloadedState?: any;
  store?: ReturnType<typeof createTestStore>;
}

const customRender = (
  ui: ReactElement,
  {
    preloadedState,
    store = createTestStore(preloadedState),
    ...renderOptions
  }: CustomRenderOptions = {}
) => {
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <AllTheProviders store={store}>{children}</AllTheProviders>
  );

  return {
    store,
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  };
};

// 模拟用户数据
export const mockUser = {
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  roles: [
    {
      id: '1',
      name: 'viewer',
      description: '查看者',
      permissions: {}
    }
  ],
  lastLogin: '2024-01-01T00:00:00.000Z',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
};

// 模拟物种数据
export const mockSpecies = {
  id: '1',
  chineseName: '蓝鲸',
  englishName: 'Blue Whale',
  latinName: 'Balaenoptera musculus',
  conservationStatus: '濒危',
  description: '世界上最大的动物',
  representativeImagePath: 'images/blue-whale.jpg',
  classification: {
    kingdom: '动物界',
    phylum: '脊索动物门',
    class: '哺乳纲',
    order: '鲸目',
    family: '须鲸科',
    genus: '蓝鲸属',
    species: '蓝鲸'
  },
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
};

// 模拟音频文件数据
export const mockAudioFile = {
  id: '1',
  fileName: 'blue-whale-song.wav',
  filePath: 'audio/blue-whale-song.wav',
  recordingLocation: '太平洋',
  recordingTime: '2024-01-01T00:00:00.000Z',
  behaviorDescription: '歌声',
  duration: 120,
  fileSize: 1024000
};

// 模拟认证状态
export const mockAuthState = {
  user: mockUser,
  token: 'mock-jwt-token',
  isAuthenticated: true,
  loading: false,
  error: null
};

// 模拟物种状态
export const mockSpeciesState = {
  speciesList: [mockSpecies],
  searchParams: {
    page: 1,
    limit: 20,
    sortBy: 'chineseName',
    sortOrder: 'ASC' as const
  },
  pagination: {
    current: 1,
    pageSize: 20,
    total: 1,
    totalPages: 1
  },
  currentSpecies: mockSpecies,
  speciesDetails: null,
  taxonomyStats: null,
  conservationStats: null,
  loading: {
    list: false,
    detail: false,
    stats: false
  },
  error: null
};

// 模拟UI状态
export const mockUIState = {
  globalLoading: false,
  sidebarCollapsed: false,
  theme: 'light' as const,
  locale: 'zh-CN' as const,
  mapMode: 'distribution' as const,
  mapCenter: [30.0, 120.0] as [number, number],
  mapZoom: 5,
  audioPlayer: {
    isPlaying: false,
    currentTrack: null,
    volume: 0.8,
    currentTime: 0,
    duration: 0,
    playlist: [],
    playMode: 'single' as const
  },
  searchHistory: [],
  notifications: [],
  modals: {
    loginVisible: false,
    registerVisible: false,
    profileVisible: false,
    settingsVisible: false
  },
  pageLoading: {}
};

// 等待异步操作完成
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0));
};

// 模拟文件对象
export const createMockFile = (
  name: string = 'test.wav',
  size: number = 1024,
  type: string = 'audio/wav'
): File => {
  const file = new File([''], name, { type });
  Object.defineProperty(file, 'size', {
    value: size,
    writable: false,
  });
  return file;
};

// 模拟拖拽事件
export const createMockDragEvent = (files: File[]) => {
  return {
    dataTransfer: {
      files,
      items: files.map(file => ({
        kind: 'file',
        type: file.type,
        getAsFile: () => file,
      })),
      types: ['Files'],
    },
    preventDefault: jest.fn(),
    stopPropagation: jest.fn(),
  };
};

// 重新导出所有testing-library的工具
export * from '@testing-library/react';
export { customRender as render };
