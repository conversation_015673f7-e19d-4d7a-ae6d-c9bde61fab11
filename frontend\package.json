{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:e2e:ci": "cypress run --browser chrome --headless", "cypress:install": "cypress install", "type-check": "tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@reduxjs/toolkit": "^2.8.2", "@types/react-router-dom": "^5.3.3", "@types/wavesurfer.js": "^6.0.12", "antd": "^5.26.5", "axios": "^1.10.0", "leaflet": "^1.9.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-leaflet": "^4.2.1", "react-redux": "^9.2.0", "react-router-dom": "^7.7.0", "wavesurfer.js": "^7.10.0"}, "devDependencies": {"@cypress/react": "^9.0.1", "@cypress/webpack-dev-server": "^4.1.0", "@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/leaflet": "^1.9.20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/supertest": "^6.0.3", "@vitejs/plugin-react": "^4.6.0", "cypress": "^14.5.2", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "msw": "^2.10.4", "prettier": "^3.6.2", "supertest": "^7.1.3", "ts-jest": "^29.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}