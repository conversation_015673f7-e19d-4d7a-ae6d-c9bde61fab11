import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { setupMemoryPressureDetection, emergencyDisableProfiler } from './utils/performanceUtils'
import { memoryManager } from './utils/memoryManager'

// 设置内存压力检测
if (process.env.NODE_ENV === 'development') {
  // 启动新的内存管理器
  memoryManager.start();

  // 设置内存警告监听器
  memoryManager.onWarning((info) => {
    console.warn(`⚠️ 内存使用较高: ${info.percentage.toFixed(1)}% (趋势: ${info.trend})`);
  });

  // 设置内存紧急监听器
  memoryManager.onCritical((info) => {
    console.error(`🚨 内存使用过高: ${info.percentage.toFixed(1)}%，禁用性能分析器！`);
    emergencyDisableProfiler();

    // 尝试强制垃圾回收
    if (memoryManager.forceGarbageCollection()) {
      console.log('✅ 已执行垃圾回收');
    }
  });

  // 保持原有的检测机制作为备用
  setupMemoryPressureDetection();
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
