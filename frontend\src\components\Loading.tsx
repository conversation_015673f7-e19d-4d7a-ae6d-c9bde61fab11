import React from 'react';
import { Spin, Typography } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface LoadingProps {
  size?: 'small' | 'default' | 'large';
  tip?: string;
  spinning?: boolean;
  children?: React.ReactNode;
  style?: React.CSSProperties;
  fullScreen?: boolean;
}

const Loading: React.FC<LoadingProps> = ({
  size = 'default',
  tip = '加载中...',
  spinning = true,
  children,
  style,
  fullScreen = false,
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: size === 'large' ? 48 : size === 'small' ? 16 : 24 }} spin />;

  if (fullScreen) {
    return (
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'rgba(255, 255, 255, 0.9)',
          zIndex: 9999,
          ...style,
        }}
      >
        <Spin indicator={antIcon} size={size} />
        {tip && (
          <Text type="secondary" style={{ marginTop: '16px', fontSize: '16px' }}>
            {tip}
          </Text>
        )}
      </div>
    );
  }

  if (children) {
    return (
      <Spin spinning={spinning} tip={tip} indicator={antIcon} size={size} style={style}>
        {children}
      </Spin>
    );
  }

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '40px',
        ...style,
      }}
    >
      <Spin indicator={antIcon} size={size} />
      {tip && (
        <Text type="secondary" style={{ marginTop: '16px' }}>
          {tip}
        </Text>
      )}
    </div>
  );
};

// 页面级加载组件
export const PageLoading: React.FC<{ tip?: string }> = ({ tip = '页面加载中...' }) => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '400px',
        background: '#fafafa',
      }}
    >
      <Spin size="large" />
      <Text type="secondary" style={{ marginTop: '16px', fontSize: '16px' }}>
        {tip}
      </Text>
    </div>
  );
};

// 内容加载组件
export const ContentLoading: React.FC<{ tip?: string }> = ({ tip = '内容加载中...' }) => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '60px 20px',
      }}
    >
      <Spin />
      <Text type="secondary" style={{ marginTop: '12px' }}>
        {tip}
      </Text>
    </div>
  );
};

// 列表加载骨架屏
export const ListLoading: React.FC = () => {
  return (
    <div>
      {Array.from({ length: 5 }).map((_, index) => (
        <div
          key={index}
          style={{
            padding: '16px',
            borderBottom: '1px solid #f0f0f0',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <div
            style={{
              width: '48px',
              height: '48px',
              borderRadius: '50%',
              background: '#f0f0f0',
              marginRight: '16px',
            }}
          />
          <div style={{ flex: 1 }}>
            <div
              style={{
                height: '16px',
                background: '#f0f0f0',
                borderRadius: '4px',
                marginBottom: '8px',
                width: '60%',
              }}
            />
            <div
              style={{
                height: '14px',
                background: '#f0f0f0',
                borderRadius: '4px',
                width: '40%',
              }}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default Loading;
