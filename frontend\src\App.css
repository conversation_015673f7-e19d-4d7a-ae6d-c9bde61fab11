/* 政府网站标准全局样式 */
#root {
  width: 100%;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  font-family: "Microsoft YaHei", "SimHei", -apple-system, BlinkMacSystemFont,
    'Segoe UI', '<PERSON><PERSON>', '<PERSON>xy<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Fira Sans',
    'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #1f2937;
}

/* 政府网站标准滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: #9ca3af;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 政府网站标准卡片样式 */
.ant-card {
  border-radius: 4px !important;
  border: 1px solid #d1d5db !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

.ant-card:hover {
  box-shadow: 0 2px 6px rgba(0,0,0,0.15) !important;
  transition: box-shadow 0.2s ease;
}

/* 政府网站标准按钮样式 */
.ant-btn-primary {
  background: #1e3a8a !important;
  border-color: #1e3a8a !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  font-family: "Microsoft YaHei", "SimHei", sans-serif !important;
}

.ant-btn-primary:hover {
  background: #1d4ed8 !important;
  border-color: #1d4ed8 !important;
}

/* 政府网站标准输入框样式 */
.ant-input,
.ant-input-search {
  border-radius: 4px !important;
  border-color: #d1d5db !important;
  font-family: "Microsoft YaHei", "SimHei", sans-serif !important;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #1e3a8a !important;
  box-shadow: 0 0 0 2px rgba(30,58,138,0.2) !important;
}

/* 政府网站标准菜单样式 */
.ant-menu-horizontal > .ant-menu-item {
  border-radius: 0 !important;
  font-family: "Microsoft YaHei", "SimHei", sans-serif !important;
}

.ant-menu-horizontal > .ant-menu-item:hover {
  color: rgba(255,255,255,1) !important;
  background: rgba(255,255,255,0.1) !important;
}

.ant-menu-horizontal > .ant-menu-item-selected {
  color: #ffffff !important;
  background: rgba(255,255,255,0.15) !important;
  border-bottom: 2px solid #ffffff !important;
}

/* 政府网站标准统计数字样式 */
.ant-statistic-content {
  font-family: "Microsoft YaHei", "SimHei", sans-serif !important;
}

.ant-statistic-title {
  font-family: "Microsoft YaHei", "SimHei", sans-serif !important;
}

/* 政府网站标准标题样式 */
.ant-typography h1,
.ant-typography h2,
.ant-typography h3,
.ant-typography h4 {
  font-family: "Microsoft YaHei", "SimHei", sans-serif !important;
  color: #1f2937 !important;
}

/* 政府网站标准段落样式 */
.ant-typography {
  font-family: "Microsoft YaHei", "SimHei", sans-serif !important;
}


