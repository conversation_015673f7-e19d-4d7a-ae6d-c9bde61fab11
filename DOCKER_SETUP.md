# Docker开发环境设置指南

本指南将帮助您使用Docker快速启动项目所需的PostgreSQL和Redis服务。

## 📋 前提条件

1. **安装Docker Desktop**
   - Windows: 从 [Docker官网](https://www.docker.com/products/docker-desktop) 下载并安装
   - 确保Docker Desktop正在运行

2. **确保Docker Compose可用**
   - Docker Desktop通常包含Docker Compose
   - 验证安装: `docker-compose --version`

## 🚀 快速启动

### 方法一：使用管理脚本（推荐）

**Windows (PowerShell):**
```powershell
# 启动服务
.\docker-dev.ps1 up

# 查看状态
.\docker-dev.ps1 status

# 查看日志
.\docker-dev.ps1 logs

# 停止服务
.\docker-dev.ps1 down
```

**Linux/macOS (Bash):**
```bash
# 给脚本执行权限
chmod +x docker-dev.sh

# 启动服务
./docker-dev.sh up

# 查看状态
./docker-dev.sh status

# 查看日志
./docker-dev.sh logs

# 停止服务
./docker-dev.sh down
```

### 方法二：直接使用Docker Compose

```bash
# 启动服务（后台运行）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 📊 服务信息

启动成功后，以下服务将可用：

| 服务 | 地址 | 用户名 | 密码 | 说明 |
|------|------|--------|------|------|
| PostgreSQL | localhost:5432 | postgres | password | 主数据库（包含PostGIS） |
| Redis | localhost:6379 | - | - | 缓存服务 |
| pgAdmin | localhost:8080 | <EMAIL> | admin123 | 数据库管理工具（可选） |

## 🔧 配置说明

### PostgreSQL配置
- **数据库名**: marine_bio_platform
- **扩展**: PostGIS（地理信息系统）
- **数据持久化**: 使用Docker volume存储

### Redis配置
- **默认端口**: 6379
- **数据持久化**: 使用Docker volume存储

## 🔍 验证安装

### 1. 检查服务状态
```bash
# 查看所有容器
docker-compose ps

# 检查PostgreSQL连接
docker-compose exec postgres pg_isready -U postgres -d marine_bio_platform

# 检查Redis连接
docker-compose exec redis redis-cli ping
```

### 2. 连接数据库
```bash
# 进入PostgreSQL容器
docker-compose exec postgres psql -U postgres -d marine_bio_platform

# 验证PostGIS扩展
SELECT PostGIS_Version();
```

### 3. 测试Redis
```bash
# 进入Redis容器
docker-compose exec redis redis-cli

# 测试Redis命令
ping
set test "hello"
get test
```

## 🛠️ 故障排除

### 常见问题

1. **Docker Desktop未启动**
   ```
   错误: cannot connect to Docker daemon
   解决: 启动Docker Desktop应用程序
   ```

2. **端口冲突**
   ```
   错误: port is already allocated
   解决: 停止占用端口的服务，或修改docker-compose.yml中的端口映射
   ```

3. **权限问题（Linux/macOS）**
   ```bash
   # 给当前用户Docker权限
   sudo usermod -aG docker $USER
   # 重新登录或重启终端
   ```

### 重置环境

如果遇到问题需要完全重置：

```bash
# 停止并删除所有容器和数据
docker-compose down -v

# 清理Docker系统
docker system prune -f

# 重新启动
docker-compose up -d
```

## 📝 后端配置

确保后端的`.env`文件包含正确的数据库配置：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=marine_bio_platform

# Redis配置（如果使用）
REDIS_HOST=localhost
REDIS_PORT=6379
```

## 🔄 开发工作流

1. **启动开发环境**
   ```bash
   docker-compose up -d
   ```

2. **启动后端服务**
   ```bash
   cd backend
   pnpm install
   pnpm run dev
   ```

3. **启动前端服务**
   ```bash
   cd frontend
   pnpm install
   pnpm run dev
   ```

4. **停止开发环境**
   ```bash
   docker-compose down
   ```

## 📚 更多信息

- [Docker官方文档](https://docs.docker.com/)
- [PostgreSQL官方文档](https://www.postgresql.org/docs/)
- [PostGIS官方文档](https://postgis.net/documentation/)
- [Redis官方文档](https://redis.io/documentation)
