import { apiClient } from './config';
import type { LoginCredentials, LoginResult, User, ApiResponse } from '../../types';

export const authApi = {
  // 管理员登录
  login: async (credentials: LoginCredentials): Promise<LoginResult> => {
    const response = await apiClient.post<ApiResponse<LoginResult>>('/auth/login', credentials);
    return response.data.data!;
  },

  // 获取当前用户信息
  getProfile: async (): Promise<User> => {
    const response = await apiClient.get<ApiResponse<User>>('/auth/profile');
    return response.data.data!;
  },

  // 更新用户信息
  updateProfile: async (data: Partial<User>): Promise<User> => {
    const response = await apiClient.put<ApiResponse<User>>('/auth/profile', data);
    return response.data.data!;
  },

  // 修改密码
  changePassword: async (data: { currentPassword: string; newPassword: string }): Promise<void> => {
    await apiClient.post<ApiResponse>('/auth/change-password', data);
  },

  // 登出
  logout: async (): Promise<void> => {
    await apiClient.post<ApiResponse>('/auth/logout');
  },

  // 验证token
  verifyToken: async (): Promise<User> => {
    const response = await apiClient.get<ApiResponse<User>>('/auth/verify');
    return response.data.data!;
  },
};
