import React, { memo, useMemo } from 'react';
import { Typography } from 'antd';

const { Title } = Typography;

const LegendPanel: React.FC = memo(() => {
  // 使用useMemo缓存图例数据，避免每次重新创建
  const legendItems = useMemo(() => [
    { color: '#dc2626', label: '500+' },
    { color: '#ea580c', label: '400-500' },
    { color: '#f59e0b', label: '300-400' },
    { color: '#eab308', label: '250-300' },
    { color: '#84cc16', label: '200-250' },
    { color: '#22c55e', label: '150-200' },
    { color: '#06b6d4', label: '100-150' },
    { color: '#3b82f6', label: '50-100' },
    { color: '#8b5cf6', label: '15-50' },
    { color: '#a855f7', label: '0-15' },
  ], []);

  return (
    <div style={{
      padding: '16px',
      borderBottom: '1px solid #e5e7eb'
    }}>
      <Title level={5} style={{
        margin: '0 0 12px 0',
        color: '#1f2937',
        fontSize: '14px',
        fontWeight: '600',
        fontFamily: '"Microsoft YaHei", "SimHei", sans-serif'
      }}>
        观察频次分布
      </Title>
      <div style={{ fontSize: '12px', color: '#6b7280' }}>
        {legendItems.map((item, index) => (
          <div 
            key={item.label}
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              marginBottom: index === legendItems.length - 1 ? '0' : '6px' 
            }}
          >
            <div style={{
              width: '16px',
              height: '16px',
              background: item.color,
              marginRight: '8px',
              borderRadius: '2px'
            }}></div>
            <span>{item.label}</span>
          </div>
        ))}
      </div>
    </div>
  );
});

LegendPanel.displayName = 'LegendPanel';

export default LegendPanel;
