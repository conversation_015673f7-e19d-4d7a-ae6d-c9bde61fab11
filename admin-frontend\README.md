# 海洋生物声音平台 - 管理端

这是海洋生物声音平台的管理端应用，用于管理用户、物种数据、媒体文件等。

## 功能特性

- 🔐 管理员认证和权限管理
- 👥 用户管理（增删改查、权限分配）
- 🐋 物种数据管理（创建、编辑、删除物种信息）
- 📁 媒体文件管理（上传、管理音频、图片、视频文件）
- 📊 数据统计和仪表盘
- ⚙️ 系统设置和维护

## 技术栈

- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **状态管理**: Redux Toolkit
- **UI组件**: Ant Design
- **路由**: React Router v7
- **HTTP客户端**: Axios

## 开发环境

### 环境要求
- Node.js >= 18
- pnpm >= 8

### 安装依赖
```bash
cd admin-frontend
pnpm install
```

### 启动开发服务器
```bash
pnpm run dev
```

管理端应用将在 http://localhost:5174 启动

### 构建生产版本
```bash
pnpm run build
```

## 环境变量

复制 `.env.example` 到 `.env` 并根据需要修改配置：

```env
VITE_API_BASE_URL=http://localhost:3001/api
VITE_APP_NAME=海洋生物声音平台 - 管理端
VITE_APP_TYPE=admin
```

## 项目结构

```
src/
├── components/          # 组件
│   ├── Layout/         # 布局组件
│   ├── UserManagement/ # 用户管理组件
│   ├── SpeciesManagement/ # 物种管理组件
│   └── ...
├── pages/              # 页面组件
├── services/           # API服务
├── store/              # 状态管理
├── types/              # 类型定义
├── hooks/              # 自定义Hooks
├── utils/              # 工具函数
└── router/             # 路由配置
```

## 开发指南

### 代码规范
- 使用 ESLint 进行代码检查
- 使用 TypeScript 进行类型检查
- 遵循 React Hooks 最佳实践

### 测试
```bash
# 运行测试
pnpm run test

# 运行测试并监听文件变化
pnpm run test:watch

# 生成测试覆盖率报告
pnpm run test:coverage
```
