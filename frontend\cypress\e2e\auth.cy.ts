describe('用户认证流程', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  describe('登录功能', () => {
    it('应该成功登录用户', () => {
      // 点击登录链接
      cy.get('[data-testid="login-link"]').click();
      
      // 验证登录页面
      cy.url().should('include', '/login');
      cy.get('[data-testid="login-form"]').should('be.visible');
      cy.get('h1').should('contain', '海洋生物声音平台');
      
      // 填写登录表单
      cy.get('[data-testid="username-input"]').type('testuser');
      cy.get('[data-testid="password-input"]').type('password123');
      
      // 提交登录
      cy.get('[data-testid="login-button"]').click();
      
      // 验证登录成功
      cy.waitForApi('login');
      cy.url().should('eq', Cypress.config().baseUrl + '/');
      cy.get('[data-testid="user-menu"]').should('contain', 'testuser');
      cy.get('[data-testid="login-link"]').should('not.exist');
    });

    it('应该显示登录错误信息', () => {
      // 模拟登录失败
      cy.intercept('POST', '/api/auth/login', {
        statusCode: 401,
        body: {
          success: false,
          error: { message: '用户名或密码错误' }
        }
      }).as('loginError');

      cy.visit('/login');
      
      // 填写错误的登录信息
      cy.get('[data-testid="username-input"]').type('wronguser');
      cy.get('[data-testid="password-input"]').type('wrongpass');
      cy.get('[data-testid="login-button"]').click();
      
      // 验证错误信息
      cy.wait('@loginError');
      cy.get('[data-testid="error-message"]').should('contain', '用户名或密码错误');
      cy.url().should('include', '/login');
    });

    it('应该验证表单字段', () => {
      cy.visit('/login');
      
      // 测试空表单提交
      cy.get('[data-testid="login-button"]').click();
      cy.get('[data-testid="username-error"]').should('contain', '请输入用户名');
      cy.get('[data-testid="password-error"]').should('contain', '请输入密码');
      
      // 测试用户名长度验证
      cy.get('[data-testid="username-input"]').type('ab');
      cy.get('[data-testid="login-button"]').click();
      cy.get('[data-testid="username-error"]').should('contain', '用户名至少3个字符');
      
      // 测试密码长度验证
      cy.get('[data-testid="username-input"]').clear().type('testuser');
      cy.get('[data-testid="password-input"]').type('12345');
      cy.get('[data-testid="login-button"]').click();
      cy.get('[data-testid="password-error"]').should('contain', '密码至少6个字符');
    });

    it('应该支持键盘导航', () => {
      cy.visit('/login');
      
      // 测试Tab键导航
      cy.get('body').tab();
      cy.focused().should('have.attr', 'data-testid', 'username-input');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'password-input');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'login-button');
      
      // 测试Enter键提交
      cy.get('[data-testid="username-input"]').type('testuser');
      cy.get('[data-testid="password-input"]').type('password123{enter}');
      
      cy.waitForApi('login');
      cy.url().should('eq', Cypress.config().baseUrl + '/');
    });
  });

  describe('注册功能', () => {
    it('应该成功注册新用户', () => {
      cy.intercept('POST', '/api/auth/register', {
        statusCode: 201,
        body: {
          success: true,
          data: {
            id: '2',
            username: 'newuser',
            email: '<EMAIL>'
          }
        }
      }).as('register');

      // 导航到注册页面
      cy.get('[data-testid="register-link"]').click();
      cy.url().should('include', '/register');
      
      // 填写注册表单
      cy.get('[data-testid="username-input"]').type('newuser');
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="confirm-password-input"]').type('password123');
      
      // 提交注册
      cy.get('[data-testid="register-button"]').click();
      
      // 验证注册成功
      cy.wait('@register');
      cy.get('[data-testid="success-message"]').should('contain', '注册成功');
    });

    it('应该验证密码确认', () => {
      cy.visit('/register');
      
      // 填写不匹配的密码
      cy.get('[data-testid="username-input"]').type('newuser');
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="confirm-password-input"]').type('differentpassword');
      cy.get('[data-testid="register-button"]').click();
      
      // 验证错误信息
      cy.get('[data-testid="confirm-password-error"]').should('contain', '两次输入的密码不一致');
    });

    it('应该验证邮箱格式', () => {
      cy.visit('/register');
      
      cy.get('[data-testid="email-input"]').type('invalid-email');
      cy.get('[data-testid="register-button"]').click();
      
      cy.get('[data-testid="email-error"]').should('contain', '请输入有效的邮箱地址');
    });
  });

  describe('登出功能', () => {
    beforeEach(() => {
      // 预设登录状态
      cy.setAuthState(true);
      cy.visit('/');
    });

    it('应该成功登出用户', () => {
      // 点击用户菜单
      cy.get('[data-testid="user-menu"]').click();
      
      // 点击登出按钮
      cy.get('[data-testid="logout-button"]').click();
      
      // 验证登出成功
      cy.get('[data-testid="login-link"]').should('be.visible');
      cy.get('[data-testid="user-menu"]').should('not.exist');
      
      // 验证本地存储已清除
      cy.window().then((win) => {
        expect(win.localStorage.getItem('token')).to.be.null;
        expect(win.localStorage.getItem('user')).to.be.null;
      });
    });
  });

  describe('受保护路由', () => {
    it('应该重定向未认证用户', () => {
      // 尝试访问需要认证的页面
      cy.visit('/dashboard');
      
      // 应该重定向到登录页面
      cy.url().should('include', '/login');
      cy.get('[data-testid="login-required-message"]').should('contain', '请先登录');
    });

    it('应该允许已认证用户访问', () => {
      // 设置认证状态
      cy.setAuthState(true);
      
      // 访问受保护页面
      cy.visit('/dashboard');
      
      // 应该成功访问
      cy.url().should('include', '/dashboard');
      cy.get('[data-testid="dashboard-content"]').should('be.visible');
    });
  });

  describe('会话管理', () => {
    it('应该在页面刷新后保持登录状态', () => {
      // 登录
      cy.login('testuser', 'password123');
      
      // 刷新页面
      cy.reload();
      
      // 验证仍然登录
      cy.get('[data-testid="user-menu"]').should('contain', 'testuser');
    });

    it('应该处理token过期', () => {
      // 设置过期的token
      cy.window().then((win) => {
        win.localStorage.setItem('token', 'expired-token');
      });

      // 模拟token过期响应
      cy.intercept('GET', '/api/auth/profile', {
        statusCode: 401,
        body: {
          success: false,
          error: { message: 'Token已过期' }
        }
      }).as('tokenExpired');

      cy.visit('/');
      
      // 验证自动登出
      cy.wait('@tokenExpired');
      cy.get('[data-testid="login-link"]').should('be.visible');
      
      // 验证token已清除
      cy.window().then((win) => {
        expect(win.localStorage.getItem('token')).to.be.null;
      });
    });
  });

  describe('响应式设计', () => {
    it('应该在移动设备上正常工作', () => {
      cy.viewport('iphone-6');
      cy.visit('/login');
      
      // 验证移动端布局
      cy.get('[data-testid="login-form"]').should('be.visible');
      cy.get('[data-testid="username-input"]').should('be.visible');
      cy.get('[data-testid="password-input"]').should('be.visible');
      cy.get('[data-testid="login-button"]').should('be.visible');
      
      // 测试登录功能
      cy.get('[data-testid="username-input"]').type('testuser');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="login-button"]').click();
      
      cy.waitForApi('login');
      cy.url().should('eq', Cypress.config().baseUrl + '/');
    });
  });
});
