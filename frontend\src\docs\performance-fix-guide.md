# 性能问题修复指南

## 问题描述
React应用程序出现"性能日志已禁用：内存使用过高"错误，主要源于PerformanceProfiler组件的内存管理问题。

## 已实施的修复方案

### 1. 调整内存阈值和检查频率
**文件**: `frontend/src/utils/performanceUtils.ts`

**修改内容**:
- 内存阈值从150MB提高到300MB
- 日志频率从每秒5条减少到3条
- 总日志数量从500条减少到300条
- 内存检查间隔从10秒增加到15秒
- 警告频率从每200次减少到每500次

### 2. 优化PerformanceProfiler组件
**文件**: `frontend/src/components/Performance/PerformanceProfiler.tsx`

**修改内容**:
- 内存检查频率从每100次渲染改为每500次
- 内存检查间隔从10秒增加到20秒
- 自动禁用阈值从90%调整到85%
- 数据存储从保留5次减少到3次渲染数据
- 统计输出间隔从30秒增加到60秒
- 性能警告阈值从5ms提高到10ms

### 3. 改进内存使用检测
**文件**: `frontend/src/utils/performanceUtils.ts`

**修改内容**:
- 使用更准确的内存限制计算（jsHeapSizeLimit）
- 改进内存百分比计算逻辑
- 添加内存限制信息到警告消息

### 4. 优化HomePage性能监控策略
**文件**: `frontend/src/pages/HomePage.tsx`

**修改内容**:
- 将性能监控改为默认禁用，需要通过URL参数`enable-profiler=true`启用
- 集成内存管理器，在开发环境中监控内存使用
- 添加内存警告和严重警告的处理逻辑

### 5. 更新内存管理器配置
**文件**: `frontend/src/utils/memoryManager.ts`

**修改内容**:
- 警告阈值从85%降低到75%
- 严重阈值从95%降低到85%
- 检查间隔从60秒减少到30秒
- 历史记录从10个减少到5个
- 趋势敏感度从5%提高到3%

## 使用方法

### 启用性能监控
在开发环境中，如果需要启用性能监控，请在URL中添加参数：
```
http://localhost:3000/?enable-profiler=true
```

### 监控内存使用
内存管理器会在开发环境中自动启动，监控内存使用情况：
- 当内存使用超过75%时，会在控制台显示警告
- 当内存使用超过85%时，会显示严重警告

### 手动检查内存
```javascript
import { memoryManager } from '../utils/memoryManager';

// 获取当前内存信息
const memoryInfo = memoryManager.getCurrentMemoryInfo();
console.log('内存使用:', memoryInfo.percentage.toFixed(1) + '%');
```

## 预期效果

1. **减少内存压力**: 通过提高阈值和减少检查频率，降低性能监控本身的内存消耗
2. **更准确的内存检测**: 使用更精确的内存计算方法，减少误报
3. **更好的用户体验**: 默认禁用性能监控，避免影响正常开发
4. **智能内存管理**: 自动监控内存使用，及时发现潜在问题

## 故障排除

### 如果仍然出现内存问题
1. 检查是否有其他组件导致内存泄漏
2. 确认地图组件和其他大型组件的内存使用
3. 考虑进一步减少性能监控的频率
4. 检查是否有未清理的事件监听器或定时器

### 调试建议
1. 使用浏览器开发者工具的Memory标签页监控内存使用
2. 启用性能监控时，观察控制台的内存警告信息
3. 定期检查内存管理器的报告

## 后续优化建议

1. **组件级性能监控**: 将性能监控的粒度细化到具体组件，而不是整个页面
2. **懒加载优化**: 对大型组件实施懒加载，减少初始内存占用
3. **缓存策略**: 实施更智能的缓存清理策略
4. **生产环境监控**: 考虑在生产环境中实施轻量级的性能监控

## 相关文件
- `frontend/src/utils/performanceUtils.ts` - 性能工具和日志管理
- `frontend/src/components/Performance/PerformanceProfiler.tsx` - 性能监控组件
- `frontend/src/utils/memoryManager.ts` - 内存管理器
- `frontend/src/pages/HomePage.tsx` - 主页组件
- `frontend/src/docs/memory-optimization-guide.md` - 内存优化指南
