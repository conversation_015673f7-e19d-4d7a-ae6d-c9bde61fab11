# 海洋生物声音平台 (Marine Bio Sound Platform)

专业的海洋生物发声数据管理和展示平台，通过交互式地图和多媒体页面向科研人员、教育工作者及公众展示海洋生物声音数据。

## 项目结构

```
mbdp/
├── frontend/          # React + TypeScript 前端应用
├── backend/           # Node.js + Express 后端服务
├── .kiro/            # 项目规格文档
└── README.md
```

## 技术栈

### 前端
- **框架**: React 19 + TypeScript
- **构建工具**: Vite
- **状态管理**: Redux Toolkit
- **UI组件**: Ant Design
- **地图**: Leaflet + React-Leaflet
- **HTTP客户端**: Axios

### 后端
- **运行时**: Node.js
- **框架**: Express 4
- **语言**: TypeScript
- **数据库**: PostgreSQL + PostGIS
- **ORM**: TypeORM
- **认证**: JWT + RBAC

## 快速开始

### 环境要求
- Node.js >= 18
- pnpm >= 8
- PostgreSQL >= 13 (带PostGIS扩展)

### 安装依赖

```bash
# 安装前端依赖
cd frontend
pnpm install

# 安装后端依赖
cd ../backend
pnpm install
```

### 环境配置

1. 复制环境变量文件：
```bash
# 前端
cp frontend/.env.example frontend/.env

# 后端
cp backend/.env.example backend/.env
```

2. 配置数据库连接（编辑 `backend/.env`）：
```env
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_DATABASE=marine_bio_platform
```

### 启动开发服务器

```bash
# 启动后端服务 (端口 3001)
cd backend
pnpm run dev

# 启动前端服务 (端口 5173)
cd frontend
pnpm run dev
```

## 主要功能

### 用户端
- 🗺️ **交互式全球地图**: 展示物种分布和数据采集点
- 🔍 **智能搜索**: 实时搜索物种信息
- 📱 **物种详情页**: 类似维基百科的详细信息展示
- 🎵 **音频播放器**: 集成时域图和时频图的音频分析
- 🖼️ **媒体画廊**: 图片和视频资源展示

### 管理端
- 👥 **用户权限管理**: RBAC权限模型
- 🐋 **物种数据管理**: 完整的物种信息管理
- 📁 **文件管理**: 音频、图片、视频文件管理
- 🎯 **音频识别**: AI模型集成的物种识别功能
- 📊 **系统监控**: 操作日志和系统状态监控

## 开发指南

### 代码规范
项目使用ESLint + Prettier进行代码规范化：

```bash
# 前端代码检查
cd frontend
pnpm run lint

# 后端代码检查
cd backend
pnpm run lint
pnpm run format
```

### 测试

项目包含完整的测试体系，确保代码质量和功能稳定性：

#### 前端测试
```bash
cd frontend

# 单元测试
pnpm test

# 监听模式运行测试
pnpm test:watch

# 生成覆盖率报告
pnpm test:coverage

# E2E测试
pnpm test:e2e

# 打开Cypress测试界面
pnpm test:e2e:open
```

#### 后端测试
```bash
cd backend

# 单元测试
pnpm test

# 监听模式运行测试
pnpm test:watch

# 生成覆盖率报告
pnpm test:coverage

# CI环境测试
pnpm test:ci
```

#### 测试覆盖率
- **目标覆盖率**: 75%
- **前端覆盖率**: 包含组件、Redux状态管理、工具函数
- **后端覆盖率**: 包含API端点、数据库操作、业务逻辑
- **E2E覆盖率**: 覆盖关键用户流程

### CI/CD
项目使用GitHub Actions进行持续集成：

- **自动化测试**: 每次提交和PR都会运行完整测试套件
- **代码覆盖率**: 自动生成并上传到Codecov
- **代码质量检查**: ESLint、TypeScript类型检查
- **安全扫描**: 依赖漏洞检查
- **构建验证**: 确保前后端都能正确构建

### 项目文档
详细的项目规格和设计文档位于 `.kiro/specs/marine-bio-sound-platform/` 目录：
- `requirements.md` - 需求文档
- `design.md` - 设计文档
- `tasks.md` - 实施计划

## 许可证

ISC License

## 贡献

欢迎提交Issue和Pull Request来改进项目。
