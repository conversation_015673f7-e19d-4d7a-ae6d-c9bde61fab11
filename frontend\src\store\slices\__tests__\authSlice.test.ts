import { configureStore } from '@reduxjs/toolkit';
import authSlice, {
  login,
  register,
  fetchUserProfile,
  logout,
  clearError,
  setToken,
} from '../authSlice';
import { server } from '../../../__mocks__/server';
import { rest } from 'msw';

// 创建测试store
const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authSlice,
    },
  });
};

describe('authSlice', () => {
  let store: ReturnType<typeof createTestStore>;

  beforeEach(() => {
    store = createTestStore();
    localStorage.clear();
  });

  describe('初始状态', () => {
    it('应该返回正确的初始状态', () => {
      const state = store.getState().auth;
      expect(state).toEqual({
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
        error: null,
      });
    });
  });

  describe('同步actions', () => {
    it('应该处理logout action', () => {
      // 设置初始状态
      store.dispatch(setToken('test-token'));
      
      // 执行logout
      store.dispatch(logout());
      
      const state = store.getState().auth;
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBeNull();
      expect(localStorage.getItem('token')).toBeNull();
    });

    it('应该处理clearError action', () => {
      // 先设置一个错误状态
      store.dispatch({ type: 'auth/login/rejected', payload: 'Test error' });
      
      // 清除错误
      store.dispatch(clearError());
      
      const state = store.getState().auth;
      expect(state.error).toBeNull();
    });

    it('应该处理setToken action', () => {
      const token = 'test-token';
      store.dispatch(setToken(token));
      
      const state = store.getState().auth;
      expect(state.token).toBe(token);
      expect(localStorage.getItem('token')).toBe(token);
    });
  });

  describe('异步actions', () => {
    describe('login', () => {
      it('应该成功处理登录', async () => {
        const credentials = { username: 'testuser', password: 'password' };
        
        const result = await store.dispatch(login(credentials));
        
        expect(result.type).toBe('auth/login/fulfilled');
        
        const state = store.getState().auth;
        expect(state.loading).toBe(false);
        expect(state.isAuthenticated).toBe(true);
        expect(state.user).toBeTruthy();
        expect(state.user?.username).toBe('testuser');
        expect(state.token).toBe('mock-jwt-token');
        expect(state.error).toBeNull();
      });

      it('应该处理登录失败', async () => {
        // 模拟登录失败
        server.use(
          rest.post('http://localhost:3001/api/auth/login', (req, res, ctx) => {
            return res(
              ctx.status(401),
              ctx.json({
                success: false,
                error: { message: '用户名或密码错误' }
              })
            );
          })
        );

        const credentials = { username: 'wronguser', password: 'wrongpass' };
        
        const result = await store.dispatch(login(credentials));
        
        expect(result.type).toBe('auth/login/rejected');
        
        const state = store.getState().auth;
        expect(state.loading).toBe(false);
        expect(state.isAuthenticated).toBe(false);
        expect(state.user).toBeNull();
        expect(state.token).toBeNull();
        expect(state.error).toBe('用户名或密码错误');
      });

      it('应该处理网络错误', async () => {
        // 模拟网络错误
        server.use(
          rest.post('http://localhost:3001/api/auth/login', (req, res, ctx) => {
            return res.networkError('Network error');
          })
        );

        const credentials = { username: 'testuser', password: 'password' };
        
        const result = await store.dispatch(login(credentials));
        
        expect(result.type).toBe('auth/login/rejected');
        
        const state = store.getState().auth;
        expect(state.error).toBe('网络错误，请稍后重试');
      });
    });

    describe('register', () => {
      it('应该成功处理注册', async () => {
        const userData = {
          username: 'newuser',
          email: '<EMAIL>',
          password: 'password123'
        };
        
        const result = await store.dispatch(register(userData));
        
        expect(result.type).toBe('auth/register/fulfilled');
        
        const state = store.getState().auth;
        expect(state.loading).toBe(false);
        expect(state.error).toBeNull();
      });

      it('应该处理注册失败', async () => {
        // 模拟注册失败
        server.use(
          rest.post('http://localhost:3001/api/auth/register', (req, res, ctx) => {
            return res(
              ctx.status(400),
              ctx.json({
                success: false,
                error: { message: '用户名已存在' }
              })
            );
          })
        );

        const userData = {
          username: 'existinguser',
          email: '<EMAIL>',
          password: 'password123'
        };
        
        const result = await store.dispatch(register(userData));
        
        expect(result.type).toBe('auth/register/rejected');
        
        const state = store.getState().auth;
        expect(state.error).toBe('用户名已存在');
      });
    });

    describe('fetchUserProfile', () => {
      it('应该成功获取用户信息', async () => {
        // 先设置token
        store.dispatch(setToken('mock-jwt-token'));
        
        const result = await store.dispatch(fetchUserProfile());
        
        expect(result.type).toBe('auth/fetchUserProfile/fulfilled');
        
        const state = store.getState().auth;
        expect(state.loading).toBe(false);
        expect(state.isAuthenticated).toBe(true);
        expect(state.user).toBeTruthy();
        expect(state.error).toBeNull();
      });

      it('应该处理未授权访问', async () => {
        // 不设置token
        const result = await store.dispatch(fetchUserProfile());
        
        expect(result.type).toBe('auth/fetchUserProfile/rejected');
        
        const state = store.getState().auth;
        expect(state.error).toBe('未找到访问令牌');
      });

      it('应该处理token过期', async () => {
        // 模拟token过期
        server.use(
          rest.get('http://localhost:3001/api/auth/profile', (req, res, ctx) => {
            return res(
              ctx.status(401),
              ctx.json({
                success: false,
                error: { message: 'Token已过期' }
              })
            );
          })
        );

        store.dispatch(setToken('expired-token'));
        
        const result = await store.dispatch(fetchUserProfile());
        
        expect(result.type).toBe('auth/fetchUserProfile/rejected');
        
        const state = store.getState().auth;
        expect(state.isAuthenticated).toBe(false);
        expect(state.token).toBeNull();
        expect(localStorage.getItem('token')).toBeNull();
      });
    });
  });

  describe('loading状态', () => {
    it('应该在异步操作期间设置loading状态', async () => {
      const credentials = { username: 'testuser', password: 'password' };
      
      // 开始登录
      const loginPromise = store.dispatch(login(credentials));
      
      // 检查loading状态
      let state = store.getState().auth;
      expect(state.loading).toBe(true);
      
      // 等待完成
      await loginPromise;
      
      // 检查loading状态已重置
      state = store.getState().auth;
      expect(state.loading).toBe(false);
    });
  });
});
