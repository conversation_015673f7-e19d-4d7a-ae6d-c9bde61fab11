// 用户相关类型
export interface User {
  id: string;
  username: string;
  email?: string;
  roles: Role[];
  status: 'active' | 'disabled';
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions?: Record<string, any>;
}

// 认证相关类型
export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResult {
  user: User;
  token: string;
  expiresIn: string;
}

// 物种相关类型
export interface TaxonomicClassification {
  kingdom?: string;
  phylum?: string;
  class?: string;
  order?: string;
  family?: string;
  genus?: string;
  species?: string;
}

export interface Species {
  id: string;
  chineseName: string;
  englishName?: string;
  latinName: string;
  conservationStatus?: string;
  description?: string;
  representativeImagePath?: string;
  classification?: TaxonomicClassification;
  createdAt: string;
  updatedAt: string;
}

export interface SpeciesSearchParams {
  keyword?: string;
  conservationStatus?: string;
  kingdom?: string;
  phylum?: string;
  class?: string;
  order?: string;
  family?: string;
  genus?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

// 媒体文件相关类型
export interface MediaFile {
  id: string;
  fileName: string;
  filePath: string;
  fileType: 'image' | 'video' | 'audio';
  fileSize: number;
  mimeType: string;
  title?: string;
  description?: string;
  isRepresentative?: boolean;
  speciesId: string;
  createdAt: string;
  updatedAt: string;
}

export interface AudioFile extends MediaFile {
  recordingLocation?: string;
  recordingTime?: string;
  behaviorDescription?: string;
  latitude?: number;
  longitude?: number;
  duration?: number;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
  };
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// 统计相关类型
export interface TaxonomyStats {
  [key: string]: number;
}

export interface ConservationStats {
  status: string;
  count: number;
  percentage: number;
}

// 系统管理相关类型
export interface SystemStats {
  totalUsers: number;
  totalSpecies: number;
  totalMediaFiles: number;
  storageUsed: number;
  storageLimit: number;
}

export interface FileStats {
  totalFiles: number;
  totalSize: number;
  imageCount: number;
  videoCount: number;
  audioCount: number;
}

// 表单相关类型
export interface UserFormData {
  username: string;
  email?: string;
  password?: string;
  roleIds: string[];
  status: 'active' | 'disabled';
}

export interface SpeciesFormData {
  chineseName: string;
  englishName?: string;
  latinName: string;
  conservationStatus?: string;
  description?: string;
  classification?: TaxonomicClassification;
}
