import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';
import { server } from './__mocks__/server';

// 配置 React Testing Library
configure({
  testIdAttribute: 'data-testid',
});

// 设置 MSW (Mock Service Worker)
beforeAll(() => {
  // 启动模拟服务器
  server.listen({
    onUnhandledRequest: 'warn',
  });
});

afterEach(() => {
  // 重置处理器
  server.resetHandlers();
});

afterAll(() => {
  // 关闭模拟服务器
  server.close();
});

// 模拟环境变量
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// 模拟 ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// 模拟 IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// 模拟 localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// 模拟 sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// 模拟 URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mocked-url');
global.URL.revokeObjectURL = jest.fn();

// 模拟 fetch
global.fetch = jest.fn();

// 模拟 console 方法以减少测试输出噪音
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

// 设置测试环境变量
process.env.VITE_API_BASE_URL = 'http://localhost:3001/api';
process.env.VITE_STATIC_FILES_URL = 'http://localhost:3001/files';
