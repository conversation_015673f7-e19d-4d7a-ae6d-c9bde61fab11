import React from 'react';
import { createBrowserRouter, RouterProvider, Navigate } from 'react-router-dom';
import AdminLayout from '../components/Layout/AdminLayout';
import LoginPage from '../pages/LoginPage';
import DashboardPage from '../pages/DashboardPage';
import UserListPage from '../pages/UserManagement/UserListPage';
import UserEditPage from '../pages/UserManagement/UserEditPage';
import RoleManagementPage from '../pages/UserManagement/RoleManagementPage';
import SpeciesListPage from '../pages/SpeciesManagement/SpeciesListPage';
import SpeciesEditPage from '../pages/SpeciesManagement/SpeciesEditPage';
import SpeciesCreatePage from '../pages/SpeciesManagement/SpeciesCreatePage';
import MediaManagementPage from '../pages/MediaManagement/MediaManagementPage';
import SystemSettingsPage from '../pages/SystemSettings/SystemSettingsPage';

// 路由保护组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const token = localStorage.getItem('token');
  
  if (!token) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
};

const router = createBrowserRouter([
  {
    path: '/login',
    element: <LoginPage />,
  },
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <AdminLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="/dashboard" replace />,
      },
      {
        path: 'dashboard',
        element: <DashboardPage />,
      },
      {
        path: 'users',
        children: [
          {
            index: true,
            element: <UserListPage />,
          },
          {
            path: 'create',
            element: <UserEditPage />,
          },
          {
            path: ':id/edit',
            element: <UserEditPage />,
          },
          {
            path: 'roles',
            element: <RoleManagementPage />,
          },
        ],
      },
      {
        path: 'species',
        children: [
          {
            index: true,
            element: <SpeciesListPage />,
          },
          {
            path: 'create',
            element: <SpeciesCreatePage />,
          },
          {
            path: ':id/edit',
            element: <SpeciesEditPage />,
          },
        ],
      },
      {
        path: 'media',
        element: <MediaManagementPage />,
      },
      {
        path: 'settings',
        element: <SystemSettingsPage />,
      },
    ],
  },
]);

const AppRouter: React.FC = () => {
  return <RouterProvider router={router} />;
};

export default AppRouter;
