import { defineConfig } from 'cypress';

export default defineConfig({
  e2e: {
    // 基础URL
    baseUrl: 'http://localhost:5173',
    
    // 测试文件位置
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    
    // 支持文件位置
    supportFile: 'cypress/support/e2e.ts',
    
    // 视频录制
    video: true,
    videosFolder: 'cypress/videos',
    
    // 截图
    screenshotsFolder: 'cypress/screenshots',
    
    // 视窗大小
    viewportWidth: 1280,
    viewportHeight: 720,
    
    // 默认命令超时
    defaultCommandTimeout: 10000,
    
    // 页面加载超时
    pageLoadTimeout: 30000,
    
    // 请求超时
    requestTimeout: 10000,
    
    // 响应超时
    responseTimeout: 30000,
    
    // 测试隔离
    testIsolation: true,
    
    // 实验性功能
    experimentalStudio: true,
    
    setupNodeEvents(on, config) {
      // 任务注册
      on('task', {
        log(message) {
          console.log(message);
          return null;
        },
        
        // 清理测试数据
        clearTestData() {
          // 这里可以添加清理测试数据的逻辑
          return null;
        },
        
        // 创建测试数据
        seedTestData() {
          // 这里可以添加创建测试数据的逻辑
          return null;
        },
      });

      // 环境变量配置
      config.env = {
        ...config.env,
        API_BASE_URL: 'http://localhost:3001/api',
        STATIC_FILES_URL: 'http://localhost:3001/files',
      };

      return config;
    },
  },

  component: {
    devServer: {
      framework: 'react',
      bundler: 'vite',
    },
    specPattern: 'src/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/component.ts',
  },

  // 全局配置
  retries: {
    runMode: 2,
    openMode: 0,
  },
  
  // 环境变量
  env: {
    coverage: true,
  },
});
