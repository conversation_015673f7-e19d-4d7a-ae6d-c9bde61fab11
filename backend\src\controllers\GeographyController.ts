import { Request, Response } from 'express';
import { GeographyService } from '../services/GeographyService';
import multer from 'multer';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// 配置KML文件上传
const kmlStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '../../uploads/kml'));
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueName);
  },
});

const kmlUpload = multer({
  storage: kmlStorage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/vnd.google-earth.kml+xml' || 
        file.originalname.toLowerCase().endsWith('.kml')) {
      cb(null, true);
    } else {
      cb(new Error('只支持KML文件格式'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
});

export class GeographyController {
  private geographyService: GeographyService;

  constructor() {
    this.geographyService = new GeographyService();
  }

  /**
   * 上传KML文件
   */
  uploadKML = async (req: Request, res: Response) => {
    try {
      const { speciesId } = req.params;
      
      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'NO_FILE',
            message: '请选择KML文件',
          },
        });
      }

      const distributionRanges = await this.geographyService.uploadKMLFile(
        speciesId,
        req.file.path,
        req.file.originalname
      );

      res.json({
        success: true,
        data: {
          distributionRanges,
          message: `成功处理${distributionRanges.length}个分布范围`,
        },
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'KML_UPLOAD_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取物种分布数据
   */
  getSpeciesDistribution = async (req: Request, res: Response) => {
    try {
      const { speciesId } = req.params;
      
      const distributions = await this.geographyService.getSpeciesDistribution(speciesId);

      res.json({
        success: true,
        data: distributions,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'GET_DISTRIBUTION_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取所有分布数据（用于地图展示）
   */
  getAllDistributions = async (req: Request, res: Response) => {
    try {
      const distributions = await this.geographyService.getAllDistributions();

      res.json({
        success: true,
        data: distributions,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'GET_ALL_DISTRIBUTIONS_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 根据位置查找物种
   */
  findSpeciesByLocation = async (req: Request, res: Response) => {
    try {
      const { longitude, latitude } = req.query;

      if (!longitude || !latitude) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_COORDINATES',
            message: '缺少经纬度参数',
          },
        });
      }

      const lng = parseFloat(longitude as string);
      const lat = parseFloat(latitude as string);

      if (!this.geographyService.validateCoordinates(lng, lat)) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_COORDINATES',
            message: '无效的经纬度坐标',
          },
        });
      }

      const species = await this.geographyService.findSpeciesByLocation(lng, lat);

      res.json({
        success: true,
        data: species,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'FIND_SPECIES_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取音频采集点
   */
  getAudioCollectionPoints = async (req: Request, res: Response) => {
    try {
      const { minLng, minLat, maxLng, maxLat } = req.query;

      let points;
      if (minLng && minLat && maxLng && maxLat) {
        // 获取指定区域内的采集点
        points = await this.geographyService.getAudioCollectionPointsInBounds(
          parseFloat(minLng as string),
          parseFloat(minLat as string),
          parseFloat(maxLng as string),
          parseFloat(maxLat as string)
        );
      } else {
        // 获取所有采集点
        points = await this.geographyService.getAudioCollectionPoints();
      }

      res.json({
        success: true,
        data: points,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'GET_COLLECTION_POINTS_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 删除分布范围
   */
  deleteDistributionRange = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      await this.geographyService.deleteDistributionRange(id);

      res.json({
        success: true,
        message: '分布范围删除成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'DELETE_DISTRIBUTION_ERROR',
          message: error.message,
        },
      });
    }
  };

  // Multer中间件
  getKMLUploadMiddleware() {
    return kmlUpload.single('kmlFile');
  }
}
