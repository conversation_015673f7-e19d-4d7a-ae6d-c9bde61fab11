import React from 'react';
import { Card, Radio, Space, Button, Tooltip, Typography } from 'antd';
import { 
  GlobalOutlined, 
  EnvironmentOutlined, 
  FullscreenOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  BarsOutlined
} from '@ant-design/icons';

const { Text } = Typography;

interface MapControlsProps {
  mode: 'distribution' | 'collection';
  onModeChange: (mode: 'distribution' | 'collection') => void;
  onReset?: () => void;
  onFullscreen?: () => void;
  onShowLegend?: () => void;
  distributionCount?: number;
  collectionCount?: number;
}

const MapControls: React.FC<MapControlsProps> = ({
  mode,
  onModeChange,
  onReset,
  onFullscreen,
  onShowLegend,
  distributionCount = 0,
  collectionCount = 0,
}) => {
  return (
    <Card size="small" style={{ marginBottom: '16px' }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: '12px'
      }}>
        {/* 模式切换 */}
        <div>
          <Radio.Group 
            value={mode} 
            onChange={(e) => onModeChange(e.target.value)}
            size="small"
          >
            <Radio.Button value="distribution">
              <Space>
                <GlobalOutlined />
                <span>物种分布</span>
                {distributionCount > 0 && (
                  <Text type="secondary">({distributionCount})</Text>
                )}
              </Space>
            </Radio.Button>
            <Radio.Button value="collection">
              <Space>
                <EnvironmentOutlined />
                <span>采集点</span>
                {collectionCount > 0 && (
                  <Text type="secondary">({collectionCount})</Text>
                )}
              </Space>
            </Radio.Button>
          </Radio.Group>
        </div>

        {/* 控制按钮 */}
        <Space>
          <Tooltip title="图例说明">
            <Button 
              size="small" 
              icon={<InfoCircleOutlined />}
              onClick={onShowLegend}
            >
              图例
            </Button>
          </Tooltip>
          
          <Tooltip title="重置视图">
            <Button 
              size="small" 
              icon={<ReloadOutlined />}
              onClick={onReset}
            >
              重置
            </Button>
          </Tooltip>
          
          <Tooltip title="全屏显示">
            <Button 
              size="small" 
              icon={<FullscreenOutlined />}
              onClick={onFullscreen}
            >
              全屏
            </Button>
          </Tooltip>
        </Space>
      </div>

      {/* 模式说明 */}
      <div style={{ marginTop: '8px' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          {mode === 'distribution' 
            ? '显示海洋生物的地理分布范围，点击区域查看物种详情'
            : '显示声音数据采集点位置，圆圈大小表示音频文件数量'
          }
        </Text>
      </div>
    </Card>
  );
};

// 图例组件
export const MapLegend: React.FC<{
  mode: 'distribution' | 'collection';
  visible: boolean;
  onClose: () => void;
}> = ({ mode, visible, onClose }) => {
  if (!visible) return null;

  return (
    <Card
      title={
        <Space>
          <BarsOutlined />
          图例说明
        </Space>
      }
      size="small"
      style={{
        position: 'absolute',
        top: '80px',
        right: '16px',
        width: '250px',
        zIndex: 1000,
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      }}
      extra={
        <Button type="text" size="small" onClick={onClose}>
          ×
        </Button>
      }
    >
      {mode === 'distribution' ? (
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              width: '20px',
              height: '20px',
              backgroundColor: '#1890ff',
              opacity: 0.3,
              border: '2px solid #096dd9',
              marginRight: '8px'
            }} />
            <Text style={{ fontSize: '12px' }}>物种分布范围</Text>
          </div>
          <Text type="secondary" style={{ fontSize: '11px' }}>
            • 蓝色区域表示物种的地理分布范围<br/>
            • 点击区域可查看物种详细信息<br/>
            • 鼠标悬停可高亮显示区域
          </Text>
        </Space>
      ) : (
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
            <Text strong style={{ fontSize: '12px' }}>采集点标记</Text>
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
            <div style={{
              width: '20px',
              height: '20px',
              backgroundColor: '#52c41a',
              borderRadius: '50%',
              border: '2px solid white',
              marginRight: '8px',
              fontSize: '10px',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>1-5</div>
            <Text style={{ fontSize: '12px' }}>少量音频文件</Text>
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
            <div style={{
              width: '25px',
              height: '25px',
              backgroundColor: '#fa8c16',
              borderRadius: '50%',
              border: '2px solid white',
              marginRight: '8px',
              fontSize: '10px',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>6-10</div>
            <Text style={{ fontSize: '12px' }}>中等数量音频文件</Text>
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
            <div style={{
              width: '30px',
              height: '30px',
              backgroundColor: '#ff4d4f',
              borderRadius: '50%',
              border: '2px solid white',
              marginRight: '8px',
              fontSize: '10px',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>10+</div>
            <Text style={{ fontSize: '12px' }}>大量音频文件</Text>
          </div>
          
          <Text type="secondary" style={{ fontSize: '11px' }}>
            • 圆圈大小和颜色表示音频文件数量<br/>
            • 点击标记可查看详细信息<br/>
            • 数字显示该点的音频文件总数
          </Text>
        </Space>
      )}
    </Card>
  );
};

export default MapControls;
