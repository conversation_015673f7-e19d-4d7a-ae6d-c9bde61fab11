import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { IsNotEmpty, IsIn } from 'class-validator';
import { Species } from './Species';

@Entity('media_files')
export class MediaFile {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty({ message: '文件类型不能为空' })
  @IsIn(['image', 'video'], { message: '文件类型必须是image或video' })
  fileType: 'image' | 'video';

  @Column()
  @IsNotEmpty({ message: '文件路径不能为空' })
  filePath: string;

  @Column()
  @IsNotEmpty({ message: '文件名不能为空' })
  fileName: string;

  @Column('bigint', { nullable: true })
  fileSize?: number;

  @Column({ nullable: true })
  mimeType?: string;

  @Column({ nullable: true })
  title?: string;

  @Column('text', { nullable: true })
  description?: string;

  @Column({ default: false })
  isRepresentative: boolean; // 是否为代表性图片

  @ManyToOne(() => Species, species => species.mediaFiles, {
    onDelete: 'CASCADE'
  })
  @JoinColumn({ name: 'species_id' })
  species: Species;

  @CreateDateColumn()
  createdAt: Date;
}
