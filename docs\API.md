# API 文档

海洋生物声音平台 RESTful API 接口文档。

## 🔗 基础信息

- **Base URL**: `http://localhost:3001/api`
- **API版本**: v1
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON

## 🔐 认证

### 获取访问令牌

**POST** `/auth/login`

```json
{
  "username": "string",
  "password": "string"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "roles": ["string"]
    }
  }
}
```

### 使用令牌
在请求头中添加：
```
Authorization: Bearer <token>
```

## 🐋 物种管理

### 搜索物种

**GET** `/species/search`

**查询参数**:
- `keyword` (string): 搜索关键词
- `conservationStatus` (string): 保护状态
- `kingdom` (string): 界
- `phylum` (string): 门
- `class` (string): 纲
- `order` (string): 目
- `family` (string): 科
- `genus` (string): 属
- `page` (number): 页码，默认1
- `limit` (number): 每页数量，默认20
- `sortBy` (string): 排序字段
- `sortOrder` (string): 排序方向 (ASC/DESC)

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "string",
        "chineseName": "蓝鲸",
        "englishName": "Blue Whale",
        "latinName": "Balaenoptera musculus",
        "conservationStatus": "濒危",
        "description": "string",
        "representativeImagePath": "string",
        "classification": {
          "kingdom": "动物界",
          "phylum": "脊索动物门",
          "class": "哺乳纲",
          "order": "鲸目",
          "family": "须鲸科",
          "genus": "蓝鲸属",
          "species": "蓝鲸"
        },
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 20,
    "totalPages": 5
  }
}
```

### 获取物种详情

**GET** `/species/{id}/details`

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "string",
    "chineseName": "蓝鲸",
    "englishName": "Blue Whale",
    "latinName": "Balaenoptera musculus",
    "conservationStatus": "濒危",
    "description": "string",
    "classification": {
      "kingdom": "动物界",
      "phylum": "脊索动物门",
      "class": "哺乳纲",
      "order": "鲸目",
      "family": "须鲸科",
      "genus": "蓝鲸属",
      "species": "蓝鲸"
    },
    "audioFiles": [
      {
        "id": "string",
        "fileName": "blue-whale-song.wav",
        "filePath": "uploads/audio/blue-whale-song.wav",
        "fileSize": 2048000,
        "mimeType": "audio/wav",
        "recordingLocation": "太平洋",
        "recordingTime": "2024-01-01T10:00:00.000Z",
        "behaviorDescription": "觅食",
        "duration": 120,
        "url": "string"
      }
    ],
    "imageFiles": [
      {
        "id": "string",
        "fileName": "blue-whale.jpg",
        "filePath": "uploads/images/blue-whale.jpg",
        "fileSize": 1024000,
        "mimeType": "image/jpeg",
        "description": "蓝鲸游泳照片",
        "imageType": "representative",
        "photographer": "摄影师",
        "url": "string"
      }
    ],
    "distributions": [
      {
        "id": "string",
        "region": "太平洋",
        "coordinates": {
          "type": "Polygon",
          "coordinates": [[[longitude, latitude]]]
        },
        "season": "全年",
        "abundance": "常见"
      }
    ],
    "statistics": {
      "audioFileCount": 10,
      "imageFileCount": 5,
      "totalRecordingDuration": 1200
    }
  }
}
```

### 创建物种

**POST** `/species` 🔒

**请求体**:
```json
{
  "chineseName": "座头鲸",
  "englishName": "Humpback Whale",
  "latinName": "Megaptera novaeangliae",
  "conservationStatus": "无危",
  "description": "string",
  "classification": {
    "kingdom": "动物界",
    "phylum": "脊索动物门",
    "class": "哺乳纲",
    "order": "鲸目",
    "family": "须鲸科",
    "genus": "座头鲸属",
    "species": "座头鲸"
  }
}
```

### 更新物种

**PUT** `/species/{id}` 🔒

### 删除物种

**DELETE** `/species/{id}` 🔒

## 📁 文件管理

### 上传文件

**POST** `/files/upload`

**Content-Type**: `multipart/form-data`

**表单字段**:
- `file`: 文件数据

**响应**:
```json
{
  "success": true,
  "data": {
    "originalName": "audio-file.wav",
    "fileName": "1640995200000-audio-file.wav",
    "path": "uploads/1640995200000-audio-file.wav",
    "size": 1024000,
    "mimeType": "audio/wav",
    "url": "http://localhost:3001/files/uploads/1640995200000-audio-file.wav"
  }
}
```

### 上传音频文件

**POST** `/files/upload/audio` 🔒

### 上传图片文件

**POST** `/files/upload/image` 🔒

### 创建音频文件记录

**POST** `/audio-files` 🔒

**请求体**:
```json
{
  "speciesId": "string",
  "fileName": "blue-whale-song.wav",
  "filePath": "uploads/audio/blue-whale-song.wav",
  "fileSize": 2048000,
  "mimeType": "audio/wav",
  "recordingLocation": "太平洋",
  "recordingTime": "2024-01-01T10:00:00.000Z",
  "behaviorDescription": "觅食"
}
```

### 获取文件列表

**GET** `/files` 🔒

**查询参数**:
- `fileType` (string): 文件类型 (audio/image/video)
- `speciesId` (string): 物种ID
- `page` (number): 页码
- `limit` (number): 每页数量

### 删除文件

**DELETE** `/files/{id}` 🔒

## 🗺️ 地理数据

### 获取物种分布

**GET** `/geography/distributions`

**查询参数**:
- `speciesId` (string): 物种ID
- `region` (string): 地区

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "speciesId": "string",
      "region": "太平洋",
      "coordinates": {
        "type": "Polygon",
        "coordinates": [[[longitude, latitude]]]
      },
      "season": "全年",
      "abundance": "常见",
      "species": {
        "id": "string",
        "chineseName": "蓝鲸",
        "latinName": "Balaenoptera musculus"
      }
    }
  ]
}
```

### 获取采集点

**GET** `/geography/collection-points`

**查询参数**:
- `speciesId` (string): 物种ID
- `bounds` (string): 地图边界 "sw_lat,sw_lng,ne_lat,ne_lng"

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "太平洋观测点1",
      "coordinates": {
        "type": "Point",
        "coordinates": [longitude, latitude]
      },
      "recordingCount": 15,
      "lastRecording": "2024-01-01T10:00:00.000Z",
      "species": [
        {
          "id": "string",
          "chineseName": "蓝鲸",
          "latinName": "Balaenoptera musculus"
        }
      ]
    }
  ]
}
```

## 📊 统计数据

### 分类统计

**GET** `/species/stats/taxonomy`

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "class": "哺乳纲",
      "count": 25
    },
    {
      "class": "硬骨鱼纲",
      "count": 150
    }
  ]
}
```

### 保护状态统计

**GET** `/species/stats/conservation`

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "status": "濒危",
      "count": 10
    },
    {
      "status": "无危",
      "count": 50
    }
  ]
}
```

## 🚨 错误处理

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "message": "错误描述",
    "code": "ERROR_CODE",
    "details": {}
  }
}
```

### 常见错误码
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突
- `422` - 数据验证失败
- `500` - 服务器内部错误

## 📝 请求示例

### 使用curl
```bash
# 登录获取token
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"password123"}'

# 搜索物种
curl -X GET "http://localhost:3001/api/species/search?keyword=蓝鲸" \
  -H "Authorization: Bearer <token>"

# 上传文件
curl -X POST http://localhost:3001/api/files/upload/audio \
  -H "Authorization: Bearer <token>" \
  -F "file=@audio-file.wav"
```

### 使用JavaScript
```javascript
// 登录
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    username: 'testuser',
    password: 'password123'
  })
});

const { data } = await loginResponse.json();
const token = data.token;

// 搜索物种
const searchResponse = await fetch('/api/species/search?keyword=蓝鲸', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const species = await searchResponse.json();
```

## 🔄 版本控制

API使用语义化版本控制。当前版本为 `v1`。

### 版本兼容性
- **主版本号**: 不兼容的API修改
- **次版本号**: 向后兼容的功能性新增
- **修订号**: 向后兼容的问题修正

## 📞 技术支持

如有API使用问题，请：
1. 查看错误响应信息
2. 检查请求格式和参数
3. 验证认证令牌
4. 提交Issue到GitHub仓库
