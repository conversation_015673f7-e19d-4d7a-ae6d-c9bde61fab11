import React, { useState } from 'react';
import { Card, Typography, Space, Alert, Row, Col, Button } from 'antd';
import BaseMap from '../components/Map/BaseMap';
import MapServiceSelector from '../components/Map/MapServiceSelector';
import { MapServiceType } from '../config/mapConfig';

const { Title, Paragraph } = Typography;

/**
 * 地图测试页面
 * 用于测试不同地图服务的加载和显示效果
 */
const MapTestPage: React.FC = () => {
  const [mapService, setMapService] = useState<MapServiceType>(MapServiceType.TIANDITU_SATELLITE);
  const [mapCenter, setMapCenter] = useState<[number, number]>([35.0, 104.0]);
  const [mapZoom, setMapZoom] = useState(4);

  const handleMapServiceChange = (service: MapServiceType) => {
    setMapService(service);
  };

  const handleResetView = () => {
    setMapCenter([35.0, 104.0]);
    setMapZoom(4);
  };

  const presetLocations = [
    { name: '中国全境', center: [35.0, 104.0] as [number, number], zoom: 4 },
    { name: '北京', center: [39.9042, 116.4074] as [number, number], zoom: 10 },
    { name: '上海', center: [31.2304, 121.4737] as [number, number], zoom: 10 },
    { name: '广州', center: [23.1291, 113.2644] as [number, number], zoom: 10 },
    { name: '台湾', center: [23.8, 121.0] as [number, number], zoom: 8 },
    { name: '南海', center: [16.0, 112.0] as [number, number], zoom: 6 },
  ];

  return (
    <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={2}>地图服务测试页面</Title>
        
        <Alert
          message="地图服务合规性说明"
          description="本页面展示的所有地图服务均符合中华人民共和国测绘法和地图管理条例要求，确保台湾地区正确显示为中国领土的一部分。"
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Row gutter={[16, 16]}>
          {/* 控制面板 */}
          <Col xs={24} lg={8}>
            <Card title="地图控制" style={{ marginBottom: '16px' }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Paragraph strong>选择地图服务：</Paragraph>
                  <MapServiceSelector
                    value={mapService}
                    onChange={handleMapServiceChange}
                    style={{ width: '100%' }}
                  />
                </div>

                <div>
                  <Paragraph strong>快速定位：</Paragraph>
                  <Space wrap>
                    {presetLocations.map((location) => (
                      <Button
                        key={location.name}
                        size="small"
                        onClick={() => {
                          setMapCenter(location.center);
                          setMapZoom(location.zoom);
                        }}
                      >
                        {location.name}
                      </Button>
                    ))}
                  </Space>
                </div>

                <Button type="primary" onClick={handleResetView} block>
                  重置视图
                </Button>
              </Space>
            </Card>

            <Card title="当前配置" size="small">
              <Space direction="vertical" style={{ width: '100%', fontSize: '12px' }}>
                <div>地图服务: {mapService}</div>
                <div>中心点: [{mapCenter[0].toFixed(4)}, {mapCenter[1].toFixed(4)}]</div>
                <div>缩放级别: {mapZoom}</div>
              </Space>
            </Card>
          </Col>

          {/* 地图显示区域 */}
          <Col xs={24} lg={16}>
            <Card 
              title={`地图显示 - ${mapService}`}
              style={{ height: '600px' }}
              bodyStyle={{ padding: 0, height: 'calc(100% - 57px)' }}
            >
              <BaseMap
                center={mapCenter}
                zoom={mapZoom}
                style={{ height: '100%', width: '100%' }}
                mapService={mapService}
                onMapReady={(map) => {
                  console.log('地图已准备就绪:', map);
                  
                  // 监听地图移动事件
                  map.on('moveend', () => {
                    const center = map.getCenter();
                    setMapCenter([center.lat, center.lng]);
                  });
                  
                  // 监听缩放事件
                  map.on('zoomend', () => {
                    setMapZoom(map.getZoom());
                  });
                }}
              />
            </Card>
          </Col>
        </Row>

        {/* 说明信息 */}
        <Card title="使用说明" style={{ marginTop: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Title level={4}>支持的地图服务</Title>
              <ul>
                <li><strong>天地图影像</strong>: 国家地理信息公共服务平台影像地图（推荐）</li>
                <li><strong>天地图矢量</strong>: 国家地理信息公共服务平台矢量地图</li>
                <li><strong>高德地图</strong>: 高德地图服务（符合中国地图审查要求）</li>
                <li><strong>百度地图</strong>: 百度地图服务（符合中国地图审查要求）</li>
              </ul>
            </Col>
            <Col xs={24} md={12}>
              <Title level={4}>合规性特点</Title>
              <ul>
                <li>✅ 台湾地区显示为中国领土的一部分</li>
                <li>✅ 南海诸岛完整显示</li>
                <li>✅ 国界线符合中国官方标准</li>
                <li>✅ 地名标注使用中文</li>
              </ul>
            </Col>
          </Row>
        </Card>
      </div>
    </div>
  );
};

export default MapTestPage;
