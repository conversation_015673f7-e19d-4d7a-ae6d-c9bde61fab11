import React, { useState, useEffect } from 'react';
import { List, Card, Button, Space, Typography, Tag, Avatar, Tooltip } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  SoundOutlined,
  DownloadOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import BaseAudioPlayer from './BaseAudioPlayer';

const { Text, Title } = Typography;

interface AudioFile {
  id: string;
  fileName: string;
  filePath: string;
  recordingLocation?: string;
  recordingTime?: string;
  behaviorDescription?: string;
  duration?: number;
  fileSize?: number;
}

interface AudioPlaylistProps {
  audioFiles: AudioFile[];
  title?: string;
  showPlayer?: boolean;
  onFileSelect?: (file: AudioFile) => void;
}

const AudioPlaylist: React.FC<AudioPlaylistProps> = ({
  audioFiles,
  title = '音频文件列表',
  showPlayer = true,
  onFileSelect,
}) => {
  const [currentFile, setCurrentFile] = useState<AudioFile | null>(null);
  const [playingId, setPlayingId] = useState<string | null>(null);

  // 默认选择第一个文件
  useEffect(() => {
    if (audioFiles.length > 0 && !currentFile) {
      setCurrentFile(audioFiles[0]);
    }
  }, [audioFiles, currentFile]);

  // 播放指定文件
  const playFile = (file: AudioFile) => {
    setCurrentFile(file);
    setPlayingId(file.id);
    onFileSelect?.(file);
  };

  // 播放下一个文件
  const playNext = () => {
    if (currentFile) {
      const currentIndex = audioFiles.findIndex(f => f.id === currentFile.id);
      const nextIndex = (currentIndex + 1) % audioFiles.length;
      playFile(audioFiles[nextIndex]);
    }
  };

  // 播放上一个文件
  const playPrevious = () => {
    if (currentFile) {
      const currentIndex = audioFiles.findIndex(f => f.id === currentFile.id);
      const prevIndex = currentIndex === 0 ? audioFiles.length - 1 : currentIndex - 1;
      playFile(audioFiles[prevIndex]);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // 格式化时长
  const formatDuration = (seconds?: number) => {
    if (!seconds) return '';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div>
      {/* 当前播放器 */}
      {showPlayer && currentFile && (
        <Card 
          title={
            <Space>
              <SoundOutlined />
              正在播放
            </Space>
          }
          style={{ marginBottom: '24px' }}
          extra={
            <Space>
              <Button size="small" onClick={playPrevious}>
                上一首
              </Button>
              <Button size="small" onClick={playNext}>
                下一首
              </Button>
            </Space>
          }
        >
          <BaseAudioPlayer
            audioUrl={`${import.meta.env.VITE_STATIC_FILES_URL}/${currentFile.filePath}`}
            title={currentFile.fileName}
            onFinish={playNext}
            showWaveform={true}
            height={100}
          />
        </Card>
      )}

      {/* 播放列表 */}
      <Card
        title={
          <Space>
            <SoundOutlined />
            {title}
            <Tag color="blue">{audioFiles.length} 个文件</Tag>
          </Space>
        }
      >
        <List
          itemLayout="vertical"
          size="small"
          dataSource={audioFiles}
          renderItem={(file, index) => (
            <List.Item
              key={file.id}
              actions={[
                <Button
                  key="play"
                  type={currentFile?.id === file.id ? "primary" : "default"}
                  size="small"
                  icon={playingId === file.id ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                  onClick={() => playFile(file)}
                >
                  {currentFile?.id === file.id ? '正在播放' : '播放'}
                </Button>,
                <Tooltip title="下载文件" key="download">
                  <Button
                    size="small"
                    type="text"
                    icon={<DownloadOutlined />}
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = `${import.meta.env.VITE_STATIC_FILES_URL}/${file.filePath}`;
                      link.download = file.fileName;
                      link.click();
                    }}
                  />
                </Tooltip>
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Avatar 
                    style={{ 
                      backgroundColor: currentFile?.id === file.id ? '#1890ff' : '#f56a00',
                      fontSize: '12px'
                    }}
                  >
                    {index + 1}
                  </Avatar>
                }
                title={
                  <Space>
                    <Text strong style={{ 
                      color: currentFile?.id === file.id ? '#1890ff' : undefined 
                    }}>
                      {file.fileName}
                    </Text>
                    {file.duration && (
                      <Tag icon={<ClockCircleOutlined />} color="default">
                        {formatDuration(file.duration)}
                      </Tag>
                    )}
                  </Space>
                }
                description={
                  <Space direction="vertical" size={4}>
                    {file.recordingLocation && (
                      <Space size={4}>
                        <EnvironmentOutlined style={{ color: '#52c41a' }} />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {file.recordingLocation}
                        </Text>
                      </Space>
                    )}
                    
                    {file.recordingTime && (
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        录音时间: {new Date(file.recordingTime).toLocaleString()}
                      </Text>
                    )}
                    
                    {file.behaviorDescription && (
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        行为描述: {file.behaviorDescription}
                      </Text>
                    )}
                    
                    <Space size={8}>
                      {file.fileSize && (
                        <Text type="secondary" style={{ fontSize: '11px' }}>
                          大小: {formatFileSize(file.fileSize)}
                        </Text>
                      )}
                    </Space>
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export default AudioPlaylist;
