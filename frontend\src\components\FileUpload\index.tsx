import React, { useState } from 'react';
import { Card, Tabs, Typography } from 'antd';
import { 
  UploadOutlined, 
  SoundOutlined, 
  PictureOutlined, 
  FileOutlined 
} from '@ant-design/icons';
import BaseUpload from './BaseUpload';
import AudioUpload from './AudioUpload';
import ImageUpload from './ImageUpload';
import FileManager from './FileManager';

const { Title } = Typography;

interface FileUploadProps {
  speciesId?: string;
  defaultActiveKey?: string;
  showTabs?: boolean;
  onUploadComplete?: (files: any[]) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({
  speciesId,
  defaultActiveKey = 'audio',
  showTabs = true,
  onUploadComplete,
}) => {
  const [activeKey, setActiveKey] = useState(defaultActiveKey);

  const handleUploadComplete = (files: any[]) => {
    onUploadComplete?.(files);
  };

  const tabItems = [
    {
      key: 'audio',
      label: (
        <span>
          <SoundOutlined />
          音频上传
        </span>
      ),
      children: (
        <AudioUpload
          speciesId={speciesId}
          onUploadComplete={handleUploadComplete}
        />
      ),
    },
    {
      key: 'image',
      label: (
        <span>
          <PictureOutlined />
          图片上传
        </span>
      ),
      children: (
        <ImageUpload
          speciesId={speciesId}
          onUploadComplete={handleUploadComplete}
        />
      ),
    },
    {
      key: 'general',
      label: (
        <span>
          <UploadOutlined />
          通用上传
        </span>
      ),
      children: (
        <BaseUpload
          accept="*"
          maxSize={100}
          maxCount={10}
          uploadUrl={`${import.meta.env.VITE_API_BASE_URL}/files/upload`}
        />
      ),
    },
    {
      key: 'manager',
      label: (
        <span>
          <FileOutlined />
          文件管理
        </span>
      ),
      children: (
        <FileManager
          speciesId={speciesId}
          showUploader={true}
        />
      ),
    },
  ];

  if (!showTabs) {
    // 根据activeKey返回对应组件
    switch (activeKey) {
      case 'audio':
        return (
          <AudioUpload
            speciesId={speciesId}
            onUploadComplete={handleUploadComplete}
          />
        );
      case 'image':
        return (
          <ImageUpload
            speciesId={speciesId}
            onUploadComplete={handleUploadComplete}
          />
        );
      case 'manager':
        return (
          <FileManager
            speciesId={speciesId}
            showUploader={true}
          />
        );
      default:
        return (
          <BaseUpload
            accept="*"
            maxSize={100}
            maxCount={10}
            uploadUrl={`${import.meta.env.VITE_API_BASE_URL}/files/upload`}
          />
        );
    }
  }

  return (
    <Card
      title={
        <span>
          <UploadOutlined />
          文件上传
        </span>
      }
    >
      <Tabs
        activeKey={activeKey}
        onChange={setActiveKey}
        items={tabItems}
        size="small"
      />
    </Card>
  );
};

// 导出所有组件
export default FileUpload;
export { BaseUpload, AudioUpload, ImageUpload, FileManager };
