import { Router } from 'express';
import { AuthController } from '../controllers/AuthController';
import { authenticateToken, requirePermission } from '../middleware/auth';

const router = Router();
const authController = new AuthController();

// 公开路由（不需要认证）
router.post('/login', authController.login);
router.post('/register', authController.register);

// 需要认证的路由
router.use(authenticateToken);

// 用户信息相关
router.get('/profile', authController.getProfile);
router.put('/profile', authController.updateProfile);
router.post('/change-password', authController.changePassword);
router.post('/logout', authController.logout);
router.get('/verify', authController.verifyToken);

// 管理员功能（需要用户管理权限）
router.post(
  '/reset-password/:userId',
  requirePermission('user', 'write'),
  authController.resetPassword
);

export default router;
