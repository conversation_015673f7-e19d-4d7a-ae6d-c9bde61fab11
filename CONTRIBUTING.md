# 贡献指南

感谢您对海洋生物声音平台项目的关注！我们欢迎所有形式的贡献，包括但不限于代码、文档、测试、问题反馈和功能建议。

## 🤝 如何贡献

### 报告问题
如果您发现了bug或有功能建议，请：

1. 在提交新issue前，先搜索现有的issues
2. 使用清晰、描述性的标题
3. 提供详细的问题描述，包括：
   - 重现步骤
   - 预期行为
   - 实际行为
   - 环境信息（操作系统、浏览器、Node.js版本等）
   - 相关截图或错误日志

### 提交代码

#### 1. Fork项目
```bash
# Fork项目到您的GitHub账户
# 然后克隆到本地
git clone https://github.com/your-username/marine-bio-platform.git
cd marine-bio-platform
```

#### 2. 创建分支
```bash
# 从main分支创建新的功能分支
git checkout -b feature/your-feature-name

# 或者修复bug的分支
git checkout -b fix/bug-description
```

#### 3. 开发环境设置
```bash
# 安装依赖
cd frontend && pnpm install
cd ../backend && pnpm install

# 配置环境变量
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# 启动开发服务器
cd backend && pnpm run dev
cd frontend && pnpm run dev
```

#### 4. 编写代码
- 遵循项目的代码规范
- 编写清晰的注释
- 确保代码通过所有测试
- 为新功能编写测试用例

#### 5. 提交更改
```bash
# 添加更改
git add .

# 提交更改（使用规范的提交信息）
git commit -m "feat: add new species search filter"

# 推送到您的fork
git push origin feature/your-feature-name
```

#### 6. 创建Pull Request
1. 在GitHub上创建Pull Request
2. 填写PR模板中的所有必要信息
3. 确保CI检查通过
4. 等待代码审查

## 📝 代码规范

### 提交信息规范
我们使用[Conventional Commits](https://www.conventionalcommits.org/)规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型 (type)**:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整（不影响功能）
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动
- `perf`: 性能优化
- `ci`: CI配置文件和脚本的变动

**示例**:
```
feat(auth): add OAuth2 login support
fix(api): resolve species search pagination issue
docs: update API documentation
test(frontend): add unit tests for audio player
```

### 代码风格

#### TypeScript/JavaScript
- 使用TypeScript进行类型安全
- 遵循ESLint配置
- 使用Prettier进行代码格式化
- 优先使用函数式编程风格
- 使用有意义的变量和函数名

```typescript
// ✅ 好的示例
interface SpeciesSearchParams {
  keyword?: string;
  conservationStatus?: string;
  page?: number;
  limit?: number;
}

const searchSpecies = async (params: SpeciesSearchParams): Promise<SpeciesSearchResult> => {
  // 实现逻辑
};

// ❌ 避免的示例
const search = (p: any) => {
  // 实现逻辑
};
```

#### React组件
- 使用函数组件和Hooks
- 优先使用TypeScript接口定义props
- 使用有意义的组件名
- 保持组件单一职责

```tsx
// ✅ 好的示例
interface AudioPlayerProps {
  audioUrl: string;
  title: string;
  onPlay?: () => void;
  onPause?: () => void;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({ 
  audioUrl, 
  title, 
  onPlay, 
  onPause 
}) => {
  // 组件实现
};
```

#### CSS/样式
- 使用CSS Modules或styled-components
- 遵循BEM命名规范
- 优先使用Ant Design组件
- 确保响应式设计

### 测试规范

#### 单元测试
- 为所有新功能编写测试
- 测试覆盖率应达到75%以上
- 使用描述性的测试名称
- 遵循AAA模式（Arrange, Act, Assert）

```typescript
// ✅ 好的测试示例
describe('SpeciesService', () => {
  describe('searchSpecies', () => {
    it('应该返回匹配关键词的物种列表', async () => {
      // Arrange
      const keyword = '蓝鲸';
      const expectedSpecies = [{ id: '1', chineseName: '蓝鲸' }];
      
      // Act
      const result = await speciesService.searchSpecies({ keyword });
      
      // Assert
      expect(result.items).toEqual(expectedSpecies);
    });
  });
});
```

#### 集成测试
- 测试API端点的完整流程
- 验证数据库操作
- 测试错误处理

#### E2E测试
- 测试关键用户流程
- 确保跨浏览器兼容性
- 测试响应式设计

## 🔍 代码审查

### 审查清单
在提交PR前，请确保：

- [ ] 代码遵循项目规范
- [ ] 所有测试通过
- [ ] 代码覆盖率达到要求
- [ ] 文档已更新
- [ ] 没有引入安全漏洞
- [ ] 性能没有明显下降
- [ ] 向后兼容性

### 审查过程
1. 自动化检查（CI/CD）
2. 代码审查（至少一位维护者）
3. 测试验证
4. 合并到主分支

## 📚 开发资源

### 文档
- [项目架构](docs/ARCHITECTURE.md)
- [API文档](docs/API.md)
- [部署指南](docs/DEPLOYMENT.md)

### 工具和库
- **前端**: React, TypeScript, Ant Design, Redux Toolkit
- **后端**: Node.js, Express, TypeORM, PostgreSQL
- **测试**: Jest, React Testing Library, Cypress
- **工具**: ESLint, Prettier, Husky

### 开发环境
- Node.js 18+
- PostgreSQL 14+ with PostGIS
- pnpm 8+

## 🏷️ 发布流程

### 版本号规范
遵循[语义化版本](https://semver.org/)：
- `MAJOR.MINOR.PATCH`
- 主版本号：不兼容的API修改
- 次版本号：向后兼容的功能性新增
- 修订号：向后兼容的问题修正

### 发布步骤
1. 更新版本号
2. 更新CHANGELOG.md
3. 创建发布标签
4. 部署到生产环境

## 🎯 优先级和路线图

### 高优先级
- 性能优化
- 安全性改进
- 用户体验提升
- 测试覆盖率提升

### 中优先级
- 新功能开发
- 代码重构
- 文档完善

### 低优先级
- 实验性功能
- 非核心功能优化

## 🆘 获取帮助

如果您在贡献过程中遇到问题：

1. 查看现有文档和issues
2. 在GitHub上创建discussion
3. 联系维护者团队
4. 参加社区会议（如有）

## 🙏 致谢

感谢所有为项目做出贡献的开发者！您的贡献让这个项目变得更好。

### 贡献者列表
- [维护者姓名](https://github.com/maintainer)
- [贡献者姓名](https://github.com/contributor)

## 📄 许可证

通过贡献代码，您同意您的贡献将在与项目相同的[MIT许可证](LICENSE)下发布。

---

再次感谢您的贡献！🌊🐋
