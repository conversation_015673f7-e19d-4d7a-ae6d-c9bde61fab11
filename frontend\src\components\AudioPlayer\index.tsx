import React, { useState } from 'react';
import { Card, Tabs, Space, Typography } from 'antd';
import { SoundOutlined, BarChartOutlined, UnorderedListOutlined } from '@ant-design/icons';
import BaseAudioPlayer from './BaseAudioPlayer';
import AudioPlaylist from './AudioPlaylist';
import AudioVisualizer from './AudioVisualizer';

const { Title } = Typography;

interface AudioFile {
  id: string;
  fileName: string;
  filePath: string;
  recordingLocation?: string;
  recordingTime?: string;
  behaviorDescription?: string;
  duration?: number;
  fileSize?: number;
}

interface AudioPlayerProps {
  audioFiles: AudioFile[];
  title?: string;
  defaultActiveKey?: string;
  showTabs?: boolean;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({
  audioFiles,
  title = '音频播放器',
  defaultActiveKey = 'player',
  showTabs = true,
}) => {
  const [currentFile, setCurrentFile] = useState<AudioFile | null>(
    audioFiles.length > 0 ? audioFiles[0] : null
  );

  const handleFileSelect = (file: AudioFile) => {
    setCurrentFile(file);
  };

  const tabItems = [
    {
      key: 'player',
      label: (
        <Space>
          <SoundOutlined />
          播放器
        </Space>
      ),
      children: currentFile ? (
        <BaseAudioPlayer
          audioUrl={`${import.meta.env.VITE_STATIC_FILES_URL}/${currentFile.filePath}`}
          title={currentFile.fileName}
          showWaveform={true}
          height={120}
        />
      ) : (
        <div style={{ 
          textAlign: 'center', 
          padding: '40px',
          color: '#999'
        }}>
          请选择音频文件
        </div>
      ),
    },
    {
      key: 'playlist',
      label: (
        <Space>
          <UnorderedListOutlined />
          播放列表
        </Space>
      ),
      children: (
        <AudioPlaylist
          audioFiles={audioFiles}
          title="音频文件列表"
          showPlayer={false}
          onFileSelect={handleFileSelect}
        />
      ),
    },
    {
      key: 'visualizer',
      label: (
        <Space>
          <BarChartOutlined />
          可视化
        </Space>
      ),
      children: currentFile ? (
        <AudioVisualizer
          audioUrl={`${import.meta.env.VITE_STATIC_FILES_URL}/${currentFile.filePath}`}
          title={`${currentFile.fileName} - 音频可视化`}
          height={300}
          showControls={true}
        />
      ) : (
        <div style={{ 
          textAlign: 'center', 
          padding: '40px',
          color: '#999'
        }}>
          请选择音频文件进行可视化分析
        </div>
      ),
    },
  ];

  if (!showTabs && currentFile) {
    return (
      <BaseAudioPlayer
        audioUrl={`${import.meta.env.VITE_STATIC_FILES_URL}/${currentFile.filePath}`}
        title={currentFile.fileName}
        showWaveform={true}
        height={80}
      />
    );
  }

  return (
    <Card
      title={
        <Space>
          <SoundOutlined />
          {title}
        </Space>
      }
    >
      {audioFiles.length === 0 ? (
        <div style={{ 
          textAlign: 'center', 
          padding: '60px',
          color: '#999'
        }}>
          <SoundOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
          <div>暂无音频文件</div>
        </div>
      ) : (
        <Tabs
          defaultActiveKey={defaultActiveKey}
          items={tabItems}
          size="small"
        />
      )}
    </Card>
  );
};

// 导出所有组件
export default AudioPlayer;
export { BaseAudioPlayer, AudioPlaylist, AudioVisualizer };
export type { AudioFile };
