// 简单的数据库连接测试脚本
const { Client } = require('pg');
const redis = require('redis');

async function testPostgreSQL() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'postgres',
    password: 'password',
    database: 'marine_bio_platform'
  });

  try {
    await client.connect();
    console.log('✅ PostgreSQL连接成功');
    
    // 测试PostGIS
    const result = await client.query("SELECT PostGIS_Version()");
    console.log('✅ PostGIS版本:', result.rows[0].postgis_version);
    
    // 测试数据库表
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    console.log('✅ 数据库表:', tables.rows.map(row => row.table_name).join(', '));
    
    await client.end();
  } catch (error) {
    console.error('❌ PostgreSQL连接失败:', error.message);
  }
}

async function testRedis() {
  const client = redis.createClient({
    host: 'localhost',
    port: 6379
  });

  try {
    await client.connect();
    console.log('✅ Redis连接成功');
    
    // 测试Redis操作
    await client.set('test_key', 'Hello Redis!');
    const value = await client.get('test_key');
    console.log('✅ Redis测试:', value);
    
    await client.del('test_key');
    await client.disconnect();
  } catch (error) {
    console.error('❌ Redis连接失败:', error.message);
  }
}

async function main() {
  console.log('🔍 开始测试数据库连接...\n');
  
  await testPostgreSQL();
  console.log('');
  await testRedis();
  
  console.log('\n🎉 连接测试完成！');
}

main().catch(console.error);
