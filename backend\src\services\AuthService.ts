import { Repository } from 'typeorm';
import bcrypt from 'bcryptjs';
import { AppDataSource } from '../config/database';
import { User, Role, OperationLog } from '../models';
import { generateToken } from '../middleware/auth';

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email?: string;
  password: string;
  roleNames?: string[];
}

export interface LoginResult {
  user: User;
  token: string;
  expiresIn: string;
}

export class AuthService {
  private userRepository: Repository<User>;
  private roleRepository: Repository<Role>;
  private logRepository: Repository<OperationLog>;

  constructor() {
    this.userRepository = AppDataSource.getRepository(User);
    this.roleRepository = AppDataSource.getRepository(Role);
    this.logRepository = AppDataSource.getRepository(OperationLog);
  }

  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials, ipAddress?: string, userAgent?: string): Promise<LoginResult> {
    const { username, password } = credentials;

    // 查找用户
    const user = await this.userRepository.findOne({
      where: { username },
      relations: ['roles'],
    });

    if (!user) {
      // 记录登录失败日志
      await this.logFailedLogin(username, '用户不存在', ipAddress, userAgent);
      throw new Error('用户名或密码错误');
    }

    // 检查用户状态
    if (user.status !== 'active') {
      await this.logFailedLogin(username, '用户已被禁用', ipAddress, userAgent);
      throw new Error('用户账户已被禁用');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      await this.logFailedLogin(username, '密码错误', ipAddress, userAgent);
      throw new Error('用户名或密码错误');
    }

    // 更新最后登录时间
    user.lastLogin = new Date();
    await this.userRepository.save(user);

    // 生成JWT令牌
    const token = generateToken(user);
    const expiresIn = process.env.JWT_EXPIRES_IN || '7d';

    // 记录成功登录日志
    await this.logSuccessfulLogin(user, ipAddress, userAgent);

    return {
      user,
      token,
      expiresIn,
    };
  }

  /**
   * 用户注册
   */
  async register(registerData: RegisterData): Promise<User> {
    const { username, email, password, roleNames = ['viewer'] } = registerData;

    // 检查用户名是否已存在
    const existingUser = await this.userRepository.findOne({
      where: { username },
    });

    if (existingUser) {
      throw new Error('用户名已存在');
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingEmail = await this.userRepository.findOne({
        where: { email },
      });

      if (existingEmail) {
        throw new Error('邮箱已被使用');
      }
    }

    // 验证角色是否存在
    const roles = await this.roleRepository.find({
      where: roleNames.map(name => ({ name })),
    });

    if (roles.length !== roleNames.length) {
      throw new Error('指定的角色不存在');
    }

    // 加密密码
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const user = new User();
    user.username = username;
    user.email = email;
    user.passwordHash = passwordHash;
    user.roles = roles;
    user.status = 'active';

    const savedUser = await this.userRepository.save(user);

    // 记录注册日志
    await this.logUserRegistration(savedUser);

    return savedUser;
  }

  /**
   * 修改密码
   */
  async changePassword(userId: string, oldPassword: string, newPassword: string): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new Error('用户不存在');
    }

    // 验证旧密码
    const isOldPasswordValid = await bcrypt.compare(oldPassword, user.passwordHash);
    if (!isOldPasswordValid) {
      throw new Error('原密码错误');
    }

    // 加密新密码
    const saltRounds = 10;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // 更新密码
    user.passwordHash = newPasswordHash;
    await this.userRepository.save(user);

    // 记录密码修改日志
    await this.logPasswordChange(user);
  }

  /**
   * 重置密码（管理员功能）
   */
  async resetPassword(userId: string, newPassword: string, adminUserId: string): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new Error('用户不存在');
    }

    // 加密新密码
    const saltRounds = 10;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // 更新密码
    user.passwordHash = newPasswordHash;
    await this.userRepository.save(user);

    // 记录密码重置日志
    await this.logPasswordReset(user, adminUserId);
  }

  /**
   * 获取用户信息
   */
  async getUserProfile(userId: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['roles'],
    });

    if (!user) {
      throw new Error('用户不存在');
    }

    return user;
  }

  /**
   * 更新用户信息
   */
  async updateUserProfile(userId: string, updateData: Partial<User>): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new Error('用户不存在');
    }

    // 只允许更新特定字段
    const allowedFields = ['email'];
    const filteredData: Partial<User> = {};

    for (const field of allowedFields) {
      if (updateData[field as keyof User] !== undefined) {
        (filteredData as any)[field] = updateData[field as keyof User];
      }
    }

    // 检查邮箱唯一性
    if (filteredData.email && filteredData.email !== user.email) {
      const existingEmail = await this.userRepository.findOne({
        where: { email: filteredData.email },
      });

      if (existingEmail) {
        throw new Error('邮箱已被使用');
      }
    }

    Object.assign(user, filteredData);
    const updatedUser = await this.userRepository.save(user);

    // 记录用户信息更新日志
    await this.logUserUpdate(updatedUser);

    return updatedUser;
  }

  /**
   * 验证用户权限
   */
  async hasPermission(userId: string, resource: string, action: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['roles'],
    });

    if (!user || user.status !== 'active') {
      return false;
    }

    // 检查用户角色权限
    for (const role of user.roles) {
      if (role.permissions) {
        // 全部权限
        if (role.permissions.all === true) {
          return true;
        }

        // 特定资源权限
        const resourcePermissions = role.permissions[resource];
        if (resourcePermissions) {
          if (resourcePermissions.all === true || resourcePermissions[action] === true) {
            return true;
          }
        }
      }
    }

    return false;
  }

  /**
   * 记录成功登录日志
   */
  private async logSuccessfulLogin(user: User, ipAddress?: string, userAgent?: string): Promise<void> {
    const log = new OperationLog();
    log.user = user;
    log.action = 'login';
    log.resource = 'auth';
    log.details = { success: true };
    log.ipAddress = ipAddress;
    log.userAgent = userAgent;

    await this.logRepository.save(log);
  }

  /**
   * 记录失败登录日志
   */
  private async logFailedLogin(username: string, reason: string, ipAddress?: string, userAgent?: string): Promise<void> {
    const log = new OperationLog();
    log.action = 'login';
    log.resource = 'auth';
    log.details = { success: false, username, reason };
    log.ipAddress = ipAddress;
    log.userAgent = userAgent;

    await this.logRepository.save(log);
  }

  /**
   * 记录用户注册日志
   */
  private async logUserRegistration(user: User): Promise<void> {
    const log = new OperationLog();
    log.user = user;
    log.action = 'register';
    log.resource = 'user';
    log.resourceId = user.id;
    log.details = { username: user.username };

    await this.logRepository.save(log);
  }

  /**
   * 记录密码修改日志
   */
  private async logPasswordChange(user: User): Promise<void> {
    const log = new OperationLog();
    log.user = user;
    log.action = 'change_password';
    log.resource = 'user';
    log.resourceId = user.id;

    await this.logRepository.save(log);
  }

  /**
   * 记录密码重置日志
   */
  private async logPasswordReset(user: User, adminUserId: string): Promise<void> {
    const log = new OperationLog();
    log.action = 'reset_password';
    log.resource = 'user';
    log.resourceId = user.id;
    log.details = { adminUserId, targetUserId: user.id };

    await this.logRepository.save(log);
  }

  /**
   * 记录用户信息更新日志
   */
  private async logUserUpdate(user: User): Promise<void> {
    const log = new OperationLog();
    log.user = user;
    log.action = 'update';
    log.resource = 'user';
    log.resourceId = user.id;

    await this.logRepository.save(log);
  }
}
