// 性能测试运行脚本
import { 
  getMemoryUsage, 
  detectMemoryLeaks, 
  FPSMonitor, 
  generatePerformanceReport 
} from '../utils/performanceUtils';

// 性能测试配置
interface PerformanceTestConfig {
  iterations: number;
  memoryThreshold: number; // MB
  renderTimeThreshold: number; // ms
  fpsThreshold: number;
}

const defaultConfig: PerformanceTestConfig = {
  iterations: 10,
  memoryThreshold: 10, // 10MB
  renderTimeThreshold: 16, // 16ms (60fps)
  fpsThreshold: 55, // 55fps minimum
};

// 性能测试结果
interface PerformanceTestResult {
  testName: string;
  passed: boolean;
  metrics: {
    memoryUsage?: ReturnType<typeof getMemoryUsage>;
    renderTime?: number;
    fps?: number;
    memoryLeak?: boolean;
  };
  details: string;
}

class PerformanceTestRunner {
  private config: PerformanceTestConfig;
  private results: PerformanceTestResult[] = [];
  private fpsMonitor: FPSMonitor;

  constructor(config: Partial<PerformanceTestConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    this.fpsMonitor = new FPSMonitor();
  }

  // 运行所有性能测试
  async runAllTests(): Promise<PerformanceTestResult[]> {
    console.log('🚀 开始运行性能测试...');
    
    this.results = [];
    
    // 内存使用测试
    await this.testMemoryUsage();
    
    // 渲染性能测试
    await this.testRenderPerformance();
    
    // FPS测试
    await this.testFPS();
    
    // 内存泄漏测试
    await this.testMemoryLeaks();
    
    // 生成报告
    this.generateReport();
    
    return this.results;
  }

  // 内存使用测试
  private async testMemoryUsage(): Promise<void> {
    console.log('📊 测试内存使用...');
    
    const memoryUsage = getMemoryUsage();
    const memoryUsageMB = memoryUsage.used / (1024 * 1024);
    
    const passed = memoryUsageMB < 100; // 100MB threshold
    
    this.results.push({
      testName: '内存使用测试',
      passed,
      metrics: { memoryUsage },
      details: `当前内存使用: ${memoryUsageMB.toFixed(2)}MB (${memoryUsage.percentage.toFixed(1)}%)`
    });
  }

  // 渲染性能测试
  private async testRenderPerformance(): Promise<void> {
    console.log('⚡ 测试渲染性能...');
    
    const renderTimes: number[] = [];
    
    // 模拟多次渲染
    for (let i = 0; i < this.config.iterations; i++) {
      const startTime = performance.now();
      
      // 模拟组件渲染工作
      await new Promise(resolve => {
        requestAnimationFrame(() => {
          // 模拟一些计算工作
          let sum = 0;
          for (let j = 0; j < 10000; j++) {
            sum += Math.random();
          }
          resolve(sum);
        });
      });
      
      const endTime = performance.now();
      renderTimes.push(endTime - startTime);
    }
    
    const avgRenderTime = renderTimes.reduce((sum, time) => sum + time, 0) / renderTimes.length;
    const maxRenderTime = Math.max(...renderTimes);
    
    const passed = avgRenderTime < this.config.renderTimeThreshold;
    
    this.results.push({
      testName: '渲染性能测试',
      passed,
      metrics: { renderTime: avgRenderTime },
      details: `平均渲染时间: ${avgRenderTime.toFixed(2)}ms, 最大: ${maxRenderTime.toFixed(2)}ms`
    });
  }

  // FPS测试
  private async testFPS(): Promise<void> {
    console.log('🎯 测试FPS...');
    
    return new Promise((resolve) => {
      this.fpsMonitor.start();
      
      // 运行3秒钟的FPS监控
      setTimeout(() => {
        const fps = this.fpsMonitor.getFPS();
        this.fpsMonitor.stop();
        
        const passed = fps >= this.config.fpsThreshold;
        
        this.results.push({
          testName: 'FPS测试',
          passed,
          metrics: { fps },
          details: `当前FPS: ${fps}`
        });
        
        resolve();
      }, 3000);
    });
  }

  // 内存泄漏测试
  private async testMemoryLeaks(): Promise<void> {
    console.log('🔍 测试内存泄漏...');
    
    const testFunction = () => {
      // 模拟组件挂载/卸载
      const elements: HTMLElement[] = [];
      for (let i = 0; i < 100; i++) {
        const div = document.createElement('div');
        div.innerHTML = `Test element ${i}`;
        elements.push(div);
      }
      
      // 清理
      elements.forEach(el => {
        if (el.parentNode) {
          el.parentNode.removeChild(el);
        }
      });
    };
    
    const hasMemoryLeak = await detectMemoryLeaks(
      testFunction,
      this.config.iterations,
      this.config.memoryThreshold * 1024 * 1024
    );
    
    this.results.push({
      testName: '内存泄漏测试',
      passed: !hasMemoryLeak,
      metrics: { memoryLeak: hasMemoryLeak },
      details: hasMemoryLeak ? '检测到内存泄漏' : '未检测到内存泄漏'
    });
  }

  // 生成测试报告
  private generateReport(): void {
    console.log('\n📋 性能测试报告');
    console.log('='.repeat(50));
    
    const passedTests = this.results.filter(r => r.passed).length;
    const totalTests = this.results.length;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${totalTests - passedTests}`);
    console.log(`通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    console.log('\n详细结果:');
    this.results.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.testName}`);
      console.log(`   ${result.details}`);
    });
    
    // 生成性能报告
    const performanceReport = generatePerformanceReport();
    console.log('\n🔧 系统性能信息:');
    console.log(`内存使用: ${(performanceReport.memory.used / 1024 / 1024).toFixed(2)}MB`);
    console.log(`页面加载时间: ${performanceReport.timing.loadEventEnd - performanceReport.timing.navigationStart}ms`);
    
    // 保存报告到localStorage（在浏览器环境中）
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('performanceTestResults', JSON.stringify({
        timestamp: new Date().toISOString(),
        results: this.results,
        systemInfo: performanceReport
      }));
    }
  }

  // 获取测试结果
  getResults(): PerformanceTestResult[] {
    return this.results;
  }
}

// 导出测试运行器
export default PerformanceTestRunner;

// 便捷函数：运行快速性能检查
export const runQuickPerformanceCheck = async (): Promise<void> => {
  const runner = new PerformanceTestRunner({
    iterations: 5,
    memoryThreshold: 5,
    renderTimeThreshold: 20,
    fpsThreshold: 50
  });
  
  await runner.runAllTests();
};
