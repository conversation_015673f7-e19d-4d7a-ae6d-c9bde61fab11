import { setupServer } from 'msw/node';
import { rest } from 'msw';

// 模拟API响应
const handlers = [
  // 健康检查
  rest.get('http://localhost:3001/health', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        database: 'connected'
      })
    );
  }),

  // 用户认证
  rest.post('http://localhost:3001/api/auth/login', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          token: 'mock-jwt-token',
          user: {
            id: '1',
            username: 'testuser',
            email: '<EMAIL>',
            roles: [
              {
                id: '1',
                name: 'viewer',
                description: '查看者'
              }
            ],
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z'
          }
        }
      })
    );
  }),

  rest.post('http://localhost:3001/api/auth/register', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        success: true,
        data: {
          id: '2',
          username: 'newuser',
          email: '<EMAIL>'
        }
      })
    );
  }),

  rest.get('http://localhost:3001/api/auth/profile', (req, res, ctx) => {
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res(
        ctx.status(401),
        ctx.json({
          success: false,
          error: { message: '未授权访问' }
        })
      );
    }

    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          roles: [
            {
              id: '1',
              name: 'viewer',
              description: '查看者'
            }
          ],
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z'
        }
      })
    );
  }),

  // 物种数据
  rest.get('http://localhost:3001/api/species/search', (req, res, ctx) => {
    const page = req.url.searchParams.get('page') || '1';
    const limit = req.url.searchParams.get('limit') || '20';
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          species: [
            {
              id: '1',
              chineseName: '蓝鲸',
              englishName: 'Blue Whale',
              latinName: 'Balaenoptera musculus',
              conservationStatus: '濒危',
              description: '世界上最大的动物',
              representativeImagePath: 'images/blue-whale.jpg',
              createdAt: '2024-01-01T00:00:00.000Z',
              updatedAt: '2024-01-01T00:00:00.000Z'
            }
          ],
          total: 1,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: 1
        }
      })
    );
  }),

  rest.get('http://localhost:3001/api/species/:id/details', (req, res, ctx) => {
    const { id } = req.params;
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          id,
          chineseName: '蓝鲸',
          englishName: 'Blue Whale',
          latinName: 'Balaenoptera musculus',
          conservationStatus: '濒危',
          description: '世界上最大的动物',
          representativeImagePath: 'images/blue-whale.jpg',
          classification: {
            kingdom: '动物界',
            phylum: '脊索动物门',
            class: '哺乳纲',
            order: '鲸目',
            family: '须鲸科',
            genus: '蓝鲸属',
            species: '蓝鲸'
          },
          audioFiles: [
            {
              id: '1',
              fileName: 'blue-whale-song.wav',
              filePath: 'audio/blue-whale-song.wav',
              recordingLocation: '太平洋',
              recordingTime: '2024-01-01T00:00:00.000Z',
              behaviorDescription: '歌声',
              duration: '120',
              fileSize: 1024000
            }
          ],
          imageFiles: [
            {
              id: '1',
              fileName: 'blue-whale.jpg',
              filePath: 'images/blue-whale.jpg',
              description: '蓝鲸游泳',
              imageType: 'representative'
            }
          ],
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z'
        }
      })
    );
  }),

  // 地理数据
  rest.get('http://localhost:3001/api/geography/distributions', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: [
          {
            id: '1',
            speciesId: '1',
            chineseName: '蓝鲸',
            latinName: 'Balaenoptera musculus',
            geometry: {
              type: 'Polygon',
              coordinates: [[
                [120, 30],
                [130, 30],
                [130, 40],
                [120, 40],
                [120, 30]
              ]]
            }
          }
        ]
      })
    );
  }),

  rest.get('http://localhost:3001/api/geography/collection-points', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: [
          {
            id: '1',
            species_id: '1',
            chinese_name: '蓝鲸',
            latin_name: 'Balaenoptera musculus',
            latitude: 35.6762,
            longitude: 139.6503,
            recording_location: '东京湾',
            audio_count: 5,
            last_recording: '2024-01-01T00:00:00.000Z',
            file_name: 'blue-whale-song.wav'
          }
        ]
      })
    );
  }),

  // 文件上传
  rest.post('http://localhost:3001/api/files/upload', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          originalName: 'test-file.wav',
          path: 'uploads/test-file.wav',
          size: 1024000,
          mimeType: 'audio/wav',
          url: 'http://localhost:3001/files/uploads/test-file.wav'
        }
      })
    );
  }),

  // 统计数据
  rest.get('http://localhost:3001/api/species/stats/taxonomy', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: [
          { name: '哺乳动物', count: 45 },
          { name: '鱼类', count: 67 },
          { name: '甲壳动物', count: 23 }
        ]
      })
    );
  }),

  // 默认处理器 - 返回404
  rest.all('*', (req, res, ctx) => {
    console.warn(`未处理的请求: ${req.method} ${req.url.href}`);
    return res(
      ctx.status(404),
      ctx.json({
        success: false,
        error: { message: '接口不存在' }
      })
    );
  }),
];

// 创建服务器实例
export const server = setupServer(...handlers);
