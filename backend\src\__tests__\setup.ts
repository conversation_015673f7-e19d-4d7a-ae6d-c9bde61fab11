import { DataSource } from 'typeorm';
import { createTestDatabase, closeTestDatabase } from './utils/database';

// 全局测试数据库连接
let testDataSource: DataSource;

// 全局设置
beforeAll(async () => {
  // 设置测试环境变量
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-jwt-secret';
  process.env.DB_HOST = 'localhost';
  process.env.DB_PORT = '5432';
  process.env.DB_USERNAME = 'test_user';
  process.env.DB_PASSWORD = 'test_password';
  process.env.DB_DATABASE = 'marine_bio_test';
  
  // 创建测试数据库连接
  testDataSource = await createTestDatabase();
  
  // 设置全局数据源
  (global as any).testDataSource = testDataSource;
}, 60000);

// 全局清理
afterAll(async () => {
  if (testDataSource) {
    await closeTestDatabase(testDataSource);
  }
}, 30000);

// 每个测试前清理数据
beforeEach(async () => {
  if (testDataSource) {
    // 清理所有表数据，但保留表结构
    const entities = testDataSource.entityMetadatas;
    
    for (const entity of entities) {
      const repository = testDataSource.getRepository(entity.name);
      await repository.clear();
    }
  }
});

// 设置Jest超时
jest.setTimeout(30000);

// 模拟console.log以减少测试输出噪音
const originalLog = console.log;
const originalError = console.error;
const originalWarn = console.warn;

beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.log = originalLog;
  console.error = originalError;
  console.warn = originalWarn;
});

// 处理未捕获的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});
