import React from 'react';
import { Card, Typography, Result, Button } from 'antd';

const { Title } = Typography;

const MediaManagementPage: React.FC = () => {
  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>媒体管理</Title>
      </div>

      <Card>
        <Result
          status="info"
          title="功能开发中"
          subTitle="媒体文件管理功能正在开发中，敬请期待。"
          extra={
            <Button type="primary">
              上传文件
            </Button>
          }
        />
      </Card>
    </div>
  );
};

export default MediaManagementPage;
