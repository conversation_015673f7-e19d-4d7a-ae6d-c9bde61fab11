import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { IsNotEmpty, IsIn, IsOptional, IsNumber, Min, Max } from 'class-validator';
import { User } from './User';

@Entity('audio_recognition_records')
export class AudioRecognitionRecord {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty({ message: '原始文件名不能为空' })
  originalFileName: string;

  @Column()
  @IsNotEmpty({ message: '文件路径不能为空' })
  filePath: string;

  @Column('bigint', { nullable: true })
  @IsOptional()
  @IsNumber({}, { message: '文件大小必须是数字' })
  fileSize?: number;

  @Column('jsonb', { nullable: true })
  recognitionResult?: Record<string, any>; // 识别结果

  @Column('decimal', { precision: 5, scale: 4, nullable: true })
  @IsOptional()
  @IsNumber({}, { message: '置信度必须是数字' })
  @Min(0, { message: '置信度不能小于0' })
  @Max(1, { message: '置信度不能大于1' })
  confidence?: number;

  @Column({
    type: 'enum',
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending'
  })
  @IsIn(['pending', 'processing', 'completed', 'failed'], {
    message: '状态必须是pending、processing、completed或failed之一'
  })
  status: 'pending' | 'processing' | 'completed' | 'failed';

  @Column('text', { nullable: true })
  errorMessage?: string;

  @Column({ nullable: true })
  completedAt?: Date;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @CreateDateColumn()
  createdAt: Date;
}
