import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { SystemStats, FileStats } from '../../types';

interface SystemState {
  // 系统统计
  systemStats: SystemStats | null;
  fileStats: FileStats | null;
  
  // 加载状态
  loading: {
    stats: boolean;
    cleanup: boolean;
  };
  
  error: string | null;
}

const initialState: SystemState = {
  systemStats: null,
  fileStats: null,
  loading: {
    stats: false,
    cleanup: false,
  },
  error: null,
};

// 获取系统统计信息
export const fetchSystemStats = createAsyncThunk(
  'system/fetchSystemStats',
  async (_, { rejectWithValue }) => {
    try {
      // 这里应该调用实际的API
      // const stats = await systemApi.getSystemStats();
      
      // 模拟数据
      const stats: SystemStats = {
        totalUsers: 156,
        totalSpecies: 89,
        totalMediaFiles: 1247,
        storageUsed: 2.5 * 1024 * 1024 * 1024, // 2.5GB
        storageLimit: 10 * 1024 * 1024 * 1024, // 10GB
      };
      
      return stats;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '获取系统统计失败');
    }
  }
);

// 获取文件统计信息
export const fetchFileStats = createAsyncThunk(
  'system/fetchFileStats',
  async (_, { rejectWithValue }) => {
    try {
      // 这里应该调用实际的API
      // const stats = await systemApi.getFileStats();
      
      // 模拟数据
      const stats: FileStats = {
        totalFiles: 1247,
        totalSize: 2.5 * 1024 * 1024 * 1024, // 2.5GB
        imageCount: 456,
        videoCount: 123,
        audioCount: 668,
      };
      
      return stats;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '获取文件统计失败');
    }
  }
);

// 清理临时文件
export const cleanupTempFiles = createAsyncThunk(
  'system/cleanupTempFiles',
  async (maxAgeHours: number = 24, { rejectWithValue }) => {
    try {
      // 这里应该调用实际的API
      // const result = await systemApi.cleanupTempFiles(maxAgeHours);
      
      // 模拟结果
      const result = {
        deletedFiles: 15,
        freedSpace: 125 * 1024 * 1024, // 125MB
      };
      
      return result;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '清理临时文件失败');
    }
  }
);

// 清理孤立文件
export const cleanupOrphanedFiles = createAsyncThunk(
  'system/cleanupOrphanedFiles',
  async (_, { rejectWithValue }) => {
    try {
      // 这里应该调用实际的API
      // const result = await systemApi.cleanupOrphanedFiles();
      
      // 模拟结果
      const result = {
        deletedFiles: 8,
        freedSpace: 67 * 1024 * 1024, // 67MB
      };
      
      return result;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '清理孤立文件失败');
    }
  }
);

const systemSlice = createSlice({
  name: 'system',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取系统统计
      .addCase(fetchSystemStats.pending, (state) => {
        state.loading.stats = true;
        state.error = null;
      })
      .addCase(fetchSystemStats.fulfilled, (state, action) => {
        state.loading.stats = false;
        state.systemStats = action.payload;
      })
      .addCase(fetchSystemStats.rejected, (state, action) => {
        state.loading.stats = false;
        state.error = action.payload as string;
      })
      // 获取文件统计
      .addCase(fetchFileStats.fulfilled, (state, action) => {
        state.fileStats = action.payload;
      })
      .addCase(fetchFileStats.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      // 清理文件
      .addCase(cleanupTempFiles.pending, (state) => {
        state.loading.cleanup = true;
      })
      .addCase(cleanupTempFiles.fulfilled, (state) => {
        state.loading.cleanup = false;
      })
      .addCase(cleanupTempFiles.rejected, (state, action) => {
        state.loading.cleanup = false;
        state.error = action.payload as string;
      })
      .addCase(cleanupOrphanedFiles.pending, (state) => {
        state.loading.cleanup = true;
      })
      .addCase(cleanupOrphanedFiles.fulfilled, (state) => {
        state.loading.cleanup = false;
      })
      .addCase(cleanupOrphanedFiles.rejected, (state, action) => {
        state.loading.cleanup = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError } = systemSlice.actions;
export default systemSlice.reducer;
