# 热力图网格大小调整

## 问题描述
用户反馈热力图方块的宽高比例不合理，显得过于狭长，影响视觉效果。

## 解决方案

### 1. 调整基础网格大小
- **原来**: 0.5° (基础网格大小)
- **现在**: 0.3° (基础网格大小)

### 2. 优化各缩放级别的倍数
调整了各个缩放级别的网格大小倍数，使热力块的宽高比例更加合理：

| 缩放级别 | 原倍数 | 新倍数 | 实际网格大小 | 说明 |
|---------|--------|--------|-------------|------|
| 1-4级   | ×4.0   | ×6.0   | 1.8°        | 大洲级别 |
| 5-6级   | ×2.0   | ×3.0   | 0.9°        | 国家级别 |
| 7-8级   | ×1.0   | ×1.5   | 0.45°       | 地区级别 |
| 9-10级  | ×0.4   | ×0.8   | 0.24°       | 城市级别 |
| 11-12级 | ×0.2   | ×0.4   | 0.12°       | 详细级别 |
| 13+级   | ×0.1   | ×0.2   | 0.06°       | 最精细级别 |

### 3. 修改的文件
- `frontend/src/components/Map/HeatmapLayer.tsx` - 核心组件
- `frontend/src/pages/HeatmapTestPage.tsx` - 测试页面
- 更新了相关文档和说明

## 预期效果
- ✅ 热力块宽高比例更加协调
- ✅ 在各个缩放级别下都有合适的显示大小
- ✅ 保持良好的视觉效果和用户体验
- ✅ 更接近专业地图应用的标准

## 测试方法
1. 访问 `http://localhost:5174/heatmap-test` 查看调整后的效果
2. 使用地图缩放功能测试不同级别下的热力块大小
3. 对比调整前后的视觉效果

## 技术细节
- 基础网格大小从 0.5° 调整为 0.3°
- 各级别倍数适当增加，确保热力块不会过小
- 保持了动态缩放的核心功能
- 维持了性能优化和用户体验
