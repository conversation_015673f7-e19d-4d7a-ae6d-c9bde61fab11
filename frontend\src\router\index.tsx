import React from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import Layout from '../components/Layout';
import HomePage from '../pages/HomePage';
import SpeciesListPage from '../pages/SpeciesListPage';
import SpeciesDetailPage from '../pages/SpeciesDetailPage';
import MapPage from '../pages/MapPage';
import HeatmapTestPage from '../pages/HeatmapTestPage';
import MapTestPage from '../pages/MapTestPage';

const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: 'species',
        element: <SpeciesListPage />,
      },
      {
        path: 'species/:id',
        element: <SpeciesDetailPage />,
      },
      {
        path: 'map',
        element: <MapPage />,
      },
      {
        path: 'heatmap-test',
        element: <HeatmapTestPage />,
      },
      {
        path: 'map-test',
        element: <MapTestPage />,
      },
    ],
  },
]);

const AppRouter: React.FC = () => {
  return <RouterProvider router={router} />;
};

export default AppRouter;
