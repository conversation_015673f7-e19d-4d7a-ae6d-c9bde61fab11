# Codecov 配置文件

coverage:
  # 覆盖率目标
  range: 70..90
  round: down
  precision: 2

  # 状态检查
  status:
    project:
      default:
        target: 75%
        threshold: 1%
        if_no_uploads: error
        if_not_found: success
        if_ci_failed: error
        
    patch:
      default:
        target: 80%
        threshold: 5%
        if_no_uploads: error
        if_not_found: success
        if_ci_failed: error

  # 忽略的文件和目录
  ignore:
    - "frontend/src/**/*.test.{js,jsx,ts,tsx}"
    - "frontend/src/**/*.spec.{js,jsx,ts,tsx}"
    - "frontend/src/**/__tests__/**"
    - "frontend/src/**/*.stories.{js,jsx,ts,tsx}"
    - "frontend/src/main.tsx"
    - "frontend/src/vite-env.d.ts"
    - "frontend/cypress/**"
    - "backend/src/**/*.test.{js,ts}"
    - "backend/src/**/*.spec.{js,ts}"
    - "backend/src/**/__tests__/**"
    - "backend/src/main.ts"
    - "backend/src/migrations/**"
    - "backend/src/seeds/**"
    - "backend/dist/**"
    - "frontend/dist/**"
    - "**/*.d.ts"
    - "**/node_modules/**"

# 标志配置
flags:
  frontend:
    paths:
      - frontend/src
    carryforward: true
    
  backend:
    paths:
      - backend/src
    carryforward: true

# 评论配置
comment:
  layout: "reach,diff,flags,tree"
  behavior: default
  require_changes: false
  require_base: no
  require_head: yes
  branches:
    - main
    - develop

# 通知配置
github_checks:
  annotations: true

# 解析器配置
parsers:
  gcov:
    branch_detection:
      conditional: yes
      loop: yes
      method: no
      macro: no
