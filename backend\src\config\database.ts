import { DataSource } from 'typeorm';
import dotenv from 'dotenv';

dotenv.config();

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_DATABASE || 'marine_bio_platform',
  synchronize: process.env.NODE_ENV === 'development', // 仅在开发环境自动同步
  logging: process.env.NODE_ENV === 'development',
  entities: [__dirname + '/../models/*.{ts,js}'],
  migrations: [__dirname + '/../migrations/*.{ts,js}'],
  subscribers: [__dirname + '/../subscribers/*.{ts,js}'],
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

// 初始化数据库连接
export const initializeDatabase = async () => {
  try {
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');
    
    // 检查PostGIS扩展
    const result = await AppDataSource.query(
      "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'postgis')"
    );
    
    if (!result[0].exists) {
      console.log('⚠️  PostGIS扩展未安装，请手动安装: CREATE EXTENSION postgis;');
    } else {
      console.log('✅ PostGIS扩展已启用');
    }
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    process.exit(1);
  }
};
