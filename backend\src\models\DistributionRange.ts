import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { IsNotEmpty } from 'class-validator';
import { Species } from './Species';

@Entity('distribution_ranges')
export class DistributionRange {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // PostGIS几何字段，存储多边形数据
  @Column('geometry', {
    spatialFeatureType: 'MultiPolygon',
    srid: 4326,
    nullable: true
  })
  geometry?: any;

  @Column({ nullable: true })
  kmlFilePath?: string; // KML文件路径

  @ManyToOne(() => Species, species => species.distributionRanges, {
    onDelete: 'CASCADE'
  })
  @JoinColumn({ name: 'species_id' })
  species: Species;

  @CreateDateColumn()
  createdAt: Date;
}
