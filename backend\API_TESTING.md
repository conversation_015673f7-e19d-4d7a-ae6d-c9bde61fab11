# API 测试指南

## 基础信息

- **基础URL**: `http://localhost:3001/api`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`

## 认证相关 API

### 1. 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "username": "admin",
      "email": "<EMAIL>",
      "roles": [{"name": "admin", "description": "系统管理员"}]
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": "7d"
  }
}
```

### 2. 获取用户信息
```http
GET /api/auth/profile
Authorization: Bearer <token>
```

### 3. 修改密码
```http
POST /api/auth/change-password
Authorization: Bearer <token>
Content-Type: application/json

{
  "oldPassword": "admin123",
  "newPassword": "newpassword123"
}
```

## 物种相关 API

### 1. 搜索物种
```http
GET /api/species/search?keyword=鲸鱼&page=1&limit=10
```

**查询参数**:
- `keyword`: 关键词搜索
- `conservationStatus`: 保护状态
- `kingdom`, `phylum`, `class`, `order`, `family`, `genus`: 分类学筛选
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)
- `sortBy`: 排序字段 (chineseName, latinName, createdAt)
- `sortOrder`: 排序方向 (ASC, DESC)

### 2. 获取物种详情
```http
GET /api/species/{id}/details
```

### 3. 创建物种 (需要认证)
```http
POST /api/species
Authorization: Bearer <token>
Content-Type: application/json

{
  "chineseName": "蓝鲸",
  "englishName": "Blue Whale",
  "latinName": "Balaenoptera musculus",
  "conservationStatus": "EN",
  "description": "世界上最大的动物",
  "classification": {
    "kingdom": "动物界",
    "phylum": "脊索动物门",
    "class": "哺乳纲",
    "order": "鲸目",
    "family": "须鲸科",
    "genus": "须鲸属",
    "species": "蓝鲸"
  }
}
```

### 4. 获取统计信息
```http
GET /api/species/stats/taxonomy
GET /api/species/stats/conservation
```

## 媒体文件 API

### 1. 上传图片 (需要认证)
```http
POST /api/media/species/{speciesId}/images
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <image_file>
title: "图片标题"
description: "图片描述"
isRepresentative: "true"
```

### 2. 上传音频 (需要认证)
```http
POST /api/media/species/{speciesId}/audio
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <audio_file>
recordingLocation: "南海"
recordingTime: "2024-01-01T10:00:00Z"
behaviorDescription: "觅食行为"
latitude: "20.123456"
longitude: "110.123456"
```

### 3. 获取物种媒体文件
```http
GET /api/media/species/{speciesId}/media?type=image
GET /api/media/species/{speciesId}/audio
```

## 地理数据 API

### 1. 获取所有分布数据
```http
GET /api/geography/distributions
```

### 2. 获取音频采集点
```http
GET /api/geography/collection-points
GET /api/geography/collection-points?minLng=100&minLat=20&maxLng=120&maxLat=40
```

### 3. 上传KML文件 (需要认证)
```http
POST /api/geography/species/{speciesId}/kml
Authorization: Bearer <token>
Content-Type: multipart/form-data

kmlFile: <kml_file>
```

### 4. 根据位置查找物种
```http
GET /api/geography/species-by-location?longitude=110.123&latitude=20.123
```

## 文件管理 API (管理员)

### 1. 获取存储统计
```http
GET /api/files/stats
Authorization: Bearer <admin_token>
```

### 2. 清理临时文件
```http
POST /api/files/cleanup/temp?maxAgeHours=24
Authorization: Bearer <admin_token>
```

### 3. 清理孤立文件
```http
POST /api/files/cleanup/orphaned
Authorization: Bearer <admin_token>
```

## 测试流程建议

### 1. 基础测试
1. 启动服务器: `pnpm run dev`
2. 检查健康状态: `GET /health`
3. 查看API信息: `GET /api`

### 2. 认证测试
1. 使用默认管理员账户登录
2. 获取JWT令牌
3. 测试受保护的端点

### 3. 物种数据测试
1. 创建测试物种
2. 搜索和筛选测试
3. 获取详情测试

### 4. 文件上传测试
1. 上传图片文件
2. 上传音频文件
3. 上传KML文件

### 5. 地理数据测试
1. 查看分布数据
2. 查看采集点
3. 位置查询测试

## 常见错误码

- `400`: 请求参数错误
- `401`: 未授权 (需要登录)
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 使用工具

推荐使用以下工具进行API测试:
- **Postman**: 图形化API测试工具
- **curl**: 命令行工具
- **Thunder Client**: VS Code扩展
- **Insomnia**: 轻量级API客户端

## 示例curl命令

```bash
# 登录获取token
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 搜索物种
curl -X GET "http://localhost:3001/api/species/search?keyword=鲸鱼"

# 创建物种 (需要替换<token>)
curl -X POST http://localhost:3001/api/species \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"chineseName":"测试物种","latinName":"Test species"}'
```
