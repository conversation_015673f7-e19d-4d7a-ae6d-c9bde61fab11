-- 插入默认角色
INSERT INTO roles (id, name, description, permissions) VALUES
(gen_random_uuid(), 'admin', 'System Administrator', '{"all": true}'::jsonb),
(gen_random_uuid(), 'editor', 'Content Editor', '{"species": {"read": true, "write": true}, "media": {"read": true, "write": true}}'::jsonb),
(gen_random_uuid(), 'viewer', 'Viewer', '{"species": {"read": true}, "media": {"read": true}}'::jsonb);

-- 创建默认管理员用户 (密码: admin123)
WITH new_user AS (
    INSERT INTO users (id, username, email, "passwordHash", status, "createdAt", "updatedAt")
    VALUES (gen_random_uuid(), 'admin', '<EMAIL>', '$2b$10$4W1iVQCQGGfrGPaAHAQXJ.OpeaOxtEUFgajrpJbh6BoyZmNXGzECO', 'active', NOW(), NOW())
    RETURNING id
)
INSERT INTO user_roles (user_id, role_id)
SELECT new_user.id, roles.id
FROM new_user, roles
WHERE roles.name = 'admin';
