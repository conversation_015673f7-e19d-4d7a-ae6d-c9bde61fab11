import * as fs from 'fs/promises';
import * as path from 'path';
import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { MediaFile, AudioFile, DistributionRange } from '../models';

export class FileCleanupService {
  private mediaRepository: Repository<MediaFile>;
  private audioRepository: Repository<AudioFile>;
  private distributionRepository: Repository<DistributionRange>;

  constructor() {
    this.mediaRepository = AppDataSource.getRepository(MediaFile);
    this.audioRepository = AppDataSource.getRepository(AudioFile);
    this.distributionRepository = AppDataSource.getRepository(DistributionRange);
  }

  /**
   * 清理临时文件
   */
  async cleanupTempFiles(maxAgeHours: number = 24): Promise<number> {
    const tempDir = path.join(process.env.UPLOAD_DIR || 'uploads', 'temp');
    let cleanedCount = 0;

    try {
      const files = await fs.readdir(tempDir);
      const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);

      for (const file of files) {
        const filePath = path.join(tempDir, file);
        
        try {
          const stats = await fs.stat(filePath);
          
          if (stats.mtime < cutoffTime) {
            await fs.unlink(filePath);
            cleanedCount++;
            console.log(`已清理临时文件: ${file}`);
          }
        } catch (error) {
          console.error(`清理文件 ${file} 时出错:`, error);
        }
      }
    } catch (error) {
      console.error('清理临时文件时出错:', error);
    }

    return cleanedCount;
  }

  /**
   * 清理孤立文件（数据库中不存在记录的文件）
   */
  async cleanupOrphanedFiles(): Promise<number> {
    let cleanedCount = 0;

    // 清理媒体文件
    cleanedCount += await this.cleanupOrphanedMediaFiles();
    
    // 清理音频文件
    cleanedCount += await this.cleanupOrphanedAudioFiles();
    
    // 清理KML文件
    cleanedCount += await this.cleanupOrphanedKMLFiles();

    return cleanedCount;
  }

  /**
   * 清理孤立的媒体文件
   */
  private async cleanupOrphanedMediaFiles(): Promise<number> {
    let cleanedCount = 0;
    const speciesDir = path.join(process.env.UPLOAD_DIR || 'uploads', 'species');

    try {
      const speciesDirs = await fs.readdir(speciesDir);

      for (const speciesId of speciesDirs) {
        const speciesPath = path.join(speciesDir, speciesId);
        const stat = await fs.stat(speciesPath);
        
        if (!stat.isDirectory()) continue;

        // 检查图片目录
        const imagesDir = path.join(speciesPath, 'images');
        if (await this.directoryExists(imagesDir)) {
          cleanedCount += await this.cleanupMediaFilesInDirectory(imagesDir, 'image', speciesId);
        }

        // 检查视频目录
        const videosDir = path.join(speciesPath, 'videos');
        if (await this.directoryExists(videosDir)) {
          cleanedCount += await this.cleanupMediaFilesInDirectory(videosDir, 'video', speciesId);
        }
      }
    } catch (error) {
      console.error('清理孤立媒体文件时出错:', error);
    }

    return cleanedCount;
  }

  /**
   * 清理孤立的音频文件
   */
  private async cleanupOrphanedAudioFiles(): Promise<number> {
    let cleanedCount = 0;
    const speciesDir = path.join(process.env.UPLOAD_DIR || 'uploads', 'species');

    try {
      const speciesDirs = await fs.readdir(speciesDir);

      for (const speciesId of speciesDirs) {
        const audioDir = path.join(speciesDir, speciesId, 'audio');
        
        if (await this.directoryExists(audioDir)) {
          const files = await fs.readdir(audioDir);

          for (const file of files) {
            const filePath = path.join(audioDir, file);
            
            // 检查数据库中是否存在该文件记录
            const audioFile = await this.audioRepository.findOne({
              where: { filePath },
            });

            if (!audioFile) {
              try {
                await fs.unlink(filePath);
                cleanedCount++;
                console.log(`已清理孤立音频文件: ${filePath}`);
              } catch (error) {
                console.error(`删除音频文件 ${filePath} 时出错:`, error);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('清理孤立音频文件时出错:', error);
    }

    return cleanedCount;
  }

  /**
   * 清理孤立的KML文件
   */
  private async cleanupOrphanedKMLFiles(): Promise<number> {
    let cleanedCount = 0;
    const kmlDir = path.join(process.env.UPLOAD_DIR || 'uploads', 'kml');

    try {
      if (await this.directoryExists(kmlDir)) {
        const files = await fs.readdir(kmlDir);

        for (const file of files) {
          const filePath = path.join(kmlDir, file);
          
          // 检查数据库中是否存在该文件记录
          const distributionRange = await this.distributionRepository.findOne({
            where: { kmlFilePath: filePath },
          });

          if (!distributionRange) {
            try {
              await fs.unlink(filePath);
              cleanedCount++;
              console.log(`已清理孤立KML文件: ${filePath}`);
            } catch (error) {
              console.error(`删除KML文件 ${filePath} 时出错:`, error);
            }
          }
        }
      }
    } catch (error) {
      console.error('清理孤立KML文件时出错:', error);
    }

    return cleanedCount;
  }

  /**
   * 清理指定目录中的媒体文件
   */
  private async cleanupMediaFilesInDirectory(
    directory: string,
    fileType: 'image' | 'video',
    speciesId: string
  ): Promise<number> {
    let cleanedCount = 0;

    try {
      const files = await fs.readdir(directory);

      for (const file of files) {
        const filePath = path.join(directory, file);
        
        // 检查数据库中是否存在该文件记录
        const mediaFile = await this.mediaRepository.findOne({
          where: { filePath, fileType },
        });

        if (!mediaFile) {
          try {
            await fs.unlink(filePath);
            cleanedCount++;
            console.log(`已清理孤立${fileType}文件: ${filePath}`);
          } catch (error) {
            console.error(`删除${fileType}文件 ${filePath} 时出错:`, error);
          }
        }
      }
    } catch (error) {
      console.error(`清理目录 ${directory} 中的${fileType}文件时出错:`, error);
    }

    return cleanedCount;
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<any> {
    const uploadDir = process.env.UPLOAD_DIR || 'uploads';
    
    const stats = {
      totalSize: 0,
      fileCount: 0,
      directories: {
        species: { size: 0, count: 0 },
        kml: { size: 0, count: 0 },
        temp: { size: 0, count: 0 },
      },
    };

    try {
      // 统计物种文件
      const speciesDir = path.join(uploadDir, 'species');
      if (await this.directoryExists(speciesDir)) {
        const speciesStats = await this.getDirectoryStats(speciesDir);
        stats.directories.species = speciesStats;
        stats.totalSize += speciesStats.size;
        stats.fileCount += speciesStats.count;
      }

      // 统计KML文件
      const kmlDir = path.join(uploadDir, 'kml');
      if (await this.directoryExists(kmlDir)) {
        const kmlStats = await this.getDirectoryStats(kmlDir);
        stats.directories.kml = kmlStats;
        stats.totalSize += kmlStats.size;
        stats.fileCount += kmlStats.count;
      }

      // 统计临时文件
      const tempDir = path.join(uploadDir, 'temp');
      if (await this.directoryExists(tempDir)) {
        const tempStats = await this.getDirectoryStats(tempDir);
        stats.directories.temp = tempStats;
        stats.totalSize += tempStats.size;
        stats.fileCount += tempStats.count;
      }
    } catch (error) {
      console.error('获取存储统计信息时出错:', error);
    }

    return stats;
  }

  /**
   * 获取目录统计信息
   */
  private async getDirectoryStats(directory: string): Promise<{ size: number; count: number }> {
    let totalSize = 0;
    let fileCount = 0;

    try {
      const items = await fs.readdir(directory, { withFileTypes: true });

      for (const item of items) {
        const itemPath = path.join(directory, item.name);

        if (item.isDirectory()) {
          const subStats = await this.getDirectoryStats(itemPath);
          totalSize += subStats.size;
          fileCount += subStats.count;
        } else if (item.isFile()) {
          const stats = await fs.stat(itemPath);
          totalSize += stats.size;
          fileCount++;
        }
      }
    } catch (error) {
      console.error(`获取目录 ${directory} 统计信息时出错:`, error);
    }

    return { size: totalSize, count: fileCount };
  }

  /**
   * 检查目录是否存在
   */
  private async directoryExists(directory: string): Promise<boolean> {
    try {
      const stats = await fs.stat(directory);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }

  /**
   * 运行完整的清理任务
   */
  async runCleanupTask(): Promise<{ tempFiles: number; orphanedFiles: number; stats: any }> {
    console.log('开始运行文件清理任务...');

    const tempFilesCleanedCount = await this.cleanupTempFiles();
    console.log(`清理了 ${tempFilesCleanedCount} 个临时文件`);

    const orphanedFilesCleanedCount = await this.cleanupOrphanedFiles();
    console.log(`清理了 ${orphanedFilesCleanedCount} 个孤立文件`);

    const storageStats = await this.getStorageStats();
    console.log('存储统计信息:', storageStats);

    console.log('文件清理任务完成');

    return {
      tempFiles: tempFilesCleanedCount,
      orphanedFiles: orphanedFilesCleanedCount,
      stats: storageStats,
    };
  }
}
