import request from 'supertest';
import { DataSource } from 'typeorm';
import path from 'path';
import fs from 'fs';
import app from '../../app';
import { getTestDataSource, createTestUser, createTestSpecies } from '../../__tests__/utils/database';
import { generateTestToken, expectSuccessResponse, expectErrorResponse, cleanupTestFiles } from '../../__tests__/utils/helpers';
import { User } from '../../models/User';
import { Species } from '../../models/Species';
import { AudioFile } from '../../models/AudioFile';
import { ImageFile } from '../../models/ImageFile';

describe('FileController', () => {
  let dataSource: DataSource;
  let testUser: User;
  let editorUser: User;
  let testSpecies: Species;
  let uploadedFiles: string[] = [];

  beforeAll(async () => {
    dataSource = getTestDataSource();
    
    // 确保上传目录存在
    const uploadDir = path.join(process.cwd(), 'uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
  });

  beforeEach(async () => {
    // 创建测试用户和物种
    const roleRepository = dataSource.getRepository('Role');
    const viewerRole = await roleRepository.findOne({ where: { name: 'viewer' } });
    const editorRole = await roleRepository.findOne({ where: { name: 'editor' } });

    testUser = await createTestUser(dataSource, {
      username: 'testuser',
      roles: viewerRole ? [viewerRole] : [],
    });

    editorUser = await createTestUser(dataSource, {
      username: 'editor',
      roles: editorRole ? [editorRole] : [],
    });

    testSpecies = await createTestSpecies(dataSource);
  });

  afterEach(async () => {
    // 清理上传的测试文件
    await cleanupTestFiles(uploadedFiles);
    uploadedFiles = [];
  });

  describe('POST /api/files/upload', () => {
    it('应该成功上传文件', async () => {
      const token = generateTestToken(testUser);
      
      // 创建测试文件
      const testFilePath = path.join(__dirname, 'test-file.txt');
      fs.writeFileSync(testFilePath, 'test content');
      uploadedFiles.push(testFilePath);

      const response = await request(app)
        .post('/api/files/upload')
        .set('Authorization', `Bearer ${token}`)
        .attach('file', testFilePath)
        .expect(200);

      expectSuccessResponse(response);
      expect(response.body.data).toHaveProperty('originalName');
      expect(response.body.data).toHaveProperty('path');
      expect(response.body.data).toHaveProperty('size');
      expect(response.body.data).toHaveProperty('mimeType');
      expect(response.body.data).toHaveProperty('url');

      // 记录上传的文件以便清理
      uploadedFiles.push(response.body.data.path);
    });

    it('应该验证文件大小限制', async () => {
      const token = generateTestToken(testUser);
      
      // 创建超大文件 (假设限制是10MB)
      const largeFilePath = path.join(__dirname, 'large-file.txt');
      const largeContent = 'x'.repeat(11 * 1024 * 1024); // 11MB
      fs.writeFileSync(largeFilePath, largeContent);
      uploadedFiles.push(largeFilePath);

      const response = await request(app)
        .post('/api/files/upload')
        .set('Authorization', `Bearer ${token}`)
        .attach('file', largeFilePath)
        .expect(400);

      expectErrorResponse(response);
      expect(response.body.error.message).toContain('文件大小');
    });

    it('应该要求认证', async () => {
      const testFilePath = path.join(__dirname, 'test-file.txt');
      fs.writeFileSync(testFilePath, 'test content');
      uploadedFiles.push(testFilePath);

      const response = await request(app)
        .post('/api/files/upload')
        .attach('file', testFilePath)
        .expect(401);

      expectErrorResponse(response);
    });

    it('应该验证文件存在', async () => {
      const token = generateTestToken(testUser);

      const response = await request(app)
        .post('/api/files/upload')
        .set('Authorization', `Bearer ${token}`)
        .expect(400);

      expectErrorResponse(response, '请选择要上传的文件');
    });
  });

  describe('POST /api/files/upload/audio', () => {
    it('应该成功上传音频文件', async () => {
      const token = generateTestToken(testUser);
      
      // 创建模拟音频文件
      const audioFilePath = path.join(__dirname, 'test-audio.wav');
      fs.writeFileSync(audioFilePath, 'fake audio content');
      uploadedFiles.push(audioFilePath);

      const response = await request(app)
        .post('/api/files/upload/audio')
        .set('Authorization', `Bearer ${token}`)
        .attach('file', audioFilePath)
        .expect(200);

      expectSuccessResponse(response);
      expect(response.body.data.mimeType).toContain('audio');
      uploadedFiles.push(response.body.data.path);
    });

    it('应该拒绝非音频文件', async () => {
      const token = generateTestToken(testUser);
      
      const textFilePath = path.join(__dirname, 'test.txt');
      fs.writeFileSync(textFilePath, 'not audio');
      uploadedFiles.push(textFilePath);

      const response = await request(app)
        .post('/api/files/upload/audio')
        .set('Authorization', `Bearer ${token}`)
        .attach('file', textFilePath)
        .expect(400);

      expectErrorResponse(response);
      expect(response.body.error.message).toContain('音频文件');
    });
  });

  describe('POST /api/files/upload/image', () => {
    it('应该成功上传图片文件', async () => {
      const token = generateTestToken(testUser);
      
      // 创建模拟图片文件
      const imageFilePath = path.join(__dirname, 'test-image.jpg');
      fs.writeFileSync(imageFilePath, 'fake image content');
      uploadedFiles.push(imageFilePath);

      const response = await request(app)
        .post('/api/files/upload/image')
        .set('Authorization', `Bearer ${token}`)
        .attach('file', imageFilePath)
        .expect(200);

      expectSuccessResponse(response);
      expect(response.body.data.mimeType).toContain('image');
      uploadedFiles.push(response.body.data.path);
    });

    it('应该拒绝非图片文件', async () => {
      const token = generateTestToken(testUser);
      
      const textFilePath = path.join(__dirname, 'test.txt');
      fs.writeFileSync(textFilePath, 'not image');
      uploadedFiles.push(textFilePath);

      const response = await request(app)
        .post('/api/files/upload/image')
        .set('Authorization', `Bearer ${token}`)
        .attach('file', textFilePath)
        .expect(400);

      expectErrorResponse(response);
      expect(response.body.error.message).toContain('图片文件');
    });
  });

  describe('POST /api/audio-files', () => {
    it('应该成功创建音频文件记录', async () => {
      const token = generateTestToken(editorUser);
      
      const audioData = {
        speciesId: testSpecies.id,
        fileName: 'test-audio.wav',
        filePath: 'uploads/audio/test-audio.wav',
        fileSize: 1024000,
        mimeType: 'audio/wav',
        recordingLocation: '测试海域',
        recordingTime: new Date().toISOString(),
        behaviorDescription: '觅食',
      };

      const response = await request(app)
        .post('/api/audio-files')
        .set('Authorization', `Bearer ${token}`)
        .send(audioData)
        .expect(201);

      expectSuccessResponse(response);
      expect(response.body.data.fileName).toBe(audioData.fileName);
      expect(response.body.data.recordingLocation).toBe(audioData.recordingLocation);
    });

    it('应该验证物种存在', async () => {
      const token = generateTestToken(editorUser);
      
      const audioData = {
        speciesId: 'nonexistent-id',
        fileName: 'test-audio.wav',
        filePath: 'uploads/audio/test-audio.wav',
        fileSize: 1024000,
        mimeType: 'audio/wav',
        recordingLocation: '测试海域',
        recordingTime: new Date().toISOString(),
        behaviorDescription: '觅食',
      };

      const response = await request(app)
        .post('/api/audio-files')
        .set('Authorization', `Bearer ${token}`)
        .send(audioData)
        .expect(404);

      expectErrorResponse(response, '物种不存在');
    });

    it('应该验证必填字段', async () => {
      const token = generateTestToken(editorUser);

      const response = await request(app)
        .post('/api/audio-files')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(400);

      expectErrorResponse(response);
    });

    it('应该要求编辑权限', async () => {
      const token = generateTestToken(testUser); // viewer权限
      
      const audioData = {
        speciesId: testSpecies.id,
        fileName: 'test-audio.wav',
        filePath: 'uploads/audio/test-audio.wav',
        fileSize: 1024000,
        mimeType: 'audio/wav',
        recordingLocation: '测试海域',
        recordingTime: new Date().toISOString(),
        behaviorDescription: '觅食',
      };

      const response = await request(app)
        .post('/api/audio-files')
        .set('Authorization', `Bearer ${token}`)
        .send(audioData)
        .expect(403);

      expectErrorResponse(response, '权限不足');
    });
  });

  describe('GET /api/files', () => {
    beforeEach(async () => {
      // 创建测试文件记录
      const audioFileRepository = dataSource.getRepository(AudioFile);
      const imageFileRepository = dataSource.getRepository(ImageFile);

      await audioFileRepository.save({
        fileName: 'test-audio.wav',
        filePath: 'uploads/audio/test-audio.wav',
        fileSize: 1024000,
        mimeType: 'audio/wav',
        recordingLocation: '测试海域',
        recordingTime: new Date(),
        behaviorDescription: '觅食',
        species: testSpecies,
        uploader: testUser,
      });

      await imageFileRepository.save({
        fileName: 'test-image.jpg',
        filePath: 'uploads/images/test-image.jpg',
        fileSize: 512000,
        mimeType: 'image/jpeg',
        description: '测试图片',
        imageType: 'representative',
        species: testSpecies,
        uploader: testUser,
      });
    });

    it('应该返回文件列表', async () => {
      const token = generateTestToken(testUser);

      const response = await request(app)
        .get('/api/files')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expectSuccessResponse(response);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    it('应该支持文件类型筛选', async () => {
      const token = generateTestToken(testUser);

      const response = await request(app)
        .get('/api/files')
        .set('Authorization', `Bearer ${token}`)
        .query({ fileType: 'audio' })
        .expect(200);

      expectSuccessResponse(response);
      expect(response.body.data.every((file: any) => file.mimeType.startsWith('audio/'))).toBe(true);
    });

    it('应该支持物种筛选', async () => {
      const token = generateTestToken(testUser);

      const response = await request(app)
        .get('/api/files')
        .set('Authorization', `Bearer ${token}`)
        .query({ speciesId: testSpecies.id })
        .expect(200);

      expectSuccessResponse(response);
      expect(response.body.data.every((file: any) => file.speciesId === testSpecies.id)).toBe(true);
    });
  });

  describe('DELETE /api/files/:id', () => {
    let testAudioFile: AudioFile;

    beforeEach(async () => {
      const audioFileRepository = dataSource.getRepository(AudioFile);
      testAudioFile = await audioFileRepository.save({
        fileName: 'test-audio.wav',
        filePath: 'uploads/audio/test-audio.wav',
        fileSize: 1024000,
        mimeType: 'audio/wav',
        recordingLocation: '测试海域',
        recordingTime: new Date(),
        behaviorDescription: '觅食',
        species: testSpecies,
        uploader: testUser,
      });
    });

    it('应该允许文件上传者删除文件', async () => {
      const token = generateTestToken(testUser);

      const response = await request(app)
        .delete(`/api/files/${testAudioFile.id}`)
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expectSuccessResponse(response);

      // 验证文件记录已删除
      const audioFileRepository = dataSource.getRepository(AudioFile);
      const deletedFile = await audioFileRepository.findOne({ where: { id: testAudioFile.id } });
      expect(deletedFile).toBeNull();
    });

    it('应该拒绝其他用户删除文件', async () => {
      const token = generateTestToken(editorUser);

      const response = await request(app)
        .delete(`/api/files/${testAudioFile.id}`)
        .set('Authorization', `Bearer ${token}`)
        .expect(403);

      expectErrorResponse(response, '权限不足');
    });

    it('应该返回404当文件不存在时', async () => {
      const token = generateTestToken(testUser);

      const response = await request(app)
        .delete('/api/files/nonexistent-id')
        .set('Authorization', `Bearer ${token}`)
        .expect(404);

      expectErrorResponse(response, '文件不存在');
    });
  });
});
