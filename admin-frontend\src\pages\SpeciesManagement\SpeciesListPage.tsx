import React from 'react';
import { Card, Typography, Result, Button } from 'antd';
import { useNavigate } from 'react-router-dom';

const { Title } = Typography;

const SpeciesListPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>物种管理</Title>
      </div>

      <Card>
        <Result
          status="info"
          title="功能开发中"
          subTitle="物种管理功能正在开发中，敬请期待。"
          extra={
            <Button type="primary" onClick={() => navigate('/species/create')}>
              新增物种
            </Button>
          }
        />
      </Card>
    </div>
  );
};

export default SpeciesListPage;
