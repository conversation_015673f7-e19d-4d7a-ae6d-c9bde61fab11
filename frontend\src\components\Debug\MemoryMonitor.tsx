// 内存监控调试组件
import React, { useState } from 'react';
import { Card, Button, Progress, Space, Typography, Statistic, Row, Col, Switch } from 'antd';
import { useMemoryMonitor, useMemoryStatus } from '../../hooks/useMemoryMonitor';

const { Title, Text } = Typography;

interface MemoryMonitorProps {
  visible?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  compact?: boolean;
}

const MemoryMonitor: React.FC<MemoryMonitorProps> = ({
  visible = true,
  position = 'bottom-right',
  compact = false,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const memoryStatus = useMemoryStatus();
  const {
    currentMemory,
    stats,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    forceGarbageCollection,
    resetHistory,
  } = useMemoryMonitor({
    enableWarnings: true,
    enableCriticalAlerts: true,
  });

  if (!visible || process.env.NODE_ENV !== 'development') {
    return null;
  }

  const getPositionStyle = () => {
    const baseStyle: React.CSSProperties = {
      position: 'fixed',
      zIndex: 9999,
      maxWidth: isExpanded ? '400px' : '200px',
      transition: 'all 0.3s ease',
    };

    switch (position) {
      case 'top-left':
        return { ...baseStyle, top: '20px', left: '20px' };
      case 'top-right':
        return { ...baseStyle, top: '20px', right: '20px' };
      case 'bottom-left':
        return { ...baseStyle, bottom: '20px', left: '20px' };
      case 'bottom-right':
      default:
        return { ...baseStyle, bottom: '20px', right: '20px' };
    }
  };

  const getProgressColor = () => {
    switch (memoryStatus.level) {
      case 'low':
        return '#52c41a';
      case 'medium':
        return '#faad14';
      case 'high':
        return '#fa8c16';
      case 'critical':
        return '#f5222d';
      default:
        return '#1890ff';
    }
  };

  const getTrendIcon = () => {
    switch (memoryStatus.trend) {
      case 'increasing':
        return '📈';
      case 'decreasing':
        return '📉';
      case 'stable':
      default:
        return '📊';
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleGarbageCollection = () => {
    const success = forceGarbageCollection();
    if (success) {
      console.log('✅ 垃圾回收已执行');
    } else {
      console.warn('⚠️ 垃圾回收不可用');
    }
  };

  if (compact && !isExpanded) {
    return (
      <div style={getPositionStyle()}>
        <Card
          size="small"
          style={{ cursor: 'pointer' }}
          onClick={() => setIsExpanded(true)}
        >
          <Space>
            <Text strong>{memoryStatus.percentage.toFixed(1)}%</Text>
            <Text>{getTrendIcon()}</Text>
            <div
              style={{
                width: '60px',
                height: '6px',
                backgroundColor: '#f0f0f0',
                borderRadius: '3px',
                overflow: 'hidden',
              }}
            >
              <div
                style={{
                  width: `${memoryStatus.percentage}%`,
                  height: '100%',
                  backgroundColor: getProgressColor(),
                  transition: 'width 0.3s ease',
                }}
              />
            </div>
          </Space>
        </Card>
      </div>
    );
  }

  return (
    <div style={getPositionStyle()}>
      <Card
        title={
          <Space>
            <span>内存监控</span>
            {compact && (
              <Button
                type="text"
                size="small"
                onClick={() => setIsExpanded(false)}
              >
                ×
              </Button>
            )}
          </Space>
        }
        size="small"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* 当前内存使用 */}
          <div>
            <Text strong>当前使用: {memoryStatus.percentage.toFixed(1)}%</Text>
            <Progress
              percent={memoryStatus.percentage}
              strokeColor={getProgressColor()}
              size="small"
              showInfo={false}
            />
          </div>

          {/* 内存信息 */}
          {currentMemory && (
            <Row gutter={8}>
              <Col span={12}>
                <Statistic
                  title="已使用"
                  value={formatBytes(currentMemory.used)}
                  valueStyle={{ fontSize: '12px' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="总计"
                  value={formatBytes(currentMemory.total)}
                  valueStyle={{ fontSize: '12px' }}
                />
              </Col>
            </Row>
          )}

          {/* 趋势和状态 */}
          <Space>
            <Text>趋势: {getTrendIcon()}</Text>
            <Text>状态: {memoryStatus.isHealthy ? '✅' : '⚠️'}</Text>
          </Space>

          {/* 统计信息 */}
          <div>
            <Text type="secondary" style={{ fontSize: '11px' }}>
              平均: {stats.average.toFixed(1)}% | 
              最高: {stats.max.toFixed(1)}% | 
              最低: {stats.min.toFixed(1)}%
            </Text>
          </div>

          {/* 控制按钮 */}
          <Space wrap>
            <Switch
              checked={isMonitoring}
              onChange={isMonitoring ? stopMonitoring : startMonitoring}
              size="small"
            />
            <Button size="small" onClick={handleGarbageCollection}>
              GC
            </Button>
            <Button size="small" onClick={resetHistory}>
              重置
            </Button>
          </Space>
        </Space>
      </Card>
    </div>
  );
};

export default MemoryMonitor;
