import { Router } from 'express';
import { GeographyController } from '../controllers/GeographyController';
import { authenticateToken, requirePermission, optionalAuth } from '../middleware/auth';

const router = Router();
const geographyController = new GeographyController();

// 公开路由（地图数据展示）
router.get('/distributions', optionalAuth, geographyController.getAllDistributions);
router.get('/collection-points', optionalAuth, geographyController.getAudioCollectionPoints);
router.get('/species-by-location', optionalAuth, geographyController.findSpeciesByLocation);

// 需要认证的路由
router.use(authenticateToken);

// 获取特定物种的分布数据（需要地理数据读权限）
router.get(
  '/species/:speciesId/distribution',
  requirePermission('geography', 'read'),
  geographyController.getSpeciesDistribution
);

// KML文件管理（需要地理数据写权限）
router.post(
  '/species/:speciesId/kml',
  requirePermission('geography', 'write'),
  geographyController.getKMLUploadMiddleware(),
  geographyController.uploadKML
);

router.delete(
  '/distribution/:id',
  requirePermission('geography', 'write'),
  geographyController.deleteDistributionRange
);

export default router;
