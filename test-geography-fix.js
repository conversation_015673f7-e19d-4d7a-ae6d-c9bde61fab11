// 测试脚本来验证geography API修复

async function testCollectionPointsAPI() {
  try {
    console.log('测试 /api/geography/collection-points 端点...');
    
    const response = await fetch('http://localhost:3001/api/geography/collection-points');
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ collection-points API调用成功');
      console.log('返回数据结构:', {
        success: data.success,
        dataCount: data.data ? data.data.length : 0,
        sampleData: data.data ? data.data[0] : null
      });
    } else {
      console.log('❌ collection-points API调用失败，状态码:', response.status);
      console.log('错误信息:', data);
    }
  } catch (error) {
    console.log('❌ 网络错误:', error.message);
  }
}

async function testDistributionsAPI() {
  try {
    console.log('测试 /api/geography/distributions 端点...');
    
    const response = await fetch('http://localhost:3001/api/geography/distributions');
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ distributions API调用成功');
      console.log('返回数据结构:', {
        success: data.success,
        dataCount: data.data ? data.data.length : 0,
        sampleData: data.data ? data.data[0] : null
      });
    } else {
      console.log('❌ distributions API调用失败，状态码:', response.status);
      console.log('错误信息:', data);
    }
  } catch (error) {
    console.log('❌ 网络错误:', error.message);
  }
}

async function testGeographyAPI() {
  console.log('开始测试Geography API修复...\n');
  
  await testCollectionPointsAPI();
  console.log('');
  await testDistributionsAPI();
  
  console.log('\n测试完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  testGeographyAPI();
}

module.exports = { testGeographyAPI, testCollectionPointsAPI, testDistributionsAPI };