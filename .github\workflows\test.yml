name: 测试流水线

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  POSTGRES_VERSION: '14'

jobs:
  # 前端单元测试
  frontend-unit-tests:
    name: 前端单元测试
    runs-on: ubuntu-latest
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
          
      - name: 安装 pnpm
        run: npm install -g pnpm
        
      - name: 安装前端依赖
        working-directory: frontend
        run: pnpm install --frozen-lockfile
        
      - name: 运行前端单元测试
        working-directory: frontend
        run: pnpm test:ci
        
      - name: 上传前端覆盖率报告
        uses: codecov/codecov-action@v3
        with:
          file: frontend/coverage/lcov.info
          flags: frontend
          name: frontend-coverage
          
  # 后端单元测试
  backend-unit-tests:
    name: 后端单元测试
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: marine_bio_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json
          
      - name: 安装 pnpm
        run: npm install -g pnpm
        
      - name: 安装后端依赖
        working-directory: backend
        run: pnpm install --frozen-lockfile
        
      - name: 设置测试环境变量
        working-directory: backend
        run: |
          echo "NODE_ENV=test" >> .env.test
          echo "DB_HOST=localhost" >> .env.test
          echo "DB_PORT=5432" >> .env.test
          echo "DB_USERNAME=test_user" >> .env.test
          echo "DB_PASSWORD=test_password" >> .env.test
          echo "DB_DATABASE=marine_bio_test" >> .env.test
          echo "JWT_SECRET=test-jwt-secret-for-ci" >> .env.test
          
      - name: 运行后端单元测试
        working-directory: backend
        run: pnpm test:ci
        env:
          NODE_ENV: test
          DB_HOST: localhost
          DB_PORT: 5432
          DB_USERNAME: test_user
          DB_PASSWORD: test_password
          DB_DATABASE: marine_bio_test
          JWT_SECRET: test-jwt-secret-for-ci
          
      - name: 上传后端覆盖率报告
        uses: codecov/codecov-action@v3
        with:
          file: backend/coverage/lcov.info
          flags: backend
          name: backend-coverage

  # E2E测试
  e2e-tests:
    name: 端到端测试
    runs-on: ubuntu-latest
    needs: [frontend-unit-tests, backend-unit-tests]
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: marine_bio_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 安装 pnpm
        run: npm install -g pnpm
        
      - name: 安装前端依赖
        working-directory: frontend
        run: pnpm install --frozen-lockfile
        
      - name: 安装后端依赖
        working-directory: backend
        run: pnpm install --frozen-lockfile
        
      - name: 构建前端
        working-directory: frontend
        run: pnpm build
        
      - name: 设置后端环境变量
        working-directory: backend
        run: |
          echo "NODE_ENV=test" >> .env.test
          echo "DB_HOST=localhost" >> .env.test
          echo "DB_PORT=5432" >> .env.test
          echo "DB_USERNAME=test_user" >> .env.test
          echo "DB_PASSWORD=test_password" >> .env.test
          echo "DB_DATABASE=marine_bio_test" >> .env.test
          echo "JWT_SECRET=test-jwt-secret-for-ci" >> .env.test
          echo "PORT=3001" >> .env.test
          
      - name: 启动后端服务
        working-directory: backend
        run: |
          pnpm build
          nohup pnpm start &
          sleep 10
        env:
          NODE_ENV: test
          DB_HOST: localhost
          DB_PORT: 5432
          DB_USERNAME: test_user
          DB_PASSWORD: test_password
          DB_DATABASE: marine_bio_test
          JWT_SECRET: test-jwt-secret-for-ci
          PORT: 3001
          
      - name: 启动前端服务
        working-directory: frontend
        run: |
          nohup pnpm preview --port 5173 &
          sleep 5
          
      - name: 等待服务启动
        run: |
          npx wait-on http://localhost:3001/health
          npx wait-on http://localhost:5173
          
      - name: 运行 Cypress E2E 测试
        working-directory: frontend
        run: pnpm test:e2e
        env:
          CYPRESS_baseUrl: http://localhost:5173
          CYPRESS_API_BASE_URL: http://localhost:3001/api
          
      - name: 上传 Cypress 截图
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: cypress-screenshots
          path: frontend/cypress/screenshots
          
      - name: 上传 Cypress 视频
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: cypress-videos
          path: frontend/cypress/videos

  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 安装 pnpm
        run: npm install -g pnpm
        
      - name: 安装前端依赖
        working-directory: frontend
        run: pnpm install --frozen-lockfile
        
      - name: 安装后端依赖
        working-directory: backend
        run: pnpm install --frozen-lockfile
        
      - name: 前端代码检查
        working-directory: frontend
        run: pnpm lint
        
      - name: 后端代码检查
        working-directory: backend
        run: pnpm lint
        
      - name: 前端类型检查
        working-directory: frontend
        run: pnpm type-check
        
      - name: 后端类型检查
        working-directory: backend
        run: pnpm type-check

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 运行 Trivy 漏洞扫描
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          
      - name: 上传 Trivy 扫描结果
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'
          
      - name: 前端依赖安全审计
        working-directory: frontend
        run: npm audit --audit-level=high
        
      - name: 后端依赖安全审计
        working-directory: backend
        run: npm audit --audit-level=high

  # 构建测试
  build-test:
    name: 构建测试
    runs-on: ubuntu-latest
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 安装 pnpm
        run: npm install -g pnpm
        
      - name: 安装前端依赖
        working-directory: frontend
        run: pnpm install --frozen-lockfile
        
      - name: 安装后端依赖
        working-directory: backend
        run: pnpm install --frozen-lockfile
        
      - name: 构建前端
        working-directory: frontend
        run: pnpm build
        
      - name: 构建后端
        working-directory: backend
        run: pnpm build
        
      - name: 上传构建产物
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: |
            frontend/dist
            backend/dist
