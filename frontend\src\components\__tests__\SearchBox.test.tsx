import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, mockUIState } from '../../__tests__/utils/test-utils';
import SearchBox from '../SearchBox';

describe('SearchBox', () => {
  const mockOnSearch = jest.fn();
  const mockOnSelect = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  it('应该渲染搜索框', () => {
    render(
      <SearchBox 
        onSearch={mockOnSearch}
        onSelect={mockOnSelect}
      />
    );
    
    expect(screen.getByPlaceholderText('搜索物种名称...')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('应该支持自定义占位符', () => {
    render(
      <SearchBox 
        placeholder="自定义搜索..."
        onSearch={mockOnSearch}
      />
    );
    
    expect(screen.getByPlaceholderText('自定义搜索...')).toBeInTheDocument();
  });

  it('应该处理搜索输入', async () => {
    const user = userEvent.setup();
    render(<SearchBox onSearch={mockOnSearch} />);
    
    const searchInput = screen.getByPlaceholderText('搜索物种名称...');
    
    await user.type(searchInput, '蓝鲸');
    
    expect(searchInput).toHaveValue('蓝鲸');
  });

  it('应该在按Enter时触发搜索', async () => {
    const user = userEvent.setup();
    render(<SearchBox onSearch={mockOnSearch} />);
    
    const searchInput = screen.getByPlaceholderText('搜索物种名称...');
    
    await user.type(searchInput, '蓝鲸');
    await user.keyboard('{Enter}');
    
    expect(mockOnSearch).toHaveBeenCalledWith('蓝鲸');
  });

  it('应该在点击搜索按钮时触发搜索', async () => {
    const user = userEvent.setup();
    render(<SearchBox onSearch={mockOnSearch} />);
    
    const searchInput = screen.getByPlaceholderText('搜索物种名称...');
    const searchButton = screen.getByRole('button');
    
    await user.type(searchInput, '座头鲸');
    await user.click(searchButton);
    
    expect(mockOnSearch).toHaveBeenCalledWith('座头鲸');
  });

  it('应该忽略空白搜索', async () => {
    const user = userEvent.setup();
    render(<SearchBox onSearch={mockOnSearch} />);
    
    const searchInput = screen.getByPlaceholderText('搜索物种名称...');
    
    await user.type(searchInput, '   ');
    await user.keyboard('{Enter}');
    
    expect(mockOnSearch).not.toHaveBeenCalled();
  });

  it('应该显示搜索历史', async () => {
    const user = userEvent.setup();
    
    // 预设搜索历史
    const preloadedState = {
      ui: {
        ...mockUIState,
        searchHistory: ['蓝鲸', '座头鲸', '虎鲸']
      }
    };
    
    render(
      <SearchBox 
        onSearch={mockOnSearch}
        showHistory={true}
      />,
      { preloadedState }
    );
    
    const searchInput = screen.getByPlaceholderText('搜索物种名称...');
    
    // 点击输入框以显示下拉菜单
    await user.click(searchInput);
    
    await waitFor(() => {
      expect(screen.getByText('蓝鲸')).toBeInTheDocument();
      expect(screen.getByText('座头鲸')).toBeInTheDocument();
      expect(screen.getByText('虎鲸')).toBeInTheDocument();
    });
  });

  it('应该支持选择搜索历史项', async () => {
    const user = userEvent.setup();
    
    const preloadedState = {
      ui: {
        ...mockUIState,
        searchHistory: ['蓝鲸', '座头鲸']
      }
    };
    
    render(
      <SearchBox 
        onSearch={mockOnSearch}
        onSelect={mockOnSelect}
        showHistory={true}
      />,
      { preloadedState }
    );
    
    const searchInput = screen.getByPlaceholderText('搜索物种名称...');
    
    // 点击输入框显示历史
    await user.click(searchInput);
    
    // 点击历史项
    await waitFor(() => {
      const historyItem = screen.getByText('蓝鲸');
      return user.click(historyItem);
    });
    
    expect(mockOnSelect).toHaveBeenCalledWith('蓝鲸');
    expect(mockOnSearch).toHaveBeenCalledWith('蓝鲸');
  });

  it('应该支持清除搜索历史', async () => {
    const user = userEvent.setup();
    
    const preloadedState = {
      ui: {
        ...mockUIState,
        searchHistory: ['蓝鲸', '座头鲸']
      }
    };
    
    render(
      <SearchBox 
        onSearch={mockOnSearch}
        showHistory={true}
      />,
      { preloadedState }
    );
    
    const searchInput = screen.getByPlaceholderText('搜索物种名称...');
    
    // 点击输入框显示历史
    await user.click(searchInput);
    
    // 点击清除历史按钮
    await waitFor(async () => {
      const clearButton = screen.getByText('清除搜索历史');
      await user.click(clearButton);
    });
    
    // 验证历史已清除（这里需要检查Redux action是否被调用）
    expect(searchInput).toHaveValue('');
  });

  it('应该支持allowClear功能', async () => {
    const user = userEvent.setup();
    render(
      <SearchBox 
        onSearch={mockOnSearch}
        allowClear={true}
      />
    );
    
    const searchInput = screen.getByPlaceholderText('搜索物种名称...');
    
    // 输入文本
    await user.type(searchInput, '蓝鲸');
    expect(searchInput).toHaveValue('蓝鲸');
    
    // 查找并点击清除按钮
    const clearButton = screen.getByRole('button', { name: /clear/ });
    await user.click(clearButton);
    
    expect(searchInput).toHaveValue('');
  });

  it('应该限制搜索历史数量', async () => {
    const user = userEvent.setup();
    
    // 创建超过限制的搜索历史
    const longHistory = Array.from({ length: 15 }, (_, i) => `物种${i + 1}`);
    
    const preloadedState = {
      ui: {
        ...mockUIState,
        searchHistory: longHistory
      }
    };
    
    render(
      <SearchBox 
        onSearch={mockOnSearch}
        showHistory={true}
        maxHistoryItems={5}
      />,
      { preloadedState }
    );
    
    const searchInput = screen.getByPlaceholderText('搜索物种名称...');
    
    // 点击输入框显示历史
    await user.click(searchInput);
    
    // 应该只显示前5个历史项
    await waitFor(() => {
      expect(screen.getByText('物种1')).toBeInTheDocument();
      expect(screen.getByText('物种5')).toBeInTheDocument();
      expect(screen.queryByText('物种6')).not.toBeInTheDocument();
    });
  });

  it('应该支持不同尺寸', () => {
    const { rerender } = render(
      <SearchBox 
        onSearch={mockOnSearch}
        size="small"
      />
    );
    
    let searchInput = screen.getByPlaceholderText('搜索物种名称...');
    expect(searchInput).toHaveClass('ant-input-sm');
    
    rerender(
      <SearchBox 
        onSearch={mockOnSearch}
        size="large"
      />
    );
    
    searchInput = screen.getByPlaceholderText('搜索物种名称...');
    expect(searchInput).toHaveClass('ant-input-lg');
  });

  it('应该处理键盘导航', async () => {
    const user = userEvent.setup();
    
    const preloadedState = {
      ui: {
        ...mockUIState,
        searchHistory: ['蓝鲸', '座头鲸', '虎鲸']
      }
    };
    
    render(
      <SearchBox 
        onSearch={mockOnSearch}
        showHistory={true}
      />,
      { preloadedState }
    );
    
    const searchInput = screen.getByPlaceholderText('搜索物种名称...');
    
    // 聚焦输入框
    await user.click(searchInput);
    
    // 使用方向键导航
    await user.keyboard('{ArrowDown}');
    await user.keyboard('{ArrowDown}');
    await user.keyboard('{Enter}');
    
    // 应该选择第二个历史项
    expect(mockOnSelect).toHaveBeenCalledWith('座头鲸');
  });

  it('应该在禁用历史时不显示历史', async () => {
    const user = userEvent.setup();
    
    const preloadedState = {
      ui: {
        ...mockUIState,
        searchHistory: ['蓝鲸', '座头鲸']
      }
    };
    
    render(
      <SearchBox 
        onSearch={mockOnSearch}
        showHistory={false}
      />,
      { preloadedState }
    );
    
    const searchInput = screen.getByPlaceholderText('搜索物种名称...');
    
    // 点击输入框
    await user.click(searchInput);
    
    // 不应该显示历史项
    expect(screen.queryByText('蓝鲸')).not.toBeInTheDocument();
    expect(screen.queryByText('座头鲸')).not.toBeInTheDocument();
  });
});
