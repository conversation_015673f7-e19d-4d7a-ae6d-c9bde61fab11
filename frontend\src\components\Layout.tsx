import React from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import {
  Layout as AntLayout,
  Menu,
  Typography,
  Space
} from 'antd';
import {
  HomeOutlined,
  SearchOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import Breadcrumb from './Breadcrumb';
import ErrorBoundary from './ErrorBoundary';

const { Header, Content, Footer } = AntLayout;
const { Text } = Typography;

const Layout: React.FC = () => {
  const location = useLocation();

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: <Link to="/" style={{ fontFamily: '"Microsoft YaHei", "SimHei", sans-serif' }}>首页</Link>,
    },
    {
      key: '/species',
      icon: <SearchOutlined />,
      label: <Link to="/species" style={{ fontFamily: '"Microsoft YaHei", "SimHei", sans-serif' }}>数据查询</Link>,
    },
    {
      key: '/map',
      icon: <GlobalOutlined />,
      label: <Link to="/map" style={{ fontFamily: '"Microsoft YaHei", "SimHei", sans-serif' }}>监测分布</Link>,
    },
  ];

  return (
    <AntLayout style={{ minHeight: '100vh' }}>
      <Header style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        background: '#1e3a8a',
        borderBottom: '3px solid #1d4ed8',
        padding: '0 24px',
        height: '80px'
      }}>
        {/* 政府网站标准Logo和标题 */}
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Link to="/" style={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}>
            <div style={{
              width: '60px',
              height: '60px',
              background: 'white',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '16px',
              color: '#1e3a8a',
              fontSize: '24px',
              fontWeight: 'bold',
              border: '2px solid #1d4ed8'
            }}>
              国徽
            </div>
            <div>
              <Text strong style={{
                fontSize: '18px',
                color: 'white',
                display: 'block',
                fontFamily: '"Microsoft YaHei", "SimHei", sans-serif'
              }}>
                国家海洋生物声学数据平台
              </Text>
              <Text style={{
                fontSize: '12px',
                color: 'rgba(255,255,255,0.8)',
                fontFamily: '"Microsoft YaHei", "SimHei", sans-serif'
              }}>
                National Marine Biological Acoustic Data Platform
              </Text>
            </div>
          </Link>
        </div>

        {/* 政府网站标准导航菜单 */}
        <Menu
          mode="horizontal"
          selectedKeys={[location.pathname]}
          items={menuItems}
          style={{
            border: 'none',
            background: 'transparent',
            flex: 1,
            justifyContent: 'center',
            fontSize: '14px'
          }}
          theme="dark"
        />

        {/* 政府网站标准右侧信息 */}
        <div style={{
          color: 'rgba(255,255,255,0.9)',
          fontSize: '12px',
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          fontFamily: '"Microsoft YaHei", "SimHei", sans-serif'
        }}>
          <span>服务热线：400-123-4567</span>
          <span>|</span>
          <span>政务公开</span>
        </div>
      </Header>

      <Content style={{ background: '#f5f5f5' }}>
        <Breadcrumb />
        <ErrorBoundary>
          <Outlet />
        </ErrorBoundary>
      </Content>

      <Footer style={{
        textAlign: 'center',
        background: '#374151',
        color: 'rgba(255, 255, 255, 0.9)',
        padding: '30px 24px 20px',
        borderTop: '1px solid #4b5563'
      }}>
        <div style={{ marginBottom: '20px' }}>
          <Space size="large" wrap>
            <Link to="/" style={{
              color: 'rgba(255, 255, 255, 0.8)',
              textDecoration: 'none',
              fontFamily: '"Microsoft YaHei", "SimHei", sans-serif',
              fontSize: '14px'
            }}>
              首页
            </Link>
            <Link to="/species" style={{
              color: 'rgba(255, 255, 255, 0.8)',
              textDecoration: 'none',
              fontFamily: '"Microsoft YaHei", "SimHei", sans-serif',
              fontSize: '14px'
            }}>
              数据查询
            </Link>
            <Link to="/map" style={{
              color: 'rgba(255, 255, 255, 0.8)',
              textDecoration: 'none',
              fontFamily: '"Microsoft YaHei", "SimHei", sans-serif',
              fontSize: '14px'
            }}>
              监测分布
            </Link>
            <a href="#" style={{
              color: 'rgba(255, 255, 255, 0.8)',
              textDecoration: 'none',
              fontFamily: '"Microsoft YaHei", "SimHei", sans-serif',
              fontSize: '14px'
            }}>
              政务公开
            </a>
            <a href="#" style={{
              color: 'rgba(255, 255, 255, 0.8)',
              textDecoration: 'none',
              fontFamily: '"Microsoft YaHei", "SimHei", sans-serif',
              fontSize: '14px'
            }}>
              联系我们
            </a>
          </Space>
        </div>
        <div style={{
          borderTop: '1px solid rgba(255,255,255,0.2)',
          paddingTop: '16px',
          marginBottom: '8px'
        }}>
          <div style={{
            fontSize: '14px',
            fontWeight: '500',
            marginBottom: '6px',
            fontFamily: '"Microsoft YaHei", "SimHei", sans-serif'
          }}>
            国家海洋生物声学数据平台 ©2024
          </div>
          <div style={{
            fontSize: '12px',
            color: 'rgba(255, 255, 255, 0.7)',
            fontFamily: '"Microsoft YaHei", "SimHei", sans-serif'
          }}>
            主办单位：国家海洋局 | 技术支持：海洋科学研究院
          </div>
        </div>
        <div style={{
          fontSize: '11px',
          color: 'rgba(255, 255, 255, 0.6)',
          fontFamily: '"Microsoft YaHei", "SimHei", sans-serif'
        }}>
          网站标识码：1234567890 | ICP备案号：京ICP备12345678号 | 京公网安备 11010802012345号
        </div>
      </Footer>
    </AntLayout>
  );
};

export default Layout;
