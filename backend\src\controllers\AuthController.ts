import { Request, Response } from 'express';
import { AuthService, LoginCredentials, RegisterData } from '../services/AuthService';
import { validate } from 'class-validator';
import { User } from '../models';

export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  /**
   * 用户登录
   */
  login = async (req: Request, res: Response) => {
    try {
      const { username, password } = req.body;

      if (!username || !password) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_CREDENTIALS',
            message: '用户名和密码不能为空',
          },
        });
      }

      const credentials: LoginCredentials = { username, password };
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');

      const result = await this.authService.login(credentials, ipAddress, userAgent);

      res.json({
        success: true,
        data: {
          user: {
            id: result.user.id,
            username: result.user.username,
            email: result.user.email,
            status: result.user.status,
            roles: result.user.roles.map(role => ({
              id: role.id,
              name: role.name,
              description: role.description,
            })),
            lastLogin: result.user.lastLogin,
          },
          token: result.token,
          expiresIn: result.expiresIn,
        },
        message: '登录成功',
      });
    } catch (error: any) {
      res.status(401).json({
        success: false,
        error: {
          code: 'LOGIN_FAILED',
          message: error.message,
        },
      });
    }
  };

  /**
   * 用户注册
   */
  register = async (req: Request, res: Response) => {
    try {
      const { username, email, password, roleNames } = req.body;

      if (!username || !password) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_REQUIRED_FIELDS',
            message: '用户名和密码不能为空',
          },
        });
      }

      // 密码强度验证
      if (password.length < 6) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'WEAK_PASSWORD',
            message: '密码长度至少6个字符',
          },
        });
      }

      const registerData: RegisterData = {
        username,
        email,
        password,
        roleNames,
      };

      const user = await this.authService.register(registerData);

      res.status(201).json({
        success: true,
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          status: user.status,
          roles: user.roles.map(role => ({
            id: role.id,
            name: role.name,
            description: role.description,
          })),
        },
        message: '注册成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'REGISTRATION_FAILED',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取当前用户信息
   */
  getProfile = async (req: Request, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未授权访问',
          },
        });
      }

      const user = await this.authService.getUserProfile(req.user.id);

      res.json({
        success: true,
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          status: user.status,
          roles: user.roles.map(role => ({
            id: role.id,
            name: role.name,
            description: role.description,
            permissions: role.permissions,
          })),
          lastLogin: user.lastLogin,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'GET_PROFILE_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 更新用户信息
   */
  updateProfile = async (req: Request, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未授权访问',
          },
        });
      }

      const { email } = req.body;
      const updateData: Partial<User> = {};

      if (email !== undefined) {
        updateData.email = email;
      }

      const user = await this.authService.updateUserProfile(req.user.id, updateData);

      res.json({
        success: true,
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          status: user.status,
        },
        message: '用户信息更新成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'UPDATE_PROFILE_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 修改密码
   */
  changePassword = async (req: Request, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未授权访问',
          },
        });
      }

      const { oldPassword, newPassword } = req.body;

      if (!oldPassword || !newPassword) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_PASSWORDS',
            message: '原密码和新密码不能为空',
          },
        });
      }

      if (newPassword.length < 6) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'WEAK_PASSWORD',
            message: '新密码长度至少6个字符',
          },
        });
      }

      await this.authService.changePassword(req.user.id, oldPassword, newPassword);

      res.json({
        success: true,
        message: '密码修改成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'CHANGE_PASSWORD_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 重置密码（管理员功能）
   */
  resetPassword = async (req: Request, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未授权访问',
          },
        });
      }

      const { userId } = req.params;
      const { newPassword } = req.body;

      if (!newPassword) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_PASSWORD',
            message: '新密码不能为空',
          },
        });
      }

      if (newPassword.length < 6) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'WEAK_PASSWORD',
            message: '新密码长度至少6个字符',
          },
        });
      }

      await this.authService.resetPassword(userId, newPassword, req.user.id);

      res.json({
        success: true,
        message: '密码重置成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'RESET_PASSWORD_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 用户登出
   */
  logout = async (req: Request, res: Response) => {
    try {
      // JWT是无状态的，登出主要是客户端删除token
      // 这里可以记录登出日志
      res.json({
        success: true,
        message: '登出成功',
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'LOGOUT_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 验证token有效性
   */
  verifyToken = async (req: Request, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: '无效的访问令牌',
          },
        });
      }

      res.json({
        success: true,
        data: {
          valid: true,
          user: {
            id: req.user.id,
            username: req.user.username,
            roles: req.user.roles.map(role => role.name),
          },
        },
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'VERIFY_TOKEN_ERROR',
          message: error.message,
        },
      });
    }
  };
}
