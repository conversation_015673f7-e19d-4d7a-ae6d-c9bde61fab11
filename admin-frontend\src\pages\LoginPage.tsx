import React, { useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Typography,
  Space,
  message
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  LoginOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks';
import { login, clearError } from '../store/slices/authSlice';

const { Title, Text } = Typography;

interface LoginForm {
  username: string;
  password: string;
}

const LoginPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, error, isAuthenticated } = useAppSelector(state => state.auth);

  useEffect(() => {
    // 如果已经登录，重定向到仪表盘
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  useEffect(() => {
    // 清除之前的错误
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  const handleLogin = async (values: LoginForm) => {
    try {
      const result = await dispatch(login(values)).unwrap();
      message.success('登录成功！');
      navigate('/dashboard');
    } catch (error) {
      message.error(error as string);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '24px'
    }}>
      <Card 
        style={{ 
          width: '100%', 
          maxWidth: '400px',
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <div style={{
            width: '64px',
            height: '64px',
            background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 16px',
            color: 'white',
            fontSize: '24px'
          }}>
            <SettingOutlined />
          </div>
          <Title level={2} style={{ margin: 0, marginBottom: '8px' }}>
            管理端登录
          </Title>
          <Text type="secondary">
            海洋生物声音平台 - 管理系统
          </Text>
        </div>

        <Form
          name="login"
          onFinish={handleLogin}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入管理员用户名' },
              { min: 3, message: '用户名至少3个字符' }
            ]}
          >
            <Input 
              prefix={<UserOutlined />} 
              placeholder="管理员用户名"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入管理员密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />} 
              placeholder="管理员密码"
            />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              icon={<LoginOutlined />}
              block
              style={{ height: '48px', fontSize: '16px' }}
            >
              登录管理系统
            </Button>
          </Form.Item>
        </Form>

        {/* 演示账户信息 */}
        <Card 
          size="small" 
          style={{ 
            marginTop: '24px', 
            background: '#f6ffed',
            border: '1px solid #b7eb8f'
          }}
        >
          <Text strong style={{ color: '#52c41a' }}>演示管理员账户：</Text>
          <br />
          <Text code>用户名: admin</Text>
          <br />
          <Text code>密码: admin123</Text>
        </Card>

        {/* 安全提示 */}
        <Card 
          size="small" 
          style={{ 
            marginTop: '16px', 
            background: '#fff7e6',
            border: '1px solid #ffd591'
          }}
        >
          <Text style={{ color: '#fa8c16', fontSize: '12px' }}>
            <strong>安全提示：</strong>
            请确保您有管理员权限才能访问此系统。
            所有操作都将被记录在系统日志中。
          </Text>
        </Card>
      </Card>
    </div>
  );
};

export default LoginPage;
