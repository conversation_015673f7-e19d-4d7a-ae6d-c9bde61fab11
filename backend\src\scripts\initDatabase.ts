import 'reflect-metadata';
import { DataSource } from 'typeorm';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';

// 加载环境变量
dotenv.config();

// 创建数据源配置
const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_DATABASE || 'marine_bio_platform',
  synchronize: false, // 使用迁移而不是同步
  logging: true,
  entities: [__dirname + '/../models/*.{ts,js}'],
  migrations: [__dirname + '/../migrations/*.{ts,js}'],
});

async function initializeDatabase() {
  try {
    console.log('🔄 正在连接数据库...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    console.log('🔄 正在运行数据库迁移...');
    await AppDataSource.runMigrations();
    console.log('✅ 数据库迁移完成');

    // 创建默认管理员用户
    console.log('🔄 正在创建默认管理员用户...');
    await createDefaultAdmin();
    console.log('✅ 默认管理员用户创建完成');

    console.log('🎉 数据库初始化完成！');
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    process.exit(1);
  } finally {
    await AppDataSource.destroy();
  }
}

async function createDefaultAdmin() {
  // 检查是否已存在管理员用户
  const existingAdmin = await AppDataSource.query(
    "SELECT id FROM users WHERE username = 'admin'"
  );

  if (existingAdmin.length > 0) {
    console.log('ℹ️  管理员用户已存在，跳过创建');
    return;
  }

  // 获取管理员角色ID
  const adminRole = await AppDataSource.query(
    "SELECT id FROM roles WHERE name = 'admin'"
  );

  if (adminRole.length === 0) {
    throw new Error('管理员角色不存在');
  }

  // 创建管理员用户
  const hashedPassword = await bcrypt.hash('admin123', 10);
  const adminUserId = await AppDataSource.query(
    `INSERT INTO users (username, email, "passwordHash", status) 
     VALUES ('admin', '<EMAIL>', $1, 'active') 
     RETURNING id`,
    [hashedPassword]
  );

  // 分配管理员角色
  await AppDataSource.query(
    'INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2)',
    [adminUserId[0].id, adminRole[0].id]
  );

  console.log('✅ 默认管理员用户创建成功');
  console.log('   用户名: admin');
  console.log('   密码: admin123');
  console.log('   ⚠️  请在生产环境中修改默认密码！');
}

// 运行初始化
if (require.main === module) {
  initializeDatabase();
}

export { initializeDatabase };
