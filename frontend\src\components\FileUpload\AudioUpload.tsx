import React, { useState } from 'react';
import { Card, Form, Input, DatePicker, Select, Space, Button, message, Typography } from 'antd';
import { SoundOutlined, SaveOutlined } from '@ant-design/icons';
import BaseUpload from './BaseUpload';
import { BaseAudioPlayer } from '../AudioPlayer';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

interface AudioUploadProps {
  speciesId?: string;
  onUploadComplete?: (files: any[]) => void;
}

interface AudioMetadata {
  recordingLocation: string;
  recordingTime: string;
  behaviorDescription: string;
  recordingConditions?: string;
  equipment?: string;
  notes?: string;
}

const AudioUpload: React.FC<AudioUploadProps> = ({
  speciesId,
  onUploadComplete,
}) => {
  const [form] = Form.useForm();
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [previewFile, setPreviewFile] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  // 音频文件上传成功
  const handleUploadSuccess = (response: any) => {
    if (response.success && response.data) {
      setUploadedFiles(prev => [...prev, response.data]);
      
      // 设置预览文件
      if (!previewFile) {
        setPreviewFile(response.data.url);
      }
    }
  };

  // 上传错误处理
  const handleUploadError = (error: string) => {
    message.error(error);
  };

  // 保存音频文件信息
  const handleSave = async (values: AudioMetadata) => {
    if (uploadedFiles.length === 0) {
      message.warning('请先上传音频文件');
      return;
    }

    setSaving(true);
    try {
      const promises = uploadedFiles.map(file => 
        fetch(`${import.meta.env.VITE_API_BASE_URL}/audio-files`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({
            speciesId,
            fileName: file.originalName,
            filePath: file.path,
            fileSize: file.size,
            mimeType: file.mimeType,
            ...values,
          }),
        })
      );

      const responses = await Promise.all(promises);
      const results = await Promise.all(responses.map(r => r.json()));
      
      const successCount = results.filter(r => r.success).length;
      
      if (successCount === uploadedFiles.length) {
        message.success(`成功保存 ${successCount} 个音频文件`);
        onUploadComplete?.(results.map(r => r.data));
        
        // 重置表单
        form.resetFields();
        setUploadedFiles([]);
        setPreviewFile(null);
      } else {
        message.warning(`保存了 ${successCount}/${uploadedFiles.length} 个文件`);
      }
    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div>
      <Card
        title={
          <Space>
            <SoundOutlined />
            音频文件上传
          </Space>
        }
        style={{ marginBottom: '24px' }}
      >
        <BaseUpload
          accept=".wav,.mp3,.flac,.aac,audio/*"
          maxSize={50}
          maxCount={5}
          onUploadSuccess={handleUploadSuccess}
          onUploadError={handleUploadError}
          uploadUrl={`${import.meta.env.VITE_API_BASE_URL}/files/upload/audio`}
        />

        {/* 音频预览 */}
        {previewFile && (
          <div style={{ marginTop: '24px' }}>
            <Title level={5}>音频预览</Title>
            <BaseAudioPlayer
              audioUrl={previewFile}
              title="预览音频"
              showWaveform={true}
              height={80}
            />
          </div>
        )}
      </Card>

      {/* 音频信息表单 */}
      {uploadedFiles.length > 0 && (
        <Card
          title="音频信息"
          extra={
            <Text type="secondary">
              已上传 {uploadedFiles.length} 个文件
            </Text>
          }
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSave}
          >
            <Form.Item
              name="recordingLocation"
              label="录音地点"
              rules={[{ required: true, message: '请输入录音地点' }]}
            >
              <Input placeholder="例如：南海、东海、太平洋等" />
            </Form.Item>

            <Form.Item
              name="recordingTime"
              label="录音时间"
              rules={[{ required: true, message: '请选择录音时间' }]}
            >
              <DatePicker 
                showTime 
                placeholder="选择录音时间"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name="behaviorDescription"
              label="行为描述"
              rules={[{ required: true, message: '请描述录音时的行为' }]}
            >
              <Select placeholder="选择或输入行为描述">
                <Option value="觅食">觅食</Option>
                <Option value="交流">交流</Option>
                <Option value="警告">警告</Option>
                <Option value="求偶">求偶</Option>
                <Option value="导航">导航</Option>
                <Option value="其他">其他</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="recordingConditions"
              label="录音条件"
            >
              <TextArea 
                rows={3}
                placeholder="描述录音时的环境条件，如天气、海况、深度等"
              />
            </Form.Item>

            <Form.Item
              name="equipment"
              label="录音设备"
            >
              <Input placeholder="录音设备型号和参数" />
            </Form.Item>

            <Form.Item
              name="notes"
              label="备注"
            >
              <TextArea 
                rows={2}
                placeholder="其他需要说明的信息"
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button 
                  type="primary" 
                  htmlType="submit"
                  icon={<SaveOutlined />}
                  loading={saving}
                >
                  保存音频信息
                </Button>
                <Button onClick={() => form.resetFields()}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>
      )}
    </div>
  );
};

export default AudioUpload;
