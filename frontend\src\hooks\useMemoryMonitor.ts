// React Hook for memory monitoring
import { useState, useEffect, useCallback } from 'react';
import { memoryManager } from '../utils/memoryManager';
import type {
  MemoryInfo,
  UseMemoryMonitorOptions,
  MemoryMonitorState,
  MemoryStatus
} from '../types/memory';

export const useMemoryMonitor = (options: UseMemoryMonitorOptions = {}) => {
  const {
    enableWarnings = true,
    enableCriticalAlerts = true,
    onWarning,
    onCritical,
  } = options;

  const [state, setState] = useState<MemoryMonitorState>({
    currentMemory: null,
    history: [],
    stats: { average: 0, max: 0, min: 0, trend: 'stable' },
    isMonitoring: false,
  });

  // 更新状态
  const updateState = useCallback(() => {
    setState({
      currentMemory: memoryManager.getCurrentMemoryInfo(),
      history: memoryManager.getMemoryHistory(),
      stats: memoryManager.getMemoryStats(),
      isMonitoring: true,
    });
  }, []);

  // 启动监控
  const startMonitoring = useCallback(() => {
    memoryManager.start();
    updateState();
  }, [updateState]);

  // 停止监控
  const stopMonitoring = useCallback(() => {
    memoryManager.stop();
    setState(prev => ({ ...prev, isMonitoring: false }));
  }, []);

  // 强制垃圾回收
  const forceGarbageCollection = useCallback(() => {
    return memoryManager.forceGarbageCollection();
  }, []);

  // 重置历史记录
  const resetHistory = useCallback(() => {
    memoryManager.resetHistory();
    updateState();
  }, [updateState]);

  useEffect(() => {
    // 设置内存更新监听器
    const unsubscribeUpdate = memoryManager.onMemoryUpdate(updateState);

    // 设置警告监听器
    let unsubscribeWarning: (() => void) | undefined;
    if (enableWarnings) {
      unsubscribeWarning = memoryManager.onWarning((info) => {
        if (onWarning) {
          onWarning(info);
        }
      });
    }

    // 设置紧急监听器
    let unsubscribeCritical: (() => void) | undefined;
    if (enableCriticalAlerts) {
      unsubscribeCritical = memoryManager.onCritical((info) => {
        if (onCritical) {
          onCritical(info);
        }
      });
    }

    // 清理函数
    return () => {
      unsubscribeUpdate();
      if (unsubscribeWarning) unsubscribeWarning();
      if (unsubscribeCritical) unsubscribeCritical();
    };
  }, [enableWarnings, enableCriticalAlerts, onWarning, onCritical, updateState]);

  return {
    ...state,
    startMonitoring,
    stopMonitoring,
    forceGarbageCollection,
    resetHistory,
    updateState,
  };
};

// 简化版本的 Hook，只返回当前内存信息
export const useCurrentMemory = () => {
  const [currentMemory, setCurrentMemory] = useState<MemoryInfo | null>(null);

  useEffect(() => {
    const unsubscribe = memoryManager.onMemoryUpdate(setCurrentMemory);
    
    // 获取初始值
    setCurrentMemory(memoryManager.getCurrentMemoryInfo());
    
    return unsubscribe;
  }, []);

  return currentMemory;
};

// 内存状态 Hook
export const useMemoryStatus = () => {
  const [status, setStatus] = useState<MemoryStatus>({
    isHealthy: true,
    level: 'low',
    percentage: 0,
    trend: 'stable',
  });

  useEffect(() => {
    const unsubscribe = memoryManager.onMemoryUpdate((info) => {
      let level: 'low' | 'medium' | 'high' | 'critical';
      let isHealthy = true;

      if (info.percentage < 60) {
        level = 'low';
      } else if (info.percentage < 80) {
        level = 'medium';
      } else if (info.percentage < 95) {
        level = 'high';
        isHealthy = false;
      } else {
        level = 'critical';
        isHealthy = false;
      }

      setStatus({
        isHealthy,
        level,
        percentage: info.percentage,
        trend: info.trend,
      });
    });

    return unsubscribe;
  }, []);

  return status;
};
