import React, { useState, useRef, useEffect } from 'react';
import { Input, AutoComplete, Space, Tag, Typography } from 'antd';
import { SearchOutlined, CloseOutlined, HistoryOutlined } from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { addSearchHistory, clearSearchHistory } from '../store/slices/uiSlice';

const { Text } = Typography;

interface SearchBoxProps {
  placeholder?: string;
  onSearch?: (value: string) => void;
  onSelect?: (value: string) => void;
  size?: 'small' | 'middle' | 'large';
  style?: React.CSSProperties;
  allowClear?: boolean;
  showHistory?: boolean;
  maxHistoryItems?: number;
}

const SearchBox: React.FC<SearchBoxProps> = ({
  placeholder = '搜索物种名称...',
  onSearch,
  onSelect,
  size = 'middle',
  style,
  allowClear = true,
  showHistory = true,
  maxHistoryItems = 8,
}) => {
  const [searchValue, setSearchValue] = useState('');
  const [options, setOptions] = useState<Array<{ value: string; label: React.ReactNode }>>([]);
  const inputRef = useRef<any>(null);
  
  const dispatch = useAppDispatch();
  const searchHistory = useAppSelector(state => state.ui.searchHistory);

  // 构建搜索建议选项
  useEffect(() => {
    if (!searchValue.trim()) {
      // 显示搜索历史
      if (showHistory && searchHistory.length > 0) {
        const historyOptions = searchHistory.slice(0, maxHistoryItems).map(item => ({
          value: item,
          label: (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Space>
                <HistoryOutlined style={{ color: '#999' }} />
                <Text>{item}</Text>
              </Space>
            </div>
          ),
        }));

        setOptions([
          ...historyOptions,
          ...(searchHistory.length > 0 ? [{
            value: '__clear_history__',
            label: (
              <div style={{ 
                textAlign: 'center', 
                padding: '4px 0',
                borderTop: '1px solid #f0f0f0',
                marginTop: '4px'
              }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  <CloseOutlined style={{ marginRight: '4px' }} />
                  清除搜索历史
                </Text>
              </div>
            ),
          }] : []),
        ]);
      } else {
        setOptions([]);
      }
    } else {
      // TODO: 这里可以添加实时搜索建议
      setOptions([]);
    }
  }, [searchValue, searchHistory, showHistory, maxHistoryItems]);

  const handleSearch = (value: string) => {
    const trimmedValue = value.trim();
    if (trimmedValue) {
      dispatch(addSearchHistory(trimmedValue));
      onSearch?.(trimmedValue);
    }
  };

  const handleSelect = (value: string) => {
    if (value === '__clear_history__') {
      dispatch(clearSearchHistory());
      setSearchValue('');
      inputRef.current?.focus();
      return;
    }

    setSearchValue(value);
    onSelect?.(value);
    handleSearch(value);
  };

  const handleChange = (value: string) => {
    setSearchValue(value);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch(searchValue);
    }
  };

  return (
    <div style={style}>
      <AutoComplete
        ref={inputRef}
        value={searchValue}
        options={options}
        onSelect={handleSelect}
        onChange={handleChange}
        style={{ width: '100%' }}
        dropdownMatchSelectWidth={true}
      >
        <Input
          size={size}
          placeholder={placeholder}
          allowClear={allowClear}
          onPressEnter={handleKeyPress}
          suffix={
            <SearchOutlined 
              style={{ color: '#1890ff', cursor: 'pointer' }}
              onClick={() => handleSearch(searchValue)}
            />
          }
        />
      </AutoComplete>
    </div>
  );
};

// 高级搜索组件
export const AdvancedSearchBox: React.FC<{
  onSearch?: (params: any) => void;
  visible?: boolean;
  onClose?: () => void;
}> = ({ onSearch, visible, onClose }) => {
  const [searchParams, setSearchParams] = useState({
    keyword: '',
    conservationStatus: '',
    kingdom: '',
    phylum: '',
    class: '',
    order: '',
    family: '',
    genus: '',
  });

  const handleSearch = () => {
    const filteredParams = Object.fromEntries(
      Object.entries(searchParams).filter(([_, value]) => value.trim() !== '')
    );
    onSearch?.(filteredParams);
    onClose?.();
  };

  const handleReset = () => {
    setSearchParams({
      keyword: '',
      conservationStatus: '',
      kingdom: '',
      phylum: '',
      class: '',
      order: '',
      family: '',
      genus: '',
    });
  };

  if (!visible) return null;

  return (
    <div style={{
      position: 'absolute',
      top: '100%',
      left: 0,
      right: 0,
      background: '#fff',
      border: '1px solid #d9d9d9',
      borderRadius: '6px',
      padding: '16px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
      zIndex: 1000,
    }}>
      {/* 高级搜索表单内容 */}
      <Text strong>高级搜索</Text>
      {/* TODO: 添加具体的搜索表单字段 */}
    </div>
  );
};

// 搜索标签组件
export const SearchTags: React.FC<{
  tags: Array<{ key: string; label: string; value: string }>;
  onRemove?: (key: string) => void;
  onClear?: () => void;
}> = ({ tags, onRemove, onClear }) => {
  if (tags.length === 0) return null;

  return (
    <div style={{ marginBottom: '16px' }}>
      <Space wrap>
        <Text type="secondary">当前筛选:</Text>
        {tags.map(tag => (
          <Tag
            key={tag.key}
            closable
            onClose={() => onRemove?.(tag.key)}
          >
            {tag.label}: {tag.value}
          </Tag>
        ))}
        {tags.length > 1 && (
          <Tag
            color="red"
            style={{ cursor: 'pointer' }}
            onClick={onClear}
          >
            清除全部
          </Tag>
        )}
      </Space>
    </div>
  );
};

export default SearchBox;
