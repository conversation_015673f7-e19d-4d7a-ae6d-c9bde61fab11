import { Router } from 'express';
import { MediaController } from '../controllers/MediaController';
import { authenticateToken, requirePermission } from '../middleware/auth';

const router = Router();
const mediaController = new MediaController();

// 所有媒体文件路由都需要认证
router.use(authenticateToken);

// 上传文件（需要媒体写权限）
router.post(
  '/species/:speciesId/images',
  requirePermission('media', 'write'),
  mediaController.getUploadMiddleware(),
  mediaController.uploadImage
);

router.post(
  '/species/:speciesId/videos',
  requirePermission('media', 'write'),
  mediaController.getUploadMiddleware(),
  mediaController.uploadVideo
);

router.post(
  '/species/:speciesId/audio',
  requirePermission('media', 'write'),
  mediaController.getUploadMiddleware(),
  mediaController.uploadAudio
);

// 获取文件列表（需要媒体读权限）
router.get(
  '/species/:speciesId/media',
  requirePermission('media', 'read'),
  mediaController.getSpeciesMediaFiles
);

router.get(
  '/species/:speciesId/audio',
  requirePermission('media', 'read'),
  mediaController.getSpeciesAudioFiles
);

// 文件管理（需要媒体写权限）
router.delete(
  '/media/:fileId',
  requirePermission('media', 'write'),
  mediaController.deleteMediaFile
);

router.delete(
  '/audio/:fileId',
  requirePermission('media', 'write'),
  mediaController.deleteAudioFile
);

router.put(
  '/species/:speciesId/representative/:fileId',
  requirePermission('media', 'write'),
  mediaController.setRepresentativeImage
);

// 文件统计（需要媒体读权限）
router.get(
  '/species/:speciesId/stats',
  requirePermission('media', 'read'),
  mediaController.getFileStats
);

export default router;
