# 数据库设置指南

## 前置要求

1. **安装PostgreSQL** (版本 13 或更高)
2. **安装PostGIS扩展** (用于地理数据支持)

### Windows 安装

1. 下载并安装 PostgreSQL: https://www.postgresql.org/download/windows/
2. 在安装过程中，确保选择安装 PostGIS 扩展
3. 或者单独安装 PostGIS: https://postgis.net/windows_downloads/

### macOS 安装

```bash
# 使用 Homebrew
brew install postgresql postgis

# 启动 PostgreSQL 服务
brew services start postgresql
```

### Ubuntu/Debian 安装

```bash
# 安装 PostgreSQL 和 PostGIS
sudo apt update
sudo apt install postgresql postgresql-contrib postgis postgresql-13-postgis-3

# 启动服务
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## 数据库配置

### 1. 创建数据库用户

```sql
-- 连接到 PostgreSQL (使用 postgres 用户)
sudo -u postgres psql

-- 创建数据库用户
CREATE USER marine_user WITH PASSWORD 'your_password';

-- 授予创建数据库权限
ALTER USER marine_user CREATEDB;
```

### 2. 创建数据库

```sql
-- 创建数据库
CREATE DATABASE marine_bio_platform OWNER marine_user;

-- 连接到新数据库
\c marine_bio_platform

-- 启用 PostGIS 扩展
CREATE EXTENSION IF NOT EXISTS postgis;

-- 验证 PostGIS 安装
SELECT PostGIS_Version();
```

### 3. 配置环境变量

编辑 `backend/.env` 文件：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=marine_user
DB_PASSWORD=your_password
DB_DATABASE=marine_bio_platform
```

## 初始化数据库

### 方法一：使用初始化脚本（推荐）

```bash
cd backend
pnpm run db:init
```

这个命令会：
- 连接到数据库
- 运行所有迁移文件
- 创建默认角色和管理员用户
- 验证 PostGIS 扩展

### 方法二：手动运行迁移

```bash
cd backend

# 运行迁移
pnpm run db:migration:run

# 如果需要回滚迁移
pnpm run db:migration:revert
```

## 数据库结构

### 核心表

1. **users** - 用户表
2. **roles** - 角色表
3. **user_roles** - 用户角色关联表
4. **species** - 物种表
5. **taxonomic_classifications** - 分类学分类表

### 媒体文件表

1. **media_files** - 图片和视频文件表
2. **audio_files** - 音频文件表

### 地理数据表

1. **distribution_ranges** - 物种分布范围表（包含 PostGIS 几何字段）

### 系统表

1. **operation_logs** - 操作日志表
2. **audio_recognition_records** - 音频识别记录表

## 默认数据

初始化后会创建以下默认数据：

### 默认角色
- **admin**: 系统管理员（所有权限）
- **editor**: 内容编辑员（物种和媒体读写权限）
- **viewer**: 查看者（只读权限）

### 默认管理员用户
- **用户名**: admin
- **密码**: admin123
- **角色**: admin

⚠️ **重要**: 请在生产环境中立即修改默认密码！

## 验证安装

### 1. 检查数据库连接

```bash
cd backend
pnpm run dev
```

如果看到以下信息，说明数据库连接成功：
```
✅ 数据库连接成功
✅ PostGIS扩展已启用
🚀 服务器运行在 http://localhost:3001
```

### 2. 检查表结构

连接到数据库并查看表：

```sql
-- 连接到数据库
psql -h localhost -U marine_user -d marine_bio_platform

-- 查看所有表
\dt

-- 查看 PostGIS 几何字段
SELECT f_table_name, f_geometry_column, type, srid 
FROM geometry_columns;
```

## 故障排除

### 常见问题

1. **连接被拒绝**
   - 检查 PostgreSQL 服务是否运行
   - 验证端口和主机配置
   - 检查防火墙设置

2. **PostGIS 扩展未找到**
   - 确保安装了 PostGIS
   - 检查 PostgreSQL 版本兼容性

3. **权限错误**
   - 确保用户有足够的数据库权限
   - 检查文件系统权限（上传目录）

### 重置数据库

如果需要完全重置数据库：

```sql
-- 删除数据库
DROP DATABASE IF EXISTS marine_bio_platform;

-- 重新创建
CREATE DATABASE marine_bio_platform OWNER marine_user;
```

然后重新运行初始化脚本。

## 备份和恢复

### 备份数据库

```bash
pg_dump -h localhost -U marine_user marine_bio_platform > backup.sql
```

### 恢复数据库

```bash
psql -h localhost -U marine_user marine_bio_platform < backup.sql
```
