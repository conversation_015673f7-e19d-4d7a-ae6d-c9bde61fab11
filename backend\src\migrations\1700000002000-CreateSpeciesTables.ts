import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateSpeciesTables1700000002000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建物种表
    await queryRunner.createTable(
      new Table({
        name: 'species',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'chineseName',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'englishName',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'latinName',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'conservationStatus',
            type: 'varchar',
            length: '50',
            isNullable: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'representativeImagePath',
            type: 'varchar',
            length: '500',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        indices: [
          {
            name: 'IDX_SPECIES_CHINESE_NAME',
            columnNames: ['chineseName'],
          },
          {
            name: 'IDX_SPECIES_LATIN_NAME',
            columnNames: ['latinName'],
          },
          {
            name: 'IDX_SPECIES_CONSERVATION_STATUS',
            columnNames: ['conservationStatus'],
          },
        ],
      }),
      true
    );

    // 创建分类学分类表
    await queryRunner.createTable(
      new Table({
        name: 'taxonomic_classifications',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'species_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'kingdom',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'phylum',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'class',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'order',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'family',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'genus',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'species',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['species_id'],
            referencedTableName: 'species',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
        indices: [
          {
            name: 'IDX_TAXONOMIC_SPECIES_ID',
            columnNames: ['species_id'],
            isUnique: true,
          },
          {
            name: 'IDX_TAXONOMIC_KINGDOM',
            columnNames: ['kingdom'],
          },
          {
            name: 'IDX_TAXONOMIC_PHYLUM',
            columnNames: ['phylum'],
          },
          {
            name: 'IDX_TAXONOMIC_CLASS',
            columnNames: ['class'],
          },
        ],
      }),
      true
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('taxonomic_classifications');
    await queryRunner.dropTable('species');
  }
}
