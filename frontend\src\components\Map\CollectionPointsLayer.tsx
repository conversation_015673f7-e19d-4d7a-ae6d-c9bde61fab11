import React from 'react';
import { <PERSON><PERSON>, Popup } from 'react-leaflet';
import { Typo<PERSON>, Space, Button, Tag, Statistic } from 'antd';
import { SoundOutlined, EyeOutlined, EnvironmentOutlined } from '@ant-design/icons';
import L from 'leaflet';

const { Text, Title } = Typography;

interface CollectionPoint {
  id: string;
  species_id: string;
  chinese_name: string;
  latin_name: string;
  latitude: number;
  longitude: number;
  recording_location: string;
  audio_count: number;
  last_recording: string;
  file_name?: string;
}

interface CollectionPointsLayerProps {
  points: CollectionPoint[];
  onPointClick?: (point: CollectionPoint) => void;
  onSpeciesClick?: (speciesId: string) => void;
}

// 创建自定义图标
const createCustomIcon = (audioCount: number) => {
  const size = Math.min(Math.max(audioCount * 2 + 20, 25), 40);
  const color = audioCount > 10 ? '#ff4d4f' : audioCount > 5 ? '#fa8c16' : '#52c41a';
  
  return L.divIcon({
    html: `
      <div style="
        background-color: ${color};
        border: 3px solid white;
        border-radius: 50%;
        width: ${size}px;
        height: ${size}px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: ${size > 30 ? '14px' : '12px'};
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      ">
        ${audioCount}
      </div>
    `,
    className: 'custom-marker',
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2],
  });
};

const CollectionPointsLayer: React.FC<CollectionPointsLayerProps> = ({
  points,
  onPointClick,
  onSpeciesClick,
}) => {
  return (
    <>
      {points.map((point) => (
        <Marker
          key={point.id}
          position={[point.latitude, point.longitude]}
          icon={createCustomIcon(point.audio_count)}
          eventHandlers={{
            click: () => onPointClick?.(point),
          }}
        >
          <Popup>
            <div style={{ minWidth: '250px', maxWidth: '300px' }}>
              {/* 物种信息 */}
              <div style={{ marginBottom: '12px' }}>
                <Title level={5} style={{ margin: '0 0 4px 0' }}>
                  {point.chinese_name}
                </Title>
                <Text type="secondary" italic style={{ fontSize: '12px' }}>
                  {point.latin_name}
                </Text>
              </div>

              {/* 位置信息 */}
              <div style={{ marginBottom: '12px' }}>
                <Space>
                  <EnvironmentOutlined style={{ color: '#1890ff' }} />
                  <Text strong>{point.recording_location}</Text>
                </Space>
                <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                  {point.latitude.toFixed(4)}, {point.longitude.toFixed(4)}
                </div>
              </div>

              {/* 统计信息 */}
              <div style={{ marginBottom: '12px' }}>
                <Space split={<span style={{ color: '#d9d9d9' }}>|</span>}>
                  <Statistic
                    title="音频文件"
                    value={point.audio_count}
                    prefix={<SoundOutlined />}
                    valueStyle={{ fontSize: '16px' }}
                  />
                  <div>
                    <div style={{ fontSize: '12px', color: '#666' }}>最后录音</div>
                    <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                      {new Date(point.last_recording).toLocaleDateString()}
                    </div>
                  </div>
                </Space>
              </div>

              {/* 最新文件 */}
              {point.file_name && (
                <div style={{ marginBottom: '12px' }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    最新文件: {point.file_name}
                  </Text>
                </div>
              )}

              {/* 操作按钮 */}
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  type="primary"
                  size="small"
                  icon={<SoundOutlined />}
                  onClick={() => onPointClick?.(point)}
                  block
                >
                  查看音频文件
                </Button>
                <Button
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => onSpeciesClick?.(point.species_id)}
                  block
                >
                  查看物种详情
                </Button>
              </Space>
            </div>
          </Popup>
        </Marker>
      ))}
    </>
  );
};

export default CollectionPointsLayer;
