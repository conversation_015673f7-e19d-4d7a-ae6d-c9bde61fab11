import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import HomePage from '../../pages/HomePage';
import uiSlice from '../../store/slices/uiSlice';

// 创建测试用的store
const createTestStore = () => {
  return configureStore({
    reducer: {
      ui: uiSlice,
    },
  });
};

// 包装组件的辅助函数
const renderWithProviders = (component: React.ReactElement) => {
  const store = createTestStore();
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('HomePage Performance Tests', () => {
  // 性能基准测试
  describe('Rendering Performance', () => {
    test('should render HomePage within acceptable time', async () => {
      const startTime = performance.now();
      
      renderWithProviders(<HomePage />);
      
      // 等待组件完全渲染
      await waitFor(() => {
        expect(screen.getByText('观鸟热点')).toBeInTheDocument();
      });
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // 渲染时间应该小于1000ms
      expect(renderTime).toBeLessThan(1000);
      console.log(`HomePage render time: ${renderTime.toFixed(2)}ms`);
    });

    test('should handle search input changes efficiently', async () => {
      renderWithProviders(<HomePage />);
      
      const searchInput = screen.getByPlaceholderText('输入地点名称或物种...');
      
      const startTime = performance.now();
      
      // 模拟快速输入
      for (let i = 0; i < 10; i++) {
        fireEvent.change(searchInput, { target: { value: `test${i}` } });
      }
      
      const endTime = performance.now();
      const inputTime = endTime - startTime;
      
      // 输入处理时间应该小于100ms
      expect(inputTime).toBeLessThan(100);
      console.log(`Search input handling time: ${inputTime.toFixed(2)}ms`);
    });
  });

  // 内存泄漏测试
  describe('Memory Leak Tests', () => {
    test('should not create memory leaks on mount/unmount', async () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      // 多次挂载和卸载组件
      for (let i = 0; i < 5; i++) {
        const { unmount } = renderWithProviders(<HomePage />);
        
        await waitFor(() => {
          expect(screen.getByText('观鸟热点')).toBeInTheDocument();
        });
        
        unmount();
      }
      
      // 强制垃圾回收（如果可用）
      if ((global as any).gc) {
        (global as any).gc();
      }
      
      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
      const memoryIncrease = finalMemory - initialMemory;
      
      // 内存增长应该在合理范围内（小于10MB）
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
      console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    });
  });

  // 响应性测试
  describe('Responsiveness Tests', () => {
    test('should handle window resize efficiently', async () => {
      renderWithProviders(<HomePage />);
      
      const startTime = performance.now();
      
      // 模拟多次窗口大小变化
      for (let i = 0; i < 10; i++) {
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          configurable: true,
          value: 800 + i * 10,
        });
        
        fireEvent(window, new Event('resize'));
      }
      
      // 等待防抖处理完成
      await new Promise(resolve => setTimeout(resolve, 200));
      
      const endTime = performance.now();
      const resizeTime = endTime - startTime;
      
      // 窗口调整处理时间应该小于500ms
      expect(resizeTime).toBeLessThan(500);
      console.log(`Window resize handling time: ${resizeTime.toFixed(2)}ms`);
    });
  });
});

// 性能监控工具
export class PerformanceMonitor {
  private static measurements: { [key: string]: number[] } = {};

  static startMeasurement(name: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (!this.measurements[name]) {
        this.measurements[name] = [];
      }
      
      this.measurements[name].push(duration);
    };
  }

  static getAverageTime(name: string): number {
    const times = this.measurements[name];
    if (!times || times.length === 0) return 0;
    
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }

  static getReport(): { [key: string]: { average: number; count: number; min: number; max: number } } {
    const report: { [key: string]: { average: number; count: number; min: number; max: number } } = {};
    
    Object.keys(this.measurements).forEach(name => {
      const times = this.measurements[name];
      report[name] = {
        average: this.getAverageTime(name),
        count: times.length,
        min: Math.min(...times),
        max: Math.max(...times),
      };
    });
    
    return report;
  }

  static reset(): void {
    this.measurements = {};
  }
}
