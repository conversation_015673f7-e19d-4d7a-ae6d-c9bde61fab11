import React, { useEffect, useRef, useState } from 'react';
import { Card, Radio, Space, Typography, Button, Slider } from 'antd';
import { 
  BarChartOutlined, 
  LineChartOutlined,
  SettingOutlined,
  FullscreenOutlined
} from '@ant-design/icons';

const { Text, Title } = Typography;

interface AudioVisualizerProps {
  audioUrl: string;
  title?: string;
  height?: number;
  showControls?: boolean;
}

type VisualizationType = 'waveform' | 'spectrogram' | 'frequency';

const AudioVisualizer: React.FC<AudioVisualizerProps> = ({
  audioUrl,
  title,
  height = 300,
  showControls = true,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const sourceRef = useRef<AudioBufferSourceNode | null>(null);
  const animationRef = useRef<number | null>(null);

  const [visualizationType, setVisualizationType] = useState<VisualizationType>('waveform');
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioBuffer, setAudioBuffer] = useState<AudioBuffer | null>(null);
  const [sensitivity, setSensitivity] = useState(50);

  // 初始化音频上下文
  useEffect(() => {
    const initAudio = async () => {
      try {
        // 创建音频上下文
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
        
        // 创建分析器节点
        analyserRef.current = audioContextRef.current.createAnalyser();
        analyserRef.current.fftSize = 2048;
        analyserRef.current.connect(audioContextRef.current.destination);

        // 加载音频文件
        const response = await fetch(audioUrl);
        const arrayBuffer = await response.arrayBuffer();
        const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);
        setAudioBuffer(audioBuffer);

      } catch (error) {
        console.error('音频初始化失败:', error);
      }
    };

    initAudio();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [audioUrl]);

  // 绘制波形图
  const drawWaveform = (dataArray: Uint8Array) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;

    // 清除画布
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, width, height);

    // 绘制波形
    ctx.lineWidth = 2;
    ctx.strokeStyle = '#1890ff';
    ctx.beginPath();

    const sliceWidth = width / dataArray.length;
    let x = 0;

    for (let i = 0; i < dataArray.length; i++) {
      const v = (dataArray[i] / 128.0) * (sensitivity / 50);
      const y = (v * height) / 2;

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }

      x += sliceWidth;
    }

    ctx.lineTo(canvas.width, canvas.height / 2);
    ctx.stroke();
  };

  // 绘制频谱图
  const drawFrequency = (dataArray: Uint8Array) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;

    // 清除画布
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, width, height);

    const barWidth = width / dataArray.length;
    let x = 0;

    for (let i = 0; i < dataArray.length; i++) {
      const barHeight = (dataArray[i] / 255) * height * (sensitivity / 50);

      // 创建渐变色
      const gradient = ctx.createLinearGradient(0, height - barHeight, 0, height);
      gradient.addColorStop(0, '#ff4d4f');
      gradient.addColorStop(0.5, '#fa8c16');
      gradient.addColorStop(1, '#52c41a');

      ctx.fillStyle = gradient;
      ctx.fillRect(x, height - barHeight, barWidth, barHeight);

      x += barWidth;
    }
  };

  // 绘制频谱图（时频图）
  const drawSpectrogram = (dataArray: Uint8Array) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;

    // 将现有内容向左移动一个像素
    const imageData = ctx.getImageData(1, 0, width - 1, height);
    ctx.putImageData(imageData, 0, 0);

    // 在右侧绘制新的频谱列
    for (let i = 0; i < dataArray.length; i++) {
      const value = dataArray[i];
      const percent = value / 255;
      
      // 根据强度设置颜色
      const hue = (1 - percent) * 240; // 从蓝色到红色
      const saturation = 100;
      const lightness = percent * 50 + 25;
      
      ctx.fillStyle = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
      
      const y = (i / dataArray.length) * height;
      const barHeight = height / dataArray.length;
      
      ctx.fillRect(width - 1, y, 1, barHeight);
    }
  };

  // 动画循环
  const animate = () => {
    if (!analyserRef.current) return;

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    
    if (visualizationType === 'waveform') {
      analyserRef.current.getByteTimeDomainData(dataArray);
      drawWaveform(dataArray);
    } else if (visualizationType === 'frequency') {
      analyserRef.current.getByteFrequencyData(dataArray);
      drawFrequency(dataArray);
    } else if (visualizationType === 'spectrogram') {
      analyserRef.current.getByteFrequencyData(dataArray);
      drawSpectrogram(dataArray);
    }

    animationRef.current = requestAnimationFrame(animate);
  };

  // 开始播放和可视化
  const startVisualization = () => {
    if (!audioContextRef.current || !analyserRef.current || !audioBuffer) return;

    // 创建音频源
    sourceRef.current = audioContextRef.current.createBufferSource();
    sourceRef.current.buffer = audioBuffer;
    sourceRef.current.connect(analyserRef.current);

    // 开始播放
    sourceRef.current.start();
    setIsPlaying(true);

    // 开始动画
    animate();

    // 播放结束处理
    sourceRef.current.onended = () => {
      setIsPlaying(false);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  };

  // 停止播放
  const stopVisualization = () => {
    if (sourceRef.current) {
      sourceRef.current.stop();
      sourceRef.current = null;
    }
    setIsPlaying(false);
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
  };

  return (
    <Card
      title={
        <Space>
          <BarChartOutlined />
          {title || '音频可视化'}
        </Space>
      }
      extra={
        showControls && (
          <Space>
            <Button
              type="primary"
              onClick={isPlaying ? stopVisualization : startVisualization}
              disabled={!audioBuffer}
            >
              {isPlaying ? '停止' : '播放'}
            </Button>
            <Button icon={<FullscreenOutlined />} />
          </Space>
        )
      }
    >
      {/* 可视化类型选择 */}
      {showControls && (
        <div style={{ marginBottom: '16px' }}>
          <Space wrap>
            <Radio.Group
              value={visualizationType}
              onChange={(e) => setVisualizationType(e.target.value)}
              size="small"
            >
              <Radio.Button value="waveform">
                <LineChartOutlined /> 波形图
              </Radio.Button>
              <Radio.Button value="frequency">
                <BarChartOutlined /> 频谱图
              </Radio.Button>
              <Radio.Button value="spectrogram">
                <SettingOutlined /> 时频图
              </Radio.Button>
            </Radio.Group>

            <Space>
              <Text style={{ fontSize: '12px' }}>灵敏度:</Text>
              <Slider
                style={{ width: '100px' }}
                min={10}
                max={100}
                value={sensitivity}
                onChange={setSensitivity}
                tooltip={{ formatter: (value) => `${value}%` }}
              />
            </Space>
          </Space>
        </div>
      )}

      {/* 可视化画布 */}
      <div style={{ 
        background: '#000', 
        borderRadius: '4px', 
        padding: '8px',
        display: 'flex',
        justifyContent: 'center'
      }}>
        <canvas
          ref={canvasRef}
          width={800}
          height={height}
          style={{ 
            maxWidth: '100%', 
            height: `${height}px`,
            border: '1px solid #333'
          }}
        />
      </div>

      {/* 说明文字 */}
      <div style={{ marginTop: '12px' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          {visualizationType === 'waveform' && '波形图显示音频信号的时域特征'}
          {visualizationType === 'frequency' && '频谱图显示音频信号的频域特征'}
          {visualizationType === 'spectrogram' && '时频图显示音频信号随时间变化的频谱特征'}
        </Text>
      </div>
    </Card>
  );
};

export default AudioVisualizer;
