import React, { useEffect, useRef, useState } from 'react';
import { Button, Slider, Space, Typography, Tooltip } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  SoundOutlined,
  MutedOutlined,
  ReloadOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import WaveSurfer from 'wavesurfer.js';

const { Text } = Typography;

interface BaseAudioPlayerProps {
  audioUrl: string;
  title?: string;
  onPlay?: () => void;
  onPause?: () => void;
  onFinish?: () => void;
  showWaveform?: boolean;
  height?: number;
  autoPlay?: boolean;
}

const BaseAudioPlayer: React.FC<BaseAudioPlayerProps> = ({
  audioUrl,
  title,
  onPlay,
  onPause,
  onFinish,
  showWaveform = true,
  height = 80,
  autoPlay = false,
}) => {
  const waveformRef = useRef<HTMLDivElement>(null);
  const wavesurfer = useRef<WaveSurfer | null>(null);
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(0.8);
  const [isMuted, setIsMuted] = useState(false);

  // 初始化WaveSurfer
  useEffect(() => {
    if (waveformRef.current && showWaveform) {
      wavesurfer.current = WaveSurfer.create({
        container: waveformRef.current,
        waveColor: '#1890ff',
        progressColor: '#096dd9',
        cursorColor: '#722ed1',
        barWidth: 2,
        barRadius: 3,
        responsive: true,
        height: height,
        normalize: true,
        backend: 'WebAudio',
        mediaControls: false,
      });

      // 加载音频
      wavesurfer.current.load(audioUrl);

      // 事件监听
      wavesurfer.current.on('ready', () => {
        setIsLoading(false);
        setDuration(wavesurfer.current?.getDuration() || 0);
        if (autoPlay) {
          wavesurfer.current?.play();
        }
      });

      wavesurfer.current.on('play', () => {
        setIsPlaying(true);
        onPlay?.();
      });

      wavesurfer.current.on('pause', () => {
        setIsPlaying(false);
        onPause?.();
      });

      wavesurfer.current.on('finish', () => {
        setIsPlaying(false);
        onFinish?.();
      });

      wavesurfer.current.on('audioprocess', () => {
        setCurrentTime(wavesurfer.current?.getCurrentTime() || 0);
      });

      wavesurfer.current.on('seek', () => {
        setCurrentTime(wavesurfer.current?.getCurrentTime() || 0);
      });

      return () => {
        wavesurfer.current?.destroy();
      };
    }
  }, [audioUrl, showWaveform, height, autoPlay, onPlay, onPause, onFinish]);

  // 播放/暂停
  const togglePlayPause = () => {
    if (wavesurfer.current) {
      wavesurfer.current.playPause();
    }
  };

  // 停止播放
  const stop = () => {
    if (wavesurfer.current) {
      wavesurfer.current.stop();
      setIsPlaying(false);
    }
  };

  // 音量控制
  const handleVolumeChange = (value: number) => {
    const newVolume = value / 100;
    setVolume(newVolume);
    if (wavesurfer.current) {
      wavesurfer.current.setVolume(newVolume);
    }
    if (newVolume === 0) {
      setIsMuted(true);
    } else {
      setIsMuted(false);
    }
  };

  // 静音切换
  const toggleMute = () => {
    if (wavesurfer.current) {
      if (isMuted) {
        wavesurfer.current.setVolume(volume);
        setIsMuted(false);
      } else {
        wavesurfer.current.setVolume(0);
        setIsMuted(true);
      }
    }
  };

  // 格式化时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 下载音频
  const downloadAudio = () => {
    const link = document.createElement('a');
    link.href = audioUrl;
    link.download = title || 'audio.wav';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div style={{ 
      border: '1px solid #d9d9d9', 
      borderRadius: '6px', 
      padding: '16px',
      background: '#fafafa'
    }}>
      {/* 标题 */}
      {title && (
        <div style={{ marginBottom: '12px' }}>
          <Text strong>{title}</Text>
        </div>
      )}

      {/* 波形图 */}
      {showWaveform && (
        <div 
          ref={waveformRef} 
          style={{ 
            marginBottom: '16px',
            background: '#fff',
            borderRadius: '4px',
            padding: '8px'
          }}
        />
      )}

      {/* 控制面板 */}
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: '12px'
      }}>
        {/* 播放控制 */}
        <Space>
          <Button
            type="primary"
            shape="circle"
            size="large"
            icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={togglePlayPause}
            loading={isLoading}
          />
          <Button
            shape="circle"
            icon={<ReloadOutlined />}
            onClick={stop}
            disabled={isLoading}
          />
        </Space>

        {/* 时间显示 */}
        <Space>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {formatTime(currentTime)} / {formatTime(duration)}
          </Text>
        </Space>

        {/* 音量控制 */}
        <Space style={{ minWidth: '120px' }}>
          <Tooltip title={isMuted ? '取消静音' : '静音'}>
            <Button
              type="text"
              size="small"
              icon={isMuted ? <MutedOutlined /> : <SoundOutlined />}
              onClick={toggleMute}
            />
          </Tooltip>
          <Slider
            style={{ width: '80px' }}
            min={0}
            max={100}
            value={isMuted ? 0 : volume * 100}
            onChange={handleVolumeChange}
            tooltip={{ formatter: (value) => `${value}%` }}
          />
        </Space>

        {/* 下载按钮 */}
        <Tooltip title="下载音频">
          <Button
            type="text"
            icon={<DownloadOutlined />}
            onClick={downloadAudio}
          />
        </Tooltip>
      </div>
    </div>
  );
};

export default BaseAudioPlayer;
