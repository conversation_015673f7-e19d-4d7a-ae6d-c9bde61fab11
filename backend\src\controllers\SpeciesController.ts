import { Request, Response } from 'express';
import { SpeciesService, CreateSpeciesData, UpdateSpeciesData, SpeciesSearchParams } from '../services/SpeciesService';

export class SpeciesController {
  private speciesService: SpeciesService;

  constructor() {
    this.speciesService = new SpeciesService();
  }

  /**
   * 创建物种
   */
  createSpecies = async (req: Request, res: Response) => {
    try {
      const {
        chineseName,
        englishName,
        latinName,
        conservationStatus,
        description,
        classification,
      } = req.body;

      if (!chineseName || !latinName) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_REQUIRED_FIELDS',
            message: '中文名和拉丁学名不能为空',
          },
        });
      }

      const createData: CreateSpeciesData = {
        chineseName,
        englishName,
        latinName,
        conservationStatus,
        description,
        classification,
      };

      const species = await this.speciesService.createSpecies(createData);

      res.status(201).json({
        success: true,
        data: species,
        message: '物种创建成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'CREATE_SPECIES_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 更新物种
   */
  updateSpecies = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const updateData: UpdateSpeciesData = req.body;

      const species = await this.speciesService.updateSpecies(id, updateData);

      res.json({
        success: true,
        data: species,
        message: '物种更新成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'UPDATE_SPECIES_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 删除物种
   */
  deleteSpecies = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      await this.speciesService.deleteSpecies(id);

      res.json({
        success: true,
        message: '物种删除成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'DELETE_SPECIES_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取物种基本信息
   */
  getSpecies = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      const species = await this.speciesService.getSpeciesById(id);

      res.json({
        success: true,
        data: species,
      });
    } catch (error: any) {
      res.status(404).json({
        success: false,
        error: {
          code: 'SPECIES_NOT_FOUND',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取物种详情（包含媒体文件和分布信息）
   */
  getSpeciesDetails = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      const details = await this.speciesService.getSpeciesDetails(id);

      res.json({
        success: true,
        data: details,
      });
    } catch (error: any) {
      res.status(404).json({
        success: false,
        error: {
          code: 'SPECIES_NOT_FOUND',
          message: error.message,
        },
      });
    }
  };

  /**
   * 搜索和筛选物种
   */
  searchSpecies = async (req: Request, res: Response) => {
    try {
      const {
        keyword,
        conservationStatus,
        kingdom,
        phylum,
        class: className,
        order,
        family,
        genus,
        page = '1',
        limit = '20',
        sortBy = 'chineseName',
        sortOrder = 'ASC',
      } = req.query;

      const searchParams: SpeciesSearchParams = {
        keyword: keyword as string,
        conservationStatus: conservationStatus as string,
        kingdom: kingdom as string,
        phylum: phylum as string,
        class: className as string,
        order: order as string,
        family: family as string,
        genus: genus as string,
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as any,
        sortOrder: sortOrder as any,
      };

      // 验证分页参数
      if (searchParams.page! < 1) searchParams.page = 1;
      if (searchParams.limit! < 1 || searchParams.limit! > 100) searchParams.limit = 20;

      const result = await this.speciesService.searchSpecies(searchParams);

      res.json({
        success: true,
        data: result,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'SEARCH_SPECIES_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取分类学统计信息
   */
  getTaxonomyStats = async (req: Request, res: Response) => {
    try {
      const stats = await this.speciesService.getTaxonomyStats();

      res.json({
        success: true,
        data: stats,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'GET_TAXONOMY_STATS_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取保护状态统计
   */
  getConservationStats = async (req: Request, res: Response) => {
    try {
      const stats = await this.speciesService.getConservationStats();

      res.json({
        success: true,
        data: stats,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'GET_CONSERVATION_STATS_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取最近添加的物种
   */
  getRecentSpecies = async (req: Request, res: Response) => {
    try {
      const { limit = '10' } = req.query;
      const limitNum = parseInt(limit as string);

      if (limitNum < 1 || limitNum > 50) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_LIMIT',
            message: '限制数量必须在1-50之间',
          },
        });
      }

      const species = await this.speciesService.getRecentSpecies(limitNum);

      res.json({
        success: true,
        data: species,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'GET_RECENT_SPECIES_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取随机物种
   */
  getRandomSpecies = async (req: Request, res: Response) => {
    try {
      const { limit = '6' } = req.query;
      const limitNum = parseInt(limit as string);

      if (limitNum < 1 || limitNum > 20) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_LIMIT',
            message: '限制数量必须在1-20之间',
          },
        });
      }

      const species = await this.speciesService.getRandomSpecies(limitNum);

      res.json({
        success: true,
        data: species,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'GET_RANDOM_SPECIES_ERROR',
          message: error.message,
        },
      });
    }
  };
}
