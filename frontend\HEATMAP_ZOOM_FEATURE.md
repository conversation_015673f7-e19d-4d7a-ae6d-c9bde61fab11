# 热力图缩放适应性功能

## 功能概述

基于对 eBird 网站热力图的深入分析，我们实现了动态缩放适应的热力图功能。热力块的大小现在会根据地图的缩放级别自动调整，提供更好的用户体验和可视性。

## 主要改进

### 1. 动态网格大小
- **低缩放级别 (1-4)**：大网格 (基础大小 × 4.0) - 适合显示大洲级别的宏观分布
- **国家级别 (5-6)**：中大网格 (基础大小 × 2.0) - 适合显示国家级别的分布
- **地区级别 (7-8)**：基础网格 (基础大小 × 1.0) - 适合显示地区级别的分布
- **城市级别 (9-10)**：小网格 (基础大小 × 0.4) - 适合显示城市级别的详细分布
- **详细级别 (11-12)**：很小网格 (基础大小 × 0.2) - 适合显示详细的局部分布
- **最精细级别 (13+)**：最小网格 (基础大小 × 0.1) - 适合显示最精细的分布

### 2. 性能优化
- **防抖机制**：250ms 防抖延迟，避免频繁的重新渲染
- **缓存机制**：使用 `useMemo` 缓存网格计算结果
- **渐进式更新**：缩放过程中保持视觉连续性

### 3. 用户体验改进
- **平滑过渡**：缩放时热力块大小平滑变化
- **响应性**：快速响应用户的缩放操作
- **视觉一致性**：保持与 eBird 类似的专业级热力图效果

## 技术实现

### 核心函数

```typescript
// 动态网格大小计算
const calculateGridSize = (zoom: number, baseGridSize: number = 0.5): number => {
  if (zoom <= 4) return baseGridSize * 4.0;    // 大洲级别
  if (zoom <= 6) return baseGridSize * 2.0;    // 国家级别  
  if (zoom <= 8) return baseGridSize * 1.0;    // 地区级别
  if (zoom <= 10) return baseGridSize * 0.4;   // 城市级别
  if (zoom <= 12) return baseGridSize * 0.2;   // 详细级别
  return baseGridSize * 0.1;                   // 最精细级别
};

// 防抖处理
const debouncedUpdateRef = useRef<((zoom: number) => void) | null>(null);
```

### 状态管理

```typescript
const [currentZoom, setCurrentZoom] = useState<number>(map.getZoom());
const [isUpdating, setIsUpdating] = useState<boolean>(false);

// 动态网格大小计算
const currentGridSize = useMemo(() => {
  return calculateGridSize(currentZoom, config.gridSize);
}, [currentZoom, config.gridSize]);

// 缓存网格计算结果
const gridCells = useMemo(() => {
  if (!data || data.length === 0) return [];
  return createGridCells(data, currentGridSize);
}, [data, currentGridSize, createGridCells]);
```

## 使用方法

### 基本使用
```typescript
<HeatmapLayer
  data={heatmapData}
  onCellClick={handlePointClick}
  options={{
    gridSize: 0.3,        // 基础网格大小，会根据缩放动态调整
    minOpacity: 0.2,
    maxOpacity: 0.8,
    gradient: {
      // 颜色渐变配置
    }
  }}
/>
```

### 配置选项
- `gridSize`: 基础网格大小（度数），默认 0.5°
- `minOpacity`: 最小透明度，默认 0.2
- `maxOpacity`: 最大透明度，默认 0.8
- `gradient`: 颜色渐变映射

## 测试方法

1. 打开应用主页
2. 观察热力图的初始状态
3. 使用地图的缩放控件或鼠标滚轮进行缩放
4. 观察热力块大小如何随缩放级别动态调整
5. 验证缩放过程中的平滑过渡效果

## 预期效果

- **低缩放级别**：显示大范围的热力分布，网格较大，适合宏观观察
- **高缩放级别**：显示精细的热力分布，网格较小，适合详细分析
- **缩放过程**：平滑的视觉过渡，无明显的性能卡顿
- **用户体验**：类似 eBird 的专业级热力图效果

## 性能考虑

- 防抖延迟设置为 250ms，平衡响应性和性能
- 使用 `useMemo` 和 `useCallback` 优化重新渲染
- 缓存网格计算结果，避免重复计算
- 渐进式渲染，优先显示重要的热力块

## 兼容性

- 兼容现有的 HeatmapLayer API
- 向后兼容，不影响现有功能
- 可以通过配置选项调整行为
