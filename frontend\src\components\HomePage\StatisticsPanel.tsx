import React, { memo } from 'react';
import { Typography, Statistic, Row, Col } from 'antd';

const { Title } = Typography;

interface StatisticsPanelProps {
  mapData: {
    totalSpecies: number;
    totalRecordings: number;
    activeHotspots: number;
    onlineUsers: number;
  };
}

const StatisticsPanel: React.FC<StatisticsPanelProps> = memo(({ mapData }) => {
  return (
    <>
      {/* 侧边栏标题 */}
      <div style={{
        padding: '16px',
        borderBottom: '1px solid #e5e7eb',
        background: '#f8f9fa'
      }}>
        <Title level={4} style={{
          margin: 0,
          color: '#1f2937',
          fontSize: '16px',
          fontWeight: '600',
          fontFamily: '"Microsoft YaHei", "SimHei", sans-serif'
        }}>
          观察数据统计
        </Title>
      </div>

      {/* 统计数据 */}
      <div style={{
        padding: '16px',
        borderBottom: '1px solid #e5e7eb'
      }}>
        <Row gutter={[8, 16]}>
          <Col span={12}>
            <Statistic
              title="物种总数"
              value={mapData.totalSpecies}
              valueStyle={{
                color: '#1e3a8a',
                fontSize: '20px',
                fontWeight: '600'
              }}
              suffix="种"
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="录音数量"
              value={mapData.totalRecordings}
              valueStyle={{
                color: '#059669',
                fontSize: '20px',
                fontWeight: '600'
              }}
              suffix="条"
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="活跃热点"
              value={mapData.activeHotspots}
              valueStyle={{
                color: '#dc2626',
                fontSize: '20px',
                fontWeight: '600'
              }}
              suffix="个"
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="在线用户"
              value={mapData.onlineUsers}
              valueStyle={{
                color: '#7c3aed',
                fontSize: '20px',
                fontWeight: '600'
              }}
              suffix="人"
            />
          </Col>
        </Row>
      </div>
    </>
  );
});

StatisticsPanel.displayName = 'StatisticsPanel';

export default StatisticsPanel;
