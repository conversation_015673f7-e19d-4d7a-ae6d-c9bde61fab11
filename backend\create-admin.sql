-- 创建默认管理员用户 (密码: admin123)
WITH new_user AS (
    INSERT INTO users (id, username, email, "passwordHash", status, "createdAt", "updatedAt") 
    VALUES (gen_random_uuid(), 'admin', '<EMAIL>', '$2b$10$4W1iVQCQGGfrGPaAHAQXJ.OpeaOxtEUFgajrpJbh6BoyZmNXGzECO', 'active', NOW(), NOW())
    RETURNING id
)
INSERT INTO user_roles (user_id, role_id) 
SELECT new_user.id, roles.id 
FROM new_user, roles 
WHERE roles.name = 'admin';
