import React, { memo, useCallback } from 'react';
import { Typography, Space, Button } from 'antd';

const { Title } = Typography;

const QuickActionsPanel: React.FC = memo(() => {
  // 使用useCallback优化事件处理函数
  const handleViewRecords = useCallback(() => {
    console.log('查看最新观察记录');
    // 这里可以添加实际的导航逻辑
  }, []);

  const handleDownloadReport = useCallback(() => {
    console.log('下载数据报告');
    // 这里可以添加实际的下载逻辑
  }, []);

  const handleSetReminder = useCallback(() => {
    console.log('设置监测提醒');
    // 这里可以添加实际的提醒设置逻辑
  }, []);

  return (
    <div style={{
      padding: '16px',
      flex: 1
    }}>
      <Title level={5} style={{
        margin: '0 0 12px 0',
        color: '#1f2937',
        fontSize: '14px',
        fontWeight: '600',
        fontFamily: '"Microsoft YaHei", "SimHei", sans-serif'
      }}>
        快捷操作
      </Title>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Button
          block
          size="small"
          onClick={handleViewRecords}
          style={{
            textAlign: 'left',
            height: 'auto',
            padding: '8px 12px',
            borderRadius: '4px'
          }}
        >
          查看最新观察记录
        </Button>
        <Button
          block
          size="small"
          onClick={handleDownloadReport}
          style={{
            textAlign: 'left',
            height: 'auto',
            padding: '8px 12px',
            borderRadius: '4px'
          }}
        >
          下载数据报告
        </Button>
        <Button
          block
          size="small"
          onClick={handleSetReminder}
          style={{
            textAlign: 'left',
            height: 'auto',
            padding: '8px 12px',
            borderRadius: '4px'
          }}
        >
          设置监测提醒
        </Button>
      </Space>
    </div>
  );
});

QuickActionsPanel.displayName = 'QuickActionsPanel';

export default QuickActionsPanel;
