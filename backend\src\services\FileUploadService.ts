import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { MediaFile, AudioFile, Species } from '../models';
import * as fs from 'fs/promises';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

export interface UploadedFileInfo {
  id: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  uploadTime: Date;
}

export interface AudioMetadata {
  recordingLocation?: string;
  recordingTime?: Date;
  behaviorDescription?: string;
  samplingRate?: number;
  sensitivity?: number;
  amplificationFactor?: number;
  latitude?: number;
  longitude?: number;
}

export class FileUploadService {
  private mediaRepository: Repository<MediaFile>;
  private audioRepository: Repository<AudioFile>;
  private speciesRepository: Repository<Species>;

  constructor() {
    this.mediaRepository = AppDataSource.getRepository(MediaFile);
    this.audioRepository = AppDataSource.getRepository(AudioFile);
    this.speciesRepository = AppDataSource.getRepository(Species);
  }

  /**
   * 上传图片文件
   */
  async uploadImage(
    speciesId: string,
    file: Express.Multer.File,
    title?: string,
    description?: string,
    isRepresentative: boolean = false
  ): Promise<MediaFile> {
    const species = await this.validateSpecies(speciesId);
    
    // 创建物种专属目录
    const speciesDir = path.join(process.env.UPLOAD_DIR || 'uploads', 'species', speciesId, 'images');
    await this.ensureDirectoryExists(speciesDir);

    // 生成唯一文件名
    const fileExtension = path.extname(file.originalname);
    const uniqueFileName = `${uuidv4()}${fileExtension}`;
    const filePath = path.join(speciesDir, uniqueFileName);

    // 移动文件到目标位置
    await fs.rename(file.path, filePath);

    // 如果设置为代表性图片，先取消其他代表性图片
    if (isRepresentative) {
      await this.mediaRepository.update(
        { species: { id: speciesId }, fileType: 'image' },
        { isRepresentative: false }
      );
    }

    // 创建媒体文件记录
    const mediaFile = new MediaFile();
    mediaFile.species = species;
    mediaFile.fileType = 'image';
    mediaFile.filePath = filePath;
    mediaFile.fileName = file.originalname;
    mediaFile.fileSize = file.size;
    mediaFile.mimeType = file.mimetype;
    mediaFile.title = title;
    mediaFile.description = description;
    mediaFile.isRepresentative = isRepresentative;

    const savedFile = await this.mediaRepository.save(mediaFile);

    // 如果是代表性图片，更新物种表
    if (isRepresentative) {
      await this.speciesRepository.update(speciesId, {
        representativeImagePath: filePath,
      });
    }

    return savedFile;
  }

  /**
   * 上传视频文件
   */
  async uploadVideo(
    speciesId: string,
    file: Express.Multer.File,
    title?: string,
    description?: string
  ): Promise<MediaFile> {
    const species = await this.validateSpecies(speciesId);
    
    // 创建物种专属目录
    const speciesDir = path.join(process.env.UPLOAD_DIR || 'uploads', 'species', speciesId, 'videos');
    await this.ensureDirectoryExists(speciesDir);

    // 生成唯一文件名
    const fileExtension = path.extname(file.originalname);
    const uniqueFileName = `${uuidv4()}${fileExtension}`;
    const filePath = path.join(speciesDir, uniqueFileName);

    // 移动文件到目标位置
    await fs.rename(file.path, filePath);

    // 创建媒体文件记录
    const mediaFile = new MediaFile();
    mediaFile.species = species;
    mediaFile.fileType = 'video';
    mediaFile.filePath = filePath;
    mediaFile.fileName = file.originalname;
    mediaFile.fileSize = file.size;
    mediaFile.mimeType = file.mimetype;
    mediaFile.title = title;
    mediaFile.description = description;

    return await this.mediaRepository.save(mediaFile);
  }

  /**
   * 上传音频文件
   */
  async uploadAudio(
    speciesId: string,
    file: Express.Multer.File,
    metadata?: AudioMetadata
  ): Promise<AudioFile> {
    const species = await this.validateSpecies(speciesId);
    
    // 创建物种专属目录
    const speciesDir = path.join(process.env.UPLOAD_DIR || 'uploads', 'species', speciesId, 'audio');
    await this.ensureDirectoryExists(speciesDir);

    // 生成唯一文件名
    const fileExtension = path.extname(file.originalname);
    const uniqueFileName = `${uuidv4()}${fileExtension}`;
    const filePath = path.join(speciesDir, uniqueFileName);

    // 移动文件到目标位置
    await fs.rename(file.path, filePath);

    // 创建音频文件记录
    const audioFile = new AudioFile();
    audioFile.species = species;
    audioFile.filePath = filePath;
    audioFile.fileName = file.originalname;
    audioFile.fileSize = file.size;
    audioFile.mimeType = file.mimetype;

    // 设置元数据
    if (metadata) {
      audioFile.recordingLocation = metadata.recordingLocation;
      audioFile.recordingTime = metadata.recordingTime;
      audioFile.behaviorDescription = metadata.behaviorDescription;
      audioFile.samplingRate = metadata.samplingRate;
      audioFile.sensitivity = metadata.sensitivity;
      audioFile.amplificationFactor = metadata.amplificationFactor;
      audioFile.latitude = metadata.latitude;
      audioFile.longitude = metadata.longitude;
    }

    return await this.audioRepository.save(audioFile);
  }

  /**
   * 获取物种的媒体文件
   */
  async getSpeciesMediaFiles(speciesId: string, fileType?: 'image' | 'video'): Promise<MediaFile[]> {
    const whereCondition: any = { species: { id: speciesId } };
    if (fileType) {
      whereCondition.fileType = fileType;
    }

    return await this.mediaRepository.find({
      where: whereCondition,
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 获取物种的音频文件
   */
  async getSpeciesAudioFiles(speciesId: string): Promise<AudioFile[]> {
    return await this.audioRepository.find({
      where: { species: { id: speciesId } },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 删除媒体文件
   */
  async deleteMediaFile(fileId: string): Promise<void> {
    const mediaFile = await this.mediaRepository.findOne({
      where: { id: fileId },
      relations: ['species'],
    });

    if (!mediaFile) {
      throw new Error('文件不存在');
    }

    // 删除物理文件
    try {
      await fs.unlink(mediaFile.filePath);
    } catch (error) {
      console.error('删除物理文件失败:', error);
    }

    // 如果是代表性图片，清除物种表中的引用
    if (mediaFile.isRepresentative && mediaFile.fileType === 'image') {
      await this.speciesRepository.update(mediaFile.species.id, {
        representativeImagePath: undefined,
      });
    }

    // 删除数据库记录
    await this.mediaRepository.remove(mediaFile);
  }

  /**
   * 删除音频文件
   */
  async deleteAudioFile(fileId: string): Promise<void> {
    const audioFile = await this.audioRepository.findOne({
      where: { id: fileId },
    });

    if (!audioFile) {
      throw new Error('音频文件不存在');
    }

    // 删除物理文件
    try {
      await fs.unlink(audioFile.filePath);
    } catch (error) {
      console.error('删除物理文件失败:', error);
    }

    // 删除数据库记录
    await this.audioRepository.remove(audioFile);
  }

  /**
   * 设置代表性图片
   */
  async setRepresentativeImage(speciesId: string, mediaFileId: string): Promise<void> {
    const mediaFile = await this.mediaRepository.findOne({
      where: { id: mediaFileId, species: { id: speciesId }, fileType: 'image' },
    });

    if (!mediaFile) {
      throw new Error('图片文件不存在');
    }

    // 取消其他代表性图片
    await this.mediaRepository.update(
      { species: { id: speciesId }, fileType: 'image' },
      { isRepresentative: false }
    );

    // 设置新的代表性图片
    await this.mediaRepository.update(mediaFileId, { isRepresentative: true });

    // 更新物种表
    await this.speciesRepository.update(speciesId, {
      representativeImagePath: mediaFile.filePath,
    });
  }

  /**
   * 验证物种是否存在
   */
  private async validateSpecies(speciesId: string): Promise<Species> {
    const species = await this.speciesRepository.findOne({
      where: { id: speciesId },
    });

    if (!species) {
      throw new Error('物种不存在');
    }

    return species;
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  /**
   * 获取文件统计信息
   */
  async getFileStats(speciesId: string): Promise<any> {
    const imageCount = await this.mediaRepository.count({
      where: { species: { id: speciesId }, fileType: 'image' },
    });

    const videoCount = await this.mediaRepository.count({
      where: { species: { id: speciesId }, fileType: 'video' },
    });

    const audioCount = await this.audioRepository.count({
      where: { species: { id: speciesId } },
    });

    return {
      imageCount,
      videoCount,
      audioCount,
      totalCount: imageCount + videoCount + audioCount,
    };
  }
}
