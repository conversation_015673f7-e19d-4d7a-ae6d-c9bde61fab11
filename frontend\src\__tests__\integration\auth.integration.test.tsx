import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, createTestStore } from '../utils/test-utils';
import App from '../../App';
import { server } from '../../__mocks__/server';
import { rest } from 'msw';

describe('用户认证集成测试', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  describe('登录流程', () => {
    it('应该完成完整的登录流程', async () => {
      const user = userEvent.setup();
      render(<App />);
      
      // 1. 导航到登录页面
      const loginLink = screen.getByText('登录');
      await user.click(loginLink);
      
      // 2. 验证登录页面已加载
      await waitFor(() => {
        expect(screen.getByText('海洋生物声音平台')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('用户名')).toBeInTheDocument();
      });
      
      // 3. 填写登录表单
      const usernameInput = screen.getByPlaceholderText('用户名');
      const passwordInput = screen.getByPlaceholderText('密码');
      const loginButton = screen.getByRole('button', { name: /登录/ });
      
      await user.type(usernameInput, 'testuser');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);
      
      // 4. 验证登录成功并重定向到首页
      await waitFor(() => {
        expect(screen.getByText('海洋生物声音平台')).toBeInTheDocument();
        expect(screen.getByText('testuser')).toBeInTheDocument(); // 用户名显示在导航栏
      });
      
      // 5. 验证导航栏状态变化
      expect(screen.queryByText('登录')).not.toBeInTheDocument();
      expect(screen.getByText('testuser')).toBeInTheDocument();
    });

    it('应该处理登录失败并显示错误', async () => {
      const user = userEvent.setup();
      
      // 模拟登录失败
      server.use(
        rest.post('http://localhost:3001/api/auth/login', (req, res, ctx) => {
          return res(
            ctx.status(401),
            ctx.json({
              success: false,
              error: { message: '用户名或密码错误' }
            })
          );
        })
      );
      
      render(<App />);
      
      // 导航到登录页面
      const loginLink = screen.getByText('登录');
      await user.click(loginLink);
      
      // 填写错误的登录信息
      const usernameInput = screen.getByPlaceholderText('用户名');
      const passwordInput = screen.getByPlaceholderText('密码');
      const loginButton = screen.getByRole('button', { name: /登录/ });
      
      await user.type(usernameInput, 'wronguser');
      await user.type(passwordInput, 'wrongpass');
      await user.click(loginButton);
      
      // 验证错误消息显示
      await waitFor(() => {
        expect(screen.getByText('用户名或密码错误')).toBeInTheDocument();
      });
      
      // 验证仍在登录页面
      expect(screen.getByPlaceholderText('用户名')).toBeInTheDocument();
      expect(screen.getByText('登录')).toBeInTheDocument(); // 导航栏仍显示登录链接
    });

    it('应该在已登录状态下重定向', async () => {
      const user = userEvent.setup();
      
      // 预设已登录状态
      const store = createTestStore({
        auth: {
          user: {
            id: '1',
            username: 'testuser',
            email: '<EMAIL>',
            roles: []
          },
          token: 'mock-token',
          isAuthenticated: true,
          loading: false,
          error: null
        }
      });
      
      render(<App />, { store });
      
      // 尝试访问登录页面
      const loginLink = screen.queryByText('登录');
      
      // 已登录用户不应该看到登录链接
      expect(loginLink).not.toBeInTheDocument();
      expect(screen.getByText('testuser')).toBeInTheDocument();
    });
  });

  describe('注册流程', () => {
    it('应该完成完整的注册流程', async () => {
      const user = userEvent.setup();
      render(<App />);
      
      // 1. 导航到注册页面
      const registerLink = screen.getByText('注册');
      await user.click(registerLink);
      
      // 2. 验证注册页面已加载
      await waitFor(() => {
        expect(screen.getByText('创建新账户')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('用户名')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('邮箱')).toBeInTheDocument();
      });
      
      // 3. 填写注册表单
      const usernameInput = screen.getByPlaceholderText('用户名');
      const emailInput = screen.getByPlaceholderText('邮箱');
      const passwordInput = screen.getByPlaceholderText('密码');
      const confirmPasswordInput = screen.getByPlaceholderText('确认密码');
      const registerButton = screen.getByRole('button', { name: /注册/ });
      
      await user.type(usernameInput, 'newuser');
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(registerButton);
      
      // 4. 验证注册成功消息
      await waitFor(() => {
        expect(screen.getByText('注册成功！请登录您的账户')).toBeInTheDocument();
      });
    });

    it('应该验证密码确认', async () => {
      const user = userEvent.setup();
      render(<App />);
      
      // 导航到注册页面
      const registerLink = screen.getByText('注册');
      await user.click(registerLink);
      
      // 填写不匹配的密码
      const usernameInput = screen.getByPlaceholderText('用户名');
      const emailInput = screen.getByPlaceholderText('邮箱');
      const passwordInput = screen.getByPlaceholderText('密码');
      const confirmPasswordInput = screen.getByPlaceholderText('确认密码');
      const registerButton = screen.getByRole('button', { name: /注册/ });
      
      await user.type(usernameInput, 'newuser');
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'differentpassword');
      await user.click(registerButton);
      
      // 验证密码不匹配错误
      await waitFor(() => {
        expect(screen.getByText('两次输入的密码不一致')).toBeInTheDocument();
      });
    });
  });

  describe('登出流程', () => {
    it('应该完成完整的登出流程', async () => {
      const user = userEvent.setup();
      
      // 预设已登录状态
      const store = createTestStore({
        auth: {
          user: {
            id: '1',
            username: 'testuser',
            email: '<EMAIL>',
            roles: []
          },
          token: 'mock-token',
          isAuthenticated: true,
          loading: false,
          error: null
        }
      });
      
      render(<App />, { store });
      
      // 1. 验证已登录状态
      expect(screen.getByText('testuser')).toBeInTheDocument();
      
      // 2. 点击用户菜单
      const userMenu = screen.getByText('testuser');
      await user.click(userMenu);
      
      // 3. 点击退出登录
      await waitFor(() => {
        const logoutButton = screen.getByText('退出登录');
        return user.click(logoutButton);
      });
      
      // 4. 验证已登出状态
      await waitFor(() => {
        expect(screen.getByText('登录')).toBeInTheDocument();
        expect(screen.queryByText('testuser')).not.toBeInTheDocument();
      });
    });
  });

  describe('受保护路由', () => {
    it('应该重定向未认证用户到登录页面', async () => {
      // 这里需要根据实际的受保护路由来测试
      // 例如，如果有管理员页面需要认证
      
      render(<App />);
      
      // 尝试直接访问受保护的路由
      // 这里需要根据实际路由结构来实现
      
      // 验证重定向到登录页面
      // expect(screen.getByText('请先登录')).toBeInTheDocument();
    });
  });

  describe('Token管理', () => {
    it('应该在页面刷新后保持登录状态', async () => {
      // 设置localStorage中的token
      localStorage.setItem('token', 'mock-jwt-token');
      
      render(<App />);
      
      // 验证自动获取用户信息
      await waitFor(() => {
        expect(screen.getByText('testuser')).toBeInTheDocument();
      });
    });

    it('应该处理过期的token', async () => {
      // 设置过期的token
      localStorage.setItem('token', 'expired-token');
      
      // 模拟token过期响应
      server.use(
        rest.get('http://localhost:3001/api/auth/profile', (req, res, ctx) => {
          return res(
            ctx.status(401),
            ctx.json({
              success: false,
              error: { message: 'Token已过期' }
            })
          );
        })
      );
      
      render(<App />);
      
      // 验证token被清除，用户被登出
      await waitFor(() => {
        expect(screen.getByText('登录')).toBeInTheDocument();
        expect(localStorage.getItem('token')).toBeNull();
      });
    });
  });
});
