import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Space, 
  Button, 
  Tag, 
  Typography, 
  Modal, 
  message,
  Input,
  Select,
  Popconfirm
} from 'antd';
import { 
  FileOutlined, 
  DownloadOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  SearchOutlined,
  FilterOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Search } = Input;
const { Option } = Select;
const { Text } = Typography;

interface FileItem {
  id: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  uploadTime: string;
  uploader: string;
  speciesName?: string;
  fileType: 'audio' | 'image' | 'video' | 'document';
  status: 'active' | 'deleted';
}

interface FileManagerProps {
  speciesId?: string;
  fileType?: 'audio' | 'image' | 'video' | 'document' | 'all';
  showUploader?: boolean;
}

const FileManager: React.FC<FileManagerProps> = ({
  speciesId,
  fileType = 'all',
  showUploader = true,
}) => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [searchText, setSearchText] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState<FileItem | null>(null);

  // 获取文件列表
  const fetchFiles = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (speciesId) params.append('speciesId', speciesId);
      if (fileType !== 'all') params.append('fileType', fileType);
      if (searchText) params.append('search', searchText);
      if (filterType !== 'all') params.append('mimeType', filterType);

      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/files?${params}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setFiles(data.data || []);
      }
    } catch (error) {
      message.error('获取文件列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFiles();
  }, [speciesId, fileType, searchText, filterType]);

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // 获取文件类型标签
  const getFileTypeTag = (mimeType: string) => {
    if (mimeType.startsWith('audio/')) {
      return <Tag color="blue">音频</Tag>;
    } else if (mimeType.startsWith('image/')) {
      return <Tag color="green">图片</Tag>;
    } else if (mimeType.startsWith('video/')) {
      return <Tag color="purple">视频</Tag>;
    } else {
      return <Tag color="default">文档</Tag>;
    }
  };

  // 下载文件
  const downloadFile = (file: FileItem) => {
    const link = document.createElement('a');
    link.href = `${import.meta.env.VITE_STATIC_FILES_URL}/${file.filePath}`;
    link.download = file.fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 预览文件
  const previewFile = (file: FileItem) => {
    setPreviewFile(file);
    setPreviewVisible(true);
  };

  // 删除文件
  const deleteFile = async (fileId: string) => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/files/${fileId}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );

      if (response.ok) {
        message.success('文件删除成功');
        fetchFiles();
      } else {
        message.error('文件删除失败');
      }
    } catch (error) {
      message.error('文件删除失败');
    }
  };

  // 批量删除
  const batchDelete = async () => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/files/batch-delete`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({ fileIds: selectedRowKeys }),
        }
      );

      if (response.ok) {
        message.success(`成功删除 ${selectedRowKeys.length} 个文件`);
        setSelectedRowKeys([]);
        fetchFiles();
      } else {
        message.error('批量删除失败');
      }
    } catch (error) {
      message.error('批量删除失败');
    }
  };

  const columns: ColumnsType<FileItem> = [
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
      ellipsis: true,
      render: (text, record) => (
        <Space>
          <FileOutlined />
          <Text>{text}</Text>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'mimeType',
      key: 'mimeType',
      width: 80,
      render: (mimeType) => getFileTypeTag(mimeType),
    },
    {
      title: '大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 100,
      render: (size) => formatFileSize(size),
    },
    {
      title: '物种',
      dataIndex: 'speciesName',
      key: 'speciesName',
      width: 120,
      render: (text) => text || '-',
    },
    {
      title: '上传时间',
      dataIndex: 'uploadTime',
      key: 'uploadTime',
      width: 150,
      render: (time) => new Date(time).toLocaleString(),
    },
    ...(showUploader ? [{
      title: '上传者',
      dataIndex: 'uploader',
      key: 'uploader',
      width: 100,
    }] : []),
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => previewFile(record)}
          />
          <Button
            type="text"
            size="small"
            icon={<DownloadOutlined />}
            onClick={() => downloadFile(record)}
          />
          <Popconfirm
            title="确定删除这个文件吗？"
            onConfirm={() => deleteFile(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              danger
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  return (
    <Card
      title={
        <Space>
          <FileOutlined />
          文件管理
        </Space>
      }
      extra={
        <Space>
          <Search
            placeholder="搜索文件名"
            allowClear
            style={{ width: 200 }}
            onSearch={setSearchText}
          />
          <Select
            value={filterType}
            onChange={setFilterType}
            style={{ width: 120 }}
            placeholder="文件类型"
          >
            <Option value="all">全部类型</Option>
            <Option value="audio">音频</Option>
            <Option value="image">图片</Option>
            <Option value="video">视频</Option>
            <Option value="document">文档</Option>
          </Select>
        </Space>
      }
    >
      {/* 批量操作 */}
      {selectedRowKeys.length > 0 && (
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Text>已选择 {selectedRowKeys.length} 个文件</Text>
            <Popconfirm
              title={`确定删除选中的 ${selectedRowKeys.length} 个文件吗？`}
              onConfirm={batchDelete}
              okText="确定"
              cancelText="取消"
            >
              <Button danger icon={<DeleteOutlined />}>
                批量删除
              </Button>
            </Popconfirm>
          </Space>
        </div>
      )}

      {/* 文件表格 */}
      <Table
        columns={columns}
        dataSource={files}
        rowKey="id"
        rowSelection={rowSelection}
        loading={loading}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 个文件`,
        }}
      />

      {/* 文件预览模态框 */}
      <Modal
        title="文件预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="download" onClick={() => previewFile && downloadFile(previewFile)}>
            下载
          </Button>,
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        {previewFile && (
          <div style={{ textAlign: 'center' }}>
            {previewFile.mimeType.startsWith('image/') ? (
              <img
                src={`${import.meta.env.VITE_STATIC_FILES_URL}/${previewFile.filePath}`}
                alt={previewFile.fileName}
                style={{ maxWidth: '100%', maxHeight: '500px' }}
              />
            ) : (
              <div style={{ padding: '40px' }}>
                <FileOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
                <div style={{ marginTop: '16px' }}>
                  <Text>{previewFile.fileName}</Text>
                </div>
                <div style={{ marginTop: '8px' }}>
                  <Text type="secondary">
                    {getFileTypeTag(previewFile.mimeType)} {formatFileSize(previewFile.fileSize)}
                  </Text>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </Card>
  );
};

export default FileManager;
