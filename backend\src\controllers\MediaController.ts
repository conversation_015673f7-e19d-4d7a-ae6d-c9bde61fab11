import { Request, Response } from 'express';
import { FileUploadService, AudioMetadata } from '../services/FileUploadService';
import multer from 'multer';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const tempDir = path.join(process.env.UPLOAD_DIR || 'uploads', 'temp');
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueName);
  },
});

// 文件类型验证
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  const allowedVideoTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'];
  const allowedAudioTypes = ['audio/wav', 'audio/mp3', 'audio/flac', 'audio/aac'];

  const allAllowedTypes = [...allowedImageTypes, ...allowedVideoTypes, ...allowedAudioTypes];

  if (allAllowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型'));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
  },
});

export class MediaController {
  private fileUploadService: FileUploadService;

  constructor() {
    this.fileUploadService = new FileUploadService();
  }

  /**
   * 上传图片
   */
  uploadImage = async (req: Request, res: Response) => {
    try {
      const { speciesId } = req.params;
      const { title, description, isRepresentative } = req.body;

      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'NO_FILE',
            message: '请选择图片文件',
          },
        });
      }

      const mediaFile = await this.fileUploadService.uploadImage(
        speciesId,
        req.file,
        title,
        description,
        isRepresentative === 'true'
      );

      res.json({
        success: true,
        data: mediaFile,
        message: '图片上传成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'IMAGE_UPLOAD_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 上传视频
   */
  uploadVideo = async (req: Request, res: Response) => {
    try {
      const { speciesId } = req.params;
      const { title, description } = req.body;

      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'NO_FILE',
            message: '请选择视频文件',
          },
        });
      }

      const mediaFile = await this.fileUploadService.uploadVideo(
        speciesId,
        req.file,
        title,
        description
      );

      res.json({
        success: true,
        data: mediaFile,
        message: '视频上传成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VIDEO_UPLOAD_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 上传音频
   */
  uploadAudio = async (req: Request, res: Response) => {
    try {
      const { speciesId } = req.params;
      const {
        recordingLocation,
        recordingTime,
        behaviorDescription,
        samplingRate,
        sensitivity,
        amplificationFactor,
        latitude,
        longitude,
      } = req.body;

      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'NO_FILE',
            message: '请选择音频文件',
          },
        });
      }

      const metadata: AudioMetadata = {
        recordingLocation,
        recordingTime: recordingTime ? new Date(recordingTime) : undefined,
        behaviorDescription,
        samplingRate: samplingRate ? parseInt(samplingRate) : undefined,
        sensitivity: sensitivity ? parseFloat(sensitivity) : undefined,
        amplificationFactor: amplificationFactor ? parseFloat(amplificationFactor) : undefined,
        latitude: latitude ? parseFloat(latitude) : undefined,
        longitude: longitude ? parseFloat(longitude) : undefined,
      };

      const audioFile = await this.fileUploadService.uploadAudio(
        speciesId,
        req.file,
        metadata
      );

      res.json({
        success: true,
        data: audioFile,
        message: '音频上传成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'AUDIO_UPLOAD_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取物种媒体文件
   */
  getSpeciesMediaFiles = async (req: Request, res: Response) => {
    try {
      const { speciesId } = req.params;
      const { type } = req.query;

      const fileType = type as 'image' | 'video' | undefined;
      const mediaFiles = await this.fileUploadService.getSpeciesMediaFiles(speciesId, fileType);

      res.json({
        success: true,
        data: mediaFiles,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'GET_MEDIA_FILES_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取物种音频文件
   */
  getSpeciesAudioFiles = async (req: Request, res: Response) => {
    try {
      const { speciesId } = req.params;

      const audioFiles = await this.fileUploadService.getSpeciesAudioFiles(speciesId);

      res.json({
        success: true,
        data: audioFiles,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'GET_AUDIO_FILES_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 删除媒体文件
   */
  deleteMediaFile = async (req: Request, res: Response) => {
    try {
      const { fileId } = req.params;

      await this.fileUploadService.deleteMediaFile(fileId);

      res.json({
        success: true,
        message: '文件删除成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'DELETE_MEDIA_FILE_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 删除音频文件
   */
  deleteAudioFile = async (req: Request, res: Response) => {
    try {
      const { fileId } = req.params;

      await this.fileUploadService.deleteAudioFile(fileId);

      res.json({
        success: true,
        message: '音频文件删除成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'DELETE_AUDIO_FILE_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 设置代表性图片
   */
  setRepresentativeImage = async (req: Request, res: Response) => {
    try {
      const { speciesId, fileId } = req.params;

      await this.fileUploadService.setRepresentativeImage(speciesId, fileId);

      res.json({
        success: true,
        message: '代表性图片设置成功',
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: {
          code: 'SET_REPRESENTATIVE_IMAGE_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取文件统计信息
   */
  getFileStats = async (req: Request, res: Response) => {
    try {
      const { speciesId } = req.params;

      const stats = await this.fileUploadService.getFileStats(speciesId);

      res.json({
        success: true,
        data: stats,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'GET_FILE_STATS_ERROR',
          message: error.message,
        },
      });
    }
  };

  // Multer中间件
  getUploadMiddleware() {
    return upload.single('file');
  }
}
