import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { DistributionRange, Species } from '../models';
import { parseKMLFile, convertToWKT, KMLParseResult } from '../utils/kmlParser';
import * as fs from 'fs/promises';
import * as path from 'path';

export class GeographyService {
  private distributionRepository: Repository<DistributionRange>;
  private speciesRepository: Repository<Species>;

  constructor() {
    this.distributionRepository = AppDataSource.getRepository(DistributionRange);
    this.speciesRepository = AppDataSource.getRepository(Species);
  }

  /**
   * 上传并解析KML文件
   */
  async uploadKMLFile(
    speciesId: string,
    kmlFilePath: string,
    originalFileName: string
  ): Promise<DistributionRange[]> {
    // 验证物种是否存在
    const species = await this.speciesRepository.findOne({
      where: { id: speciesId },
    });

    if (!species) {
      throw new Error('物种不存在');
    }

    try {
      // 解析KML文件
      const kmlResults = await parseKMLFile(kmlFilePath);
      
      if (kmlResults.length === 0) {
        throw new Error('KML文件中没有找到有效的地理数据');
      }

      // 创建分布范围记录
      const distributionRanges: DistributionRange[] = [];

      for (const kmlResult of kmlResults) {
        const wkt = convertToWKT(kmlResult.multiPolygon);
        
        // 创建分布范围记录
        const distributionRange = new DistributionRange();
        distributionRange.species = species;
        distributionRange.kmlFilePath = kmlFilePath;

        // 保存到数据库
        const savedRange = await this.distributionRepository.save(distributionRange);

        // 更新几何数据
        await AppDataSource.query(
          'UPDATE distribution_ranges SET geometry = ST_GeomFromText($1, 4326) WHERE id = $2',
          [wkt, savedRange.id]
        );

        distributionRanges.push(savedRange);
      }

      return distributionRanges;
    } catch (error) {
      // 如果解析失败，删除上传的文件
      try {
        await fs.unlink(kmlFilePath);
      } catch (unlinkError) {
        console.error('删除失败的KML文件时出错:', unlinkError);
      }
      
      throw new Error(`KML文件处理失败: ${error}`);
    }
  }

  /**
   * 获取物种的分布范围数据
   */
  async getSpeciesDistribution(speciesId: string): Promise<any[]> {
    const query = `
      SELECT 
        dr.id,
        dr.species_id,
        ST_AsGeoJSON(dr.geometry) as geometry,
        dr.kml_file_path,
        dr.created_at
      FROM distribution_ranges dr
      WHERE dr.species_id = $1
      AND dr.geometry IS NOT NULL
    `;

    const results = await AppDataSource.query(query, [speciesId]);
    
    return results.map((row: any) => ({
      id: row.id,
      speciesId: row.species_id,
      geometry: row.geometry ? JSON.parse(row.geometry) : null,
      kmlFilePath: row.kml_file_path,
      createdAt: row.created_at,
    }));
  }

  /**
   * 获取所有物种的分布范围（用于地图展示）
   */
  async getAllDistributions(): Promise<any[]> {
    const query = `
      SELECT 
        dr.id,
        dr.species_id,
        s."chineseName",
        s."latinName",
        ST_AsGeoJSON(dr.geometry) as geometry
      FROM distribution_ranges dr
      JOIN species s ON dr.species_id = s.id
      WHERE dr.geometry IS NOT NULL
    `;

    const results = await AppDataSource.query(query);
    
    return results.map((row: any) => ({
      id: row.id,
      speciesId: row.species_id,
      chineseName: row.chineseName,
      latinName: row.latinName,
      geometry: row.geometry ? JSON.parse(row.geometry) : null,
    }));
  }

  /**
   * 根据地理位置查找物种
   */
  async findSpeciesByLocation(
    longitude: number,
    latitude: number
  ): Promise<Species[]> {
    const query = `
      SELECT DISTINCT s.*
      FROM species s
      JOIN distribution_ranges dr ON s.id = dr.species_id
      WHERE ST_Contains(
        dr.geometry, 
        ST_SetSRID(ST_MakePoint($1, $2), 4326)
      )
    `;

    const results = await AppDataSource.query(query, [longitude, latitude]);
    return results;
  }

  /**
   * 获取音频采集点数据
   */
  async getAudioCollectionPoints(): Promise<any[]> {
    const query = `
      SELECT 
        MIN(af.id) as id,
        af.species_id,
        s."chineseName" as chinese_name,
        s."latinName" as latin_name,
        af.latitude,
        af.longitude,
        af."recordingLocation" as recording_location,
        COUNT(*) as audio_count,
        MAX(af."recordingTime") as last_recording,
        (array_agg(af."fileName" ORDER BY af."recordingTime" DESC))[1] as file_name
      FROM audio_files af
      JOIN species s ON af.species_id = s.id
      WHERE af.latitude IS NOT NULL 
      AND af.longitude IS NOT NULL
      GROUP BY af.species_id, s."chineseName", s."latinName", af.latitude, af.longitude, af."recordingLocation"
      ORDER BY audio_count DESC
    `;

    return await AppDataSource.query(query);
  }

  /**
   * 获取指定区域内的音频采集点
   */
  async getAudioCollectionPointsInBounds(
    minLng: number,
    minLat: number,
    maxLng: number,
    maxLat: number
  ): Promise<any[]> {
    const query = `
      SELECT 
        MIN(af.id) as id,
        af.species_id,
        s."chineseName" as chinese_name,
        s."latinName" as latin_name,
        af.latitude,
        af.longitude,
        af."recordingLocation" as recording_location,
        COUNT(*) as audio_count,
        MAX(af."recordingTime") as last_recording,
        (array_agg(af."fileName" ORDER BY af."recordingTime" DESC))[1] as file_name
      FROM audio_files af
      JOIN species s ON af.species_id = s.id
      WHERE af.latitude BETWEEN $1 AND $3
      AND af.longitude BETWEEN $2 AND $4
      GROUP BY af.species_id, s."chineseName", s."latinName", af.latitude, af.longitude, af."recordingLocation"
      ORDER BY audio_count DESC
    `;

    return await AppDataSource.query(query, [minLat, minLng, maxLat, maxLng]);
  }

  /**
   * 删除分布范围数据
   */
  async deleteDistributionRange(id: string): Promise<void> {
    const distributionRange = await this.distributionRepository.findOne({
      where: { id },
    });

    if (!distributionRange) {
      throw new Error('分布范围数据不存在');
    }

    // 删除KML文件
    if (distributionRange.kmlFilePath) {
      try {
        await fs.unlink(distributionRange.kmlFilePath);
      } catch (error) {
        console.error('删除KML文件时出错:', error);
      }
    }

    // 删除数据库记录
    await this.distributionRepository.remove(distributionRange);
  }

  /**
   * 验证地理坐标
   */
  validateCoordinates(longitude: number, latitude: number): boolean {
    return (
      longitude >= -180 &&
      longitude <= 180 &&
      latitude >= -90 &&
      latitude <= 90
    );
  }
}
