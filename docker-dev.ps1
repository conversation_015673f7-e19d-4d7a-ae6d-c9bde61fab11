# Docker开发环境管理脚本 (PowerShell)

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("up", "down", "restart", "logs", "status", "clean")]
    [string]$Action
)

switch ($Action) {
    "up" {
        Write-Host "🚀 启动开发环境..." -ForegroundColor Green
        docker-compose up -d
        Write-Host "✅ 服务启动完成!" -ForegroundColor Green
        Write-Host ""
        Write-Host "📊 服务信息:" -ForegroundColor Yellow
        Write-Host "  PostgreSQL: localhost:5432" -ForegroundColor Cyan
        Write-Host "  Redis: localhost:6379" -ForegroundColor Cyan
        Write-Host "  pgAdmin: http://localhost:8080 (可选)" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "🔍 查看服务状态: .\docker-dev.ps1 status" -ForegroundColor Yellow
        Write-Host "📋 查看日志: .\docker-dev.ps1 logs" -ForegroundColor Yellow
    }
    "down" {
        Write-Host "🛑 停止开发环境..." -ForegroundColor Red
        docker-compose down
        Write-Host "✅ 服务已停止!" -ForegroundColor Green
    }
    "restart" {
        Write-Host "🔄 重启开发环境..." -ForegroundColor Yellow
        docker-compose restart
        Write-Host "✅ 服务已重启!" -ForegroundColor Green
    }
    "logs" {
        Write-Host "📋 显示服务日志..." -ForegroundColor Blue
        docker-compose logs -f
    }
    "status" {
        Write-Host "📊 服务状态:" -ForegroundColor Yellow
        docker-compose ps
        Write-Host ""
        Write-Host "🔍 健康检查:" -ForegroundColor Yellow
        docker-compose exec postgres pg_isready -U postgres -d marine_bio_platform
        docker-compose exec redis redis-cli ping
    }
    "clean" {
        Write-Host "🧹 清理开发环境..." -ForegroundColor Red
        Write-Host "⚠️  这将删除所有数据！按任意键继续，或Ctrl+C取消..." -ForegroundColor Red
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        docker-compose down -v
        docker system prune -f
        Write-Host "✅ 清理完成!" -ForegroundColor Green
    }
}
