import { apiClient } from './config';
import type { User, UserFormData, Role, ApiResponse, PaginatedResponse } from '../../types';

export interface UserSearchParams {
  keyword?: string;
  status?: 'active' | 'disabled';
  roleId?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export const userApi = {
  // 获取用户列表
  getUsers: async (params: UserSearchParams = {}): Promise<PaginatedResponse<User>> => {
    const response = await apiClient.get<ApiResponse<PaginatedResponse<User>>>('/users', { params });
    return response.data.data!;
  },

  // 获取用户详情
  getUser: async (id: string): Promise<User> => {
    const response = await apiClient.get<ApiResponse<User>>(`/users/${id}`);
    return response.data.data!;
  },

  // 创建用户
  createUser: async (data: UserFormData): Promise<User> => {
    const response = await apiClient.post<ApiResponse<User>>('/users', data);
    return response.data.data!;
  },

  // 更新用户
  updateUser: async (id: string, data: Partial<UserFormData>): Promise<User> => {
    const response = await apiClient.put<ApiResponse<User>>(`/users/${id}`, data);
    return response.data.data!;
  },

  // 删除用户
  deleteUser: async (id: string): Promise<void> => {
    await apiClient.delete<ApiResponse>(`/users/${id}`);
  },

  // 批量删除用户
  deleteUsers: async (ids: string[]): Promise<void> => {
    await apiClient.post<ApiResponse>('/users/batch-delete', { ids });
  },

  // 启用/禁用用户
  toggleUserStatus: async (id: string, status: 'active' | 'disabled'): Promise<User> => {
    const response = await apiClient.patch<ApiResponse<User>>(`/users/${id}/status`, { status });
    return response.data.data!;
  },

  // 重置用户密码
  resetPassword: async (id: string, newPassword: string): Promise<void> => {
    await apiClient.post<ApiResponse>(`/users/${id}/reset-password`, { password: newPassword });
  },

  // 获取所有角色
  getRoles: async (): Promise<Role[]> => {
    const response = await apiClient.get<ApiResponse<Role[]>>('/roles');
    return response.data.data!;
  },

  // 创建角色
  createRole: async (data: { name: string; description?: string; permissions?: Record<string, any> }): Promise<Role> => {
    const response = await apiClient.post<ApiResponse<Role>>('/roles', data);
    return response.data.data!;
  },

  // 更新角色
  updateRole: async (id: string, data: Partial<Role>): Promise<Role> => {
    const response = await apiClient.put<ApiResponse<Role>>(`/roles/${id}`, data);
    return response.data.data!;
  },

  // 删除角色
  deleteRole: async (id: string): Promise<void> => {
    await apiClient.delete<ApiResponse>(`/roles/${id}`);
  },
};
