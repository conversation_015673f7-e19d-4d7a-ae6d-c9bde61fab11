{"name": "admin-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 5174", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview --port 5174", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "type-check": "tsc --noEmit"}, "dependencies": {"@ant-design/charts": "^2.6.0", "@ant-design/icons": "^6.0.0", "@reduxjs/toolkit": "^2.8.2", "@types/react-router-dom": "^5.3.3", "antd": "^5.26.5", "axios": "^1.10.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^9.2.0", "react-router-dom": "^7.7.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "msw": "^2.10.4", "prettier": "^3.6.2", "ts-jest": "^29.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}