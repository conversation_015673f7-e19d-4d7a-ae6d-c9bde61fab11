import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { IsNotEmpty } from 'class-validator';
import { TaxonomicClassification } from './TaxonomicClassification';
import { MediaFile } from './MediaFile';
import { AudioFile } from './AudioFile';
import { DistributionRange } from './DistributionRange';

@Entity('species')
export class Species {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty({ message: '中文名不能为空' })
  chineseName: string;

  @Column({ nullable: true })
  englishName?: string;

  @Column()
  @IsNotEmpty({ message: '拉丁学名不能为空' })
  latinName: string;

  @Column({ nullable: true })
  conservationStatus?: string; // IUCN红色名录等级

  @Column('text', { nullable: true })
  description?: string;

  @Column({ nullable: true })
  representativeImagePath?: string; // 代表性图片路径

  @OneToOne(() => TaxonomicClassification, classification => classification.species, {
    cascade: true,
    eager: true
  })
  classification?: TaxonomicClassification;

  @OneToMany(() => MediaFile, mediaFile => mediaFile.species, {
    cascade: true
  })
  mediaFiles: MediaFile[];

  @OneToMany(() => AudioFile, audioFile => audioFile.species, {
    cascade: true
  })
  audioFiles: AudioFile[];

  @OneToMany(() => DistributionRange, distributionRange => distributionRange.species, {
    cascade: true
  })
  distributionRanges: DistributionRange[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
