import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button, Typography, Card } from 'antd';
import { ReloadOutlined, HomeOutlined } from '@ant-design/icons';

const { Paragraph, Text } = Typography;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // 这里可以将错误信息发送到错误监控服务
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div style={{ padding: '50px', minHeight: '400px' }}>
          <Result
            status="error"
            title="页面出现错误"
            subTitle="抱歉，页面遇到了一些问题。请尝试刷新页面或返回首页。"
            extra={[
              <Button type="primary" icon={<ReloadOutlined />} onClick={this.handleReload} key="reload">
                刷新页面
              </Button>,
              <Button icon={<HomeOutlined />} onClick={this.handleGoHome} key="home">
                返回首页
              </Button>,
            ]}
          >
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <Card
                title="错误详情 (仅开发环境显示)"
                style={{ marginTop: '24px', textAlign: 'left' }}
                size="small"
              >
                <Paragraph>
                  <Text strong>错误信息:</Text>
                  <br />
                  <Text code>{this.state.error.message}</Text>
                </Paragraph>
                
                <Paragraph>
                  <Text strong>错误堆栈:</Text>
                  <br />
                  <Text code style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
                    {this.state.error.stack}
                  </Text>
                </Paragraph>

                {this.state.errorInfo && (
                  <Paragraph>
                    <Text strong>组件堆栈:</Text>
                    <br />
                    <Text code style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
                      {this.state.errorInfo.componentStack}
                    </Text>
                  </Paragraph>
                )}
              </Card>
            )}
          </Result>
        </div>
      );
    }

    return this.props.children;
  }
}

// 简单的错误显示组件
export const ErrorDisplay: React.FC<{
  error?: string | Error;
  onRetry?: () => void;
  showRetry?: boolean;
}> = ({ error, onRetry, showRetry = true }) => {
  const errorMessage = typeof error === 'string' ? error : error?.message || '发生未知错误';

  return (
    <Result
      status="error"
      title="加载失败"
      subTitle={errorMessage}
      extra={
        showRetry && onRetry ? (
          <Button type="primary" onClick={onRetry}>
            重试
          </Button>
        ) : null
      }
    />
  );
};

// 网络错误组件
export const NetworkError: React.FC<{
  onRetry?: () => void;
}> = ({ onRetry }) => {
  return (
    <Result
      status="error"
      title="网络连接失败"
      subTitle="请检查您的网络连接，然后重试"
      extra={
        onRetry ? (
          <Button type="primary" icon={<ReloadOutlined />} onClick={onRetry}>
            重新加载
          </Button>
        ) : null
      }
    />
  );
};

// 404错误组件
export const NotFound: React.FC = () => {
  return (
    <Result
      status="404"
      title="404"
      subTitle="抱歉，您访问的页面不存在"
      extra={
        <Button type="primary" icon={<HomeOutlined />} onClick={() => window.location.href = '/'}>
          返回首页
        </Button>
      }
    />
  );
};

// 403权限错误组件
export const Forbidden: React.FC = () => {
  return (
    <Result
      status="403"
      title="403"
      subTitle="抱歉，您没有权限访问此页面"
      extra={
        <Button type="primary" icon={<HomeOutlined />} onClick={() => window.location.href = '/'}>
          返回首页
        </Button>
      }
    />
  );
};

// 空状态组件
export const EmptyState: React.FC<{
  title?: string;
  description?: string;
  action?: ReactNode;
  image?: ReactNode;
}> = ({ 
  title = '暂无数据', 
  description = '当前没有可显示的内容',
  action,
  image
}) => {
  return (
    <Result
      icon={image}
      title={title}
      subTitle={description}
      extra={action}
    />
  );
};

export default ErrorBoundary;
