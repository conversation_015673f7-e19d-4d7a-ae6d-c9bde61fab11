import React from 'react';
import { screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, createMockFile, createMockDragEvent } from '../utils/test-utils';
import FileUpload from '../../components/FileUpload';
import { server } from '../../__mocks__/server';
import { rest } from 'msw';

describe('文件上传集成测试', () => {
  beforeEach(() => {
    // 重置fetch模拟
    global.fetch = jest.fn();
  });

  describe('音频文件上传', () => {
    it('应该完成完整的音频文件上传流程', async () => {
      const user = userEvent.setup();
      const mockOnUploadComplete = jest.fn();
      
      render(
        <FileUpload 
          speciesId="1"
          defaultActiveKey="audio"
          onUploadComplete={mockOnUploadComplete}
        />
      );
      
      // 1. 验证音频上传界面
      expect(screen.getByText('音频文件上传')).toBeInTheDocument();
      expect(screen.getByText('点击或拖拽文件到此区域上传')).toBeInTheDocument();
      
      // 2. 创建模拟音频文件
      const audioFile = createMockFile('test-audio.wav', 1024 * 1024, 'audio/wav');
      
      // 3. 模拟文件选择
      const fileInput = screen.getByRole('button', { name: /选择文件/ });
      const hiddenInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      
      if (hiddenInput) {
        Object.defineProperty(hiddenInput, 'files', {
          value: [audioFile],
          writable: false,
        });
        fireEvent.change(hiddenInput);
      }
      
      // 4. 验证文件已添加到列表
      await waitFor(() => {
        expect(screen.getByText('test-audio.wav')).toBeInTheDocument();
      });
      
      // 5. 填写音频信息表单
      await waitFor(() => {
        expect(screen.getByText('音频信息')).toBeInTheDocument();
      });
      
      const locationInput = screen.getByLabelText('录音地点');
      const behaviorSelect = screen.getByLabelText('行为描述');
      
      await user.type(locationInput, '太平洋');
      await user.click(behaviorSelect);
      await user.click(screen.getByText('觅食'));
      
      // 6. 保存音频信息
      const saveButton = screen.getByRole('button', { name: /保存音频信息/ });
      await user.click(saveButton);
      
      // 7. 验证上传完成
      await waitFor(() => {
        expect(mockOnUploadComplete).toHaveBeenCalled();
      });
    });

    it('应该验证音频文件类型', async () => {
      const user = userEvent.setup();
      
      render(<FileUpload defaultActiveKey="audio" />);
      
      // 创建非音频文件
      const textFile = createMockFile('test.txt', 1024, 'text/plain');
      
      // 尝试上传
      const hiddenInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      
      if (hiddenInput) {
        Object.defineProperty(hiddenInput, 'files', {
          value: [textFile],
          writable: false,
        });
        fireEvent.change(hiddenInput);
      }
      
      // 验证错误提示
      await waitFor(() => {
        expect(screen.getByText(/不支持的文件类型/)).toBeInTheDocument();
      });
    });

    it('应该验证文件大小限制', async () => {
      render(<FileUpload defaultActiveKey="audio" />);
      
      // 创建超大文件 (100MB+)
      const largeFile = createMockFile('large-audio.wav', 100 * 1024 * 1024 + 1, 'audio/wav');
      
      // 尝试上传
      const hiddenInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      
      if (hiddenInput) {
        Object.defineProperty(hiddenInput, 'files', {
          value: [largeFile],
          writable: false,
        });
        fireEvent.change(hiddenInput);
      }
      
      // 验证大小限制错误
      await waitFor(() => {
        expect(screen.getByText(/文件大小不能超过/)).toBeInTheDocument();
      });
    });
  });

  describe('图片文件上传', () => {
    it('应该完成完整的图片文件上传流程', async () => {
      const user = userEvent.setup();
      
      render(<FileUpload defaultActiveKey="image" />);
      
      // 切换到图片上传标签
      const imageTab = screen.getByText('图片上传');
      await user.click(imageTab);
      
      // 验证图片上传界面
      expect(screen.getByText('图片文件上传')).toBeInTheDocument();
      
      // 创建模拟图片文件
      const imageFile = createMockFile('test-image.jpg', 1024 * 1024, 'image/jpeg');
      
      // 模拟文件上传
      const hiddenInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      
      if (hiddenInput) {
        Object.defineProperty(hiddenInput, 'files', {
          value: [imageFile],
          writable: false,
        });
        fireEvent.change(hiddenInput);
      }
      
      // 验证图片预览
      await waitFor(() => {
        expect(screen.getByText('图片预览')).toBeInTheDocument();
        expect(screen.getByAltText('test-image.jpg')).toBeInTheDocument();
      });
      
      // 填写图片信息
      const descriptionInput = screen.getByLabelText('图片描述');
      const typeSelect = screen.getByLabelText('图片类型');
      
      await user.type(descriptionInput, '蓝鲸游泳照片');
      await user.click(typeSelect);
      await user.click(screen.getByText('代表性图片'));
      
      // 保存图片信息
      const saveButton = screen.getByRole('button', { name: /保存图片信息/ });
      await user.click(saveButton);
      
      // 验证保存成功
      await waitFor(() => {
        expect(screen.getByText(/成功保存.*个图片文件/)).toBeInTheDocument();
      });
    });
  });

  describe('拖拽上传', () => {
    it('应该支持拖拽上传文件', async () => {
      render(<FileUpload defaultActiveKey="audio" />);
      
      // 创建模拟文件
      const audioFile = createMockFile('drag-audio.wav', 1024 * 1024, 'audio/wav');
      
      // 查找拖拽区域
      const dropZone = screen.getByText('点击或拖拽文件到此区域上传').closest('.ant-upload-drag');
      
      if (dropZone) {
        // 模拟拖拽事件
        const dragEvent = createMockDragEvent([audioFile]);
        
        fireEvent.dragEnter(dropZone, dragEvent);
        fireEvent.dragOver(dropZone, dragEvent);
        fireEvent.drop(dropZone, dragEvent);
      }
      
      // 验证文件已添加
      await waitFor(() => {
        expect(screen.getByText('drag-audio.wav')).toBeInTheDocument();
      });
    });

    it('应该在拖拽时显示视觉反馈', async () => {
      render(<FileUpload defaultActiveKey="audio" />);
      
      const audioFile = createMockFile('test.wav', 1024, 'audio/wav');
      const dropZone = screen.getByText('点击或拖拽文件到此区域上传').closest('.ant-upload-drag');
      
      if (dropZone) {
        const dragEvent = createMockDragEvent([audioFile]);
        
        // 拖拽进入
        fireEvent.dragEnter(dropZone, dragEvent);
        
        // 验证样式变化（这里需要根据实际实现来验证）
        expect(dropZone).toHaveClass('ant-upload-drag-hover');
        
        // 拖拽离开
        fireEvent.dragLeave(dropZone, dragEvent);
        
        expect(dropZone).not.toHaveClass('ant-upload-drag-hover');
      }
    });
  });

  describe('上传进度', () => {
    it('应该显示上传进度', async () => {
      // 模拟慢速上传
      server.use(
        rest.post('http://localhost:3001/api/files/upload/audio', async (req, res, ctx) => {
          // 延迟响应以模拟上传过程
          await new Promise(resolve => setTimeout(resolve, 1000));
          return res(
            ctx.status(200),
            ctx.json({
              success: true,
              data: {
                originalName: 'test-audio.wav',
                path: 'uploads/test-audio.wav',
                size: 1024000,
                mimeType: 'audio/wav',
                url: 'http://localhost:3001/files/uploads/test-audio.wav'
              }
            })
          );
        })
      );
      
      render(<FileUpload defaultActiveKey="audio" />);
      
      const audioFile = createMockFile('test-audio.wav', 1024 * 1024, 'audio/wav');
      
      // 开始上传
      const hiddenInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      
      if (hiddenInput) {
        Object.defineProperty(hiddenInput, 'files', {
          value: [audioFile],
          writable: false,
        });
        fireEvent.change(hiddenInput);
      }
      
      // 验证进度显示
      await waitFor(() => {
        expect(screen.getByText('正在上传...')).toBeInTheDocument();
      });
      
      // 等待上传完成
      await waitFor(() => {
        expect(screen.queryByText('正在上传...')).not.toBeInTheDocument();
      }, { timeout: 2000 });
    });
  });

  describe('错误处理', () => {
    it('应该处理上传失败', async () => {
      // 模拟上传失败
      server.use(
        rest.post('http://localhost:3001/api/files/upload/audio', (req, res, ctx) => {
          return res(
            ctx.status(500),
            ctx.json({
              success: false,
              error: { message: '服务器错误' }
            })
          );
        })
      );
      
      render(<FileUpload defaultActiveKey="audio" />);
      
      const audioFile = createMockFile('test-audio.wav', 1024, 'audio/wav');
      
      // 尝试上传
      const hiddenInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      
      if (hiddenInput) {
        Object.defineProperty(hiddenInput, 'files', {
          value: [audioFile],
          writable: false,
        });
        fireEvent.change(hiddenInput);
      }
      
      // 验证错误提示
      await waitFor(() => {
        expect(screen.getByText('服务器错误')).toBeInTheDocument();
      });
    });

    it('应该处理网络错误', async () => {
      // 模拟网络错误
      server.use(
        rest.post('http://localhost:3001/api/files/upload/audio', (req, res, ctx) => {
          return res.networkError('Network error');
        })
      );
      
      render(<FileUpload defaultActiveKey="audio" />);
      
      const audioFile = createMockFile('test-audio.wav', 1024, 'audio/wav');
      
      // 尝试上传
      const hiddenInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      
      if (hiddenInput) {
        Object.defineProperty(hiddenInput, 'files', {
          value: [audioFile],
          writable: false,
        });
        fireEvent.change(hiddenInput);
      }
      
      // 验证网络错误提示
      await waitFor(() => {
        expect(screen.getByText(/网络错误/)).toBeInTheDocument();
      });
    });
  });

  describe('文件管理', () => {
    it('应该显示已上传的文件列表', async () => {
      const user = userEvent.setup();
      
      render(<FileUpload defaultActiveKey="manager" />);
      
      // 切换到文件管理标签
      const managerTab = screen.getByText('文件管理');
      await user.click(managerTab);
      
      // 验证文件列表显示
      await waitFor(() => {
        expect(screen.getByText('文件管理')).toBeInTheDocument();
        expect(screen.getByRole('table')).toBeInTheDocument();
      });
    });

    it('应该支持文件删除', async () => {
      const user = userEvent.setup();
      
      render(<FileUpload defaultActiveKey="manager" />);
      
      // 切换到文件管理标签
      const managerTab = screen.getByText('文件管理');
      await user.click(managerTab);
      
      // 等待文件列表加载
      await waitFor(() => {
        expect(screen.getByRole('table')).toBeInTheDocument();
      });
      
      // 查找删除按钮并点击
      const deleteButton = screen.getByRole('button', { name: /删除/ });
      await user.click(deleteButton);
      
      // 确认删除
      const confirmButton = screen.getByRole('button', { name: /确定/ });
      await user.click(confirmButton);
      
      // 验证删除成功提示
      await waitFor(() => {
        expect(screen.getByText('文件删除成功')).toBeInTheDocument();
      });
    });
  });
});
