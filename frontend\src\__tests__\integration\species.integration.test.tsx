import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../utils/test-utils';
import App from '../../App';
import { server } from '../../__mocks__/server';
import { rest } from 'msw';

describe('物种数据集成测试', () => {
  describe('物种搜索流程', () => {
    it('应该完成完整的物种搜索流程', async () => {
      const user = userEvent.setup();
      render(<App />);
      
      // 1. 导航到物种数据库页面
      const speciesLink = screen.getByText('物种数据库');
      await user.click(speciesLink);
      
      // 2. 验证物种列表页面已加载
      await waitFor(() => {
        expect(screen.getByText('物种数据库')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('搜索物种名称...')).toBeInTheDocument();
      });
      
      // 3. 执行搜索
      const searchInput = screen.getByPlaceholderText('搜索物种名称...');
      await user.type(searchInput, '蓝鲸');
      await user.keyboard('{Enter}');
      
      // 4. 验证搜索结果显示
      await waitFor(() => {
        expect(screen.getByText('蓝鲸')).toBeInTheDocument();
        expect(screen.getByText('Balaenoptera musculus')).toBeInTheDocument();
      });
      
      // 5. 点击物种卡片查看详情
      const speciesCard = screen.getByText('蓝鲸');
      await user.click(speciesCard);
      
      // 6. 验证物种详情页面已加载
      await waitFor(() => {
        expect(screen.getByText('物种详情')).toBeInTheDocument();
        expect(screen.getByText('基本信息')).toBeInTheDocument();
      });
    });

    it('应该处理搜索无结果的情况', async () => {
      const user = userEvent.setup();
      
      // 模拟无搜索结果
      server.use(
        rest.get('http://localhost:3001/api/species/search', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json({
              success: true,
              data: {
                species: [],
                total: 0,
                page: 1,
                limit: 20,
                totalPages: 0
              }
            })
          );
        })
      );
      
      render(<App />);
      
      // 导航到物种数据库页面
      const speciesLink = screen.getByText('物种数据库');
      await user.click(speciesLink);
      
      // 搜索不存在的物种
      const searchInput = screen.getByPlaceholderText('搜索物种名称...');
      await user.type(searchInput, '不存在的物种');
      await user.keyboard('{Enter}');
      
      // 验证无结果提示
      await waitFor(() => {
        expect(screen.getByText('暂无数据')).toBeInTheDocument();
      });
    });

    it('应该支持高级搜索筛选', async () => {
      const user = userEvent.setup();
      render(<App />);
      
      // 导航到物种数据库页面
      const speciesLink = screen.getByText('物种数据库');
      await user.click(speciesLink);
      
      // 打开高级搜索
      const advancedSearchButton = screen.getByText('高级搜索');
      await user.click(advancedSearchButton);
      
      // 设置筛选条件
      const conservationSelect = screen.getByLabelText('保护状态');
      await user.click(conservationSelect);
      await user.click(screen.getByText('濒危'));
      
      const kingdomSelect = screen.getByLabelText('界');
      await user.click(kingdomSelect);
      await user.click(screen.getByText('动物界'));
      
      // 执行搜索
      const searchButton = screen.getByRole('button', { name: /搜索/ });
      await user.click(searchButton);
      
      // 验证筛选标签显示
      await waitFor(() => {
        expect(screen.getByText('保护状态: 濒危')).toBeInTheDocument();
        expect(screen.getByText('界: 动物界')).toBeInTheDocument();
      });
    });
  });

  describe('物种详情页面', () => {
    it('应该显示完整的物种信息', async () => {
      const user = userEvent.setup();
      render(<App />);
      
      // 直接导航到物种详情页面
      // 这里需要模拟路由导航
      window.history.pushState({}, '', '/species/1');
      
      // 验证物种详情内容
      await waitFor(() => {
        expect(screen.getByText('蓝鲸')).toBeInTheDocument();
        expect(screen.getByText('Balaenoptera musculus')).toBeInTheDocument();
        expect(screen.getByText('基本信息')).toBeInTheDocument();
        expect(screen.getByText('分类信息')).toBeInTheDocument();
      });
    });

    it('应该显示音频文件列表', async () => {
      const user = userEvent.setup();
      render(<App />);
      
      // 导航到物种详情页面
      window.history.pushState({}, '', '/species/1');
      
      // 验证音频文件部分
      await waitFor(() => {
        expect(screen.getByText('音频文件')).toBeInTheDocument();
        expect(screen.getByText('blue-whale-song.wav')).toBeInTheDocument();
      });
      
      // 点击播放按钮
      const playButton = screen.getByRole('button', { name: /播放/ });
      await user.click(playButton);
      
      // 验证音频播放器激活
      // 这里需要根据实际的音频播放器实现来验证
    });

    it('应该处理物种不存在的情况', async () => {
      // 模拟物种不存在
      server.use(
        rest.get('http://localhost:3001/api/species/:id/details', (req, res, ctx) => {
          return res(
            ctx.status(404),
            ctx.json({
              success: false,
              error: { message: '物种不存在' }
            })
          );
        })
      );
      
      render(<App />);
      
      // 导航到不存在的物种页面
      window.history.pushState({}, '', '/species/nonexistent');
      
      // 验证404页面或错误提示
      await waitFor(() => {
        expect(screen.getByText('404')).toBeInTheDocument();
      });
    });
  });

  describe('分页和排序', () => {
    it('应该支持分页导航', async () => {
      const user = userEvent.setup();
      
      // 模拟多页数据
      server.use(
        rest.get('http://localhost:3001/api/species/search', (req, res, ctx) => {
          const page = req.url.searchParams.get('page') || '1';
          return res(
            ctx.status(200),
            ctx.json({
              success: true,
              data: {
                species: [
                  {
                    id: `${page}-1`,
                    chineseName: `物种${page}-1`,
                    latinName: 'Species test',
                    conservationStatus: '无危'
                  }
                ],
                total: 50,
                page: parseInt(page),
                limit: 20,
                totalPages: 3
              }
            })
          );
        })
      );
      
      render(<App />);
      
      // 导航到物种数据库页面
      const speciesLink = screen.getByText('物种数据库');
      await user.click(speciesLink);
      
      // 等待数据加载
      await waitFor(() => {
        expect(screen.getByText('物种1-1')).toBeInTheDocument();
      });
      
      // 点击下一页
      const nextPageButton = screen.getByRole('button', { name: /下一页/ });
      await user.click(nextPageButton);
      
      // 验证第二页数据
      await waitFor(() => {
        expect(screen.getByText('物种2-1')).toBeInTheDocument();
      });
    });

    it('应该支持排序功能', async () => {
      const user = userEvent.setup();
      render(<App />);
      
      // 导航到物种数据库页面
      const speciesLink = screen.getByText('物种数据库');
      await user.click(speciesLink);
      
      // 点击排序选项
      const sortSelect = screen.getByLabelText('排序方式');
      await user.click(sortSelect);
      await user.click(screen.getByText('拉丁名'));
      
      // 验证排序参数被发送
      // 这里需要验证API请求参数
    });
  });

  describe('搜索历史', () => {
    it('应该保存和显示搜索历史', async () => {
      const user = userEvent.setup();
      render(<App />);
      
      // 导航到物种数据库页面
      const speciesLink = screen.getByText('物种数据库');
      await user.click(speciesLink);
      
      // 执行搜索
      const searchInput = screen.getByPlaceholderText('搜索物种名称...');
      await user.type(searchInput, '蓝鲸');
      await user.keyboard('{Enter}');
      
      // 清空搜索框并重新聚焦
      await user.clear(searchInput);
      await user.click(searchInput);
      
      // 验证搜索历史显示
      await waitFor(() => {
        expect(screen.getByText('蓝鲸')).toBeInTheDocument();
      });
    });

    it('应该支持清除搜索历史', async () => {
      const user = userEvent.setup();
      render(<App />);
      
      // 导航到物种数据库页面
      const speciesLink = screen.getByText('物种数据库');
      await user.click(speciesLink);
      
      // 执行搜索以创建历史
      const searchInput = screen.getByPlaceholderText('搜索物种名称...');
      await user.type(searchInput, '蓝鲸');
      await user.keyboard('{Enter}');
      
      // 清空搜索框并显示历史
      await user.clear(searchInput);
      await user.click(searchInput);
      
      // 点击清除历史
      const clearHistoryButton = screen.getByText('清除搜索历史');
      await user.click(clearHistoryButton);
      
      // 验证历史已清除
      expect(screen.queryByText('蓝鲸')).not.toBeInTheDocument();
    });
  });
});
