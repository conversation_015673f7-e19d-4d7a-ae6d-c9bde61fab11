import React from 'react';
import { Card, Typography, Result, Button } from 'antd';
import { useNavigate } from 'react-router-dom';

const { Title } = Typography;

const RoleManagementPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>角色管理</Title>
      </div>

      <Card>
        <Result
          status="info"
          title="功能开发中"
          subTitle="角色管理功能正在开发中，敬请期待。"
          extra={
            <Button type="primary" onClick={() => navigate('/users')}>
              返回用户管理
            </Button>
          }
        />
      </Card>
    </div>
  );
};

export default RoleManagementPage;
