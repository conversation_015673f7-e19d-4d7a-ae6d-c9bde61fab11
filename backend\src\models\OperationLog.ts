import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { IsNotEmpty } from 'class-validator';
import { User } from './User';

@Entity('operation_logs')
export class OperationLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty({ message: '操作类型不能为空' })
  action: string; // create, update, delete, login, logout

  @Column()
  @IsNotEmpty({ message: '资源类型不能为空' })
  resource: string; // user, species, media, audio

  @Column({ nullable: true })
  resourceId?: string; // 资源ID

  @Column('jsonb', { nullable: true })
  details?: Record<string, any>; // 操作详情

  @Column({ nullable: true })
  ipAddress?: string;

  @Column('text', { nullable: true })
  userAgent?: string;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @CreateDateColumn()
  createdAt: Date;
}
