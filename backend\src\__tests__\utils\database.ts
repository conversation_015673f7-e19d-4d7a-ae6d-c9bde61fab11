import { DataSource } from 'typeorm';
import { User } from '../../models/User';
import { Role } from '../../models/Role';
import { Species } from '../../models/Species';
import { AudioFile } from '../../models/AudioFile';
import { ImageFile } from '../../models/ImageFile';
import { VideoFile } from '../../models/VideoFile';
import { SpeciesDistribution } from '../../models/SpeciesDistribution';

// 测试数据库配置
const testDatabaseConfig = {
  type: 'postgres' as const,
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  username: process.env.DB_USERNAME || 'test_user',
  password: process.env.DB_PASSWORD || 'test_password',
  database: process.env.DB_DATABASE || 'marine_bio_test',
  synchronize: true, // 测试环境自动同步表结构
  dropSchema: true,  // 每次启动时删除现有表
  logging: false,    // 关闭SQL日志
  entities: [
    User,
    Role,
    Species,
    AudioFile,
    ImageFile,
    VideoFile,
    SpeciesDistribution,
  ],
};

// 创建测试数据库连接
export const createTestDatabase = async (): Promise<DataSource> => {
  const dataSource = new DataSource(testDatabaseConfig);
  
  try {
    await dataSource.initialize();
    console.log('Test database connected successfully');
    
    // 创建基础测试数据
    await seedTestData(dataSource);
    
    return dataSource;
  } catch (error) {
    console.error('Failed to connect to test database:', error);
    throw error;
  }
};

// 关闭测试数据库连接
export const closeTestDatabase = async (dataSource: DataSource): Promise<void> => {
  if (dataSource && dataSource.isInitialized) {
    await dataSource.destroy();
    console.log('Test database connection closed');
  }
};

// 创建基础测试数据
const seedTestData = async (dataSource: DataSource): Promise<void> => {
  const roleRepository = dataSource.getRepository(Role);
  const userRepository = dataSource.getRepository(User);
  
  // 创建角色
  const adminRole = roleRepository.create({
    name: 'admin',
    description: '管理员',
    permissions: {
      species: { create: true, read: true, update: true, delete: true },
      files: { create: true, read: true, update: true, delete: true },
      users: { create: true, read: true, update: true, delete: true },
    },
  });
  
  const editorRole = roleRepository.create({
    name: 'editor',
    description: '编辑者',
    permissions: {
      species: { create: true, read: true, update: true, delete: false },
      files: { create: true, read: true, update: true, delete: false },
      users: { create: false, read: true, update: false, delete: false },
    },
  });
  
  const viewerRole = roleRepository.create({
    name: 'viewer',
    description: '查看者',
    permissions: {
      species: { create: false, read: true, update: false, delete: false },
      files: { create: false, read: true, update: false, delete: false },
      users: { create: false, read: true, update: false, delete: false },
    },
  });
  
  await roleRepository.save([adminRole, editorRole, viewerRole]);
  
  // 创建测试用户
  const testUser = userRepository.create({
    username: 'testuser',
    email: '<EMAIL>',
    passwordHash: '$2b$10$test.hash.for.testing.purposes.only', // 测试用的固定hash
    roles: [viewerRole],
  });
  
  const adminUser = userRepository.create({
    username: 'admin',
    email: '<EMAIL>',
    passwordHash: '$2b$10$admin.hash.for.testing.purposes.only',
    roles: [adminRole],
  });
  
  await userRepository.save([testUser, adminUser]);
};

// 创建测试物种数据
export const createTestSpecies = async (dataSource: DataSource, overrides: Partial<Species> = {}): Promise<Species> => {
  const speciesRepository = dataSource.getRepository(Species);
  
  const species = speciesRepository.create({
    chineseName: '测试蓝鲸',
    englishName: 'Test Blue Whale',
    latinName: 'Balaenoptera musculus test',
    conservationStatus: '濒危',
    description: '这是一个测试物种',
    classification: {
      kingdom: '动物界',
      phylum: '脊索动物门',
      class: '哺乳纲',
      order: '鲸目',
      family: '须鲸科',
      genus: '蓝鲸属',
      species: '蓝鲸',
    },
    ...overrides,
  });
  
  return await speciesRepository.save(species);
};

// 创建测试用户
export const createTestUser = async (
  dataSource: DataSource, 
  overrides: Partial<User> = {}
): Promise<User> => {
  const userRepository = dataSource.getRepository(User);
  const roleRepository = dataSource.getRepository(Role);
  
  // 获取默认角色
  const defaultRole = await roleRepository.findOne({ where: { name: 'viewer' } });
  
  const user = userRepository.create({
    username: 'testuser' + Date.now(),
    email: `test${Date.now()}@example.com`,
    passwordHash: '$2b$10$test.hash.for.testing.purposes.only',
    roles: defaultRole ? [defaultRole] : [],
    ...overrides,
  });
  
  return await userRepository.save(user);
};

// 创建测试音频文件
export const createTestAudioFile = async (
  dataSource: DataSource,
  species: Species,
  overrides: Partial<AudioFile> = {}
): Promise<AudioFile> => {
  const audioFileRepository = dataSource.getRepository(AudioFile);
  
  const audioFile = audioFileRepository.create({
    fileName: 'test-audio.wav',
    filePath: 'uploads/audio/test-audio.wav',
    fileSize: 1024000,
    mimeType: 'audio/wav',
    recordingLocation: '测试海域',
    recordingTime: new Date(),
    behaviorDescription: '测试行为',
    species,
    ...overrides,
  });
  
  return await audioFileRepository.save(audioFile);
};

// 清理测试数据
export const clearTestData = async (dataSource: DataSource): Promise<void> => {
  const entities = dataSource.entityMetadatas;
  
  // 按依赖关系逆序删除
  const entityNames = entities.map(entity => entity.name).reverse();
  
  for (const entityName of entityNames) {
    const repository = dataSource.getRepository(entityName);
    await repository.clear();
  }
};

// 获取测试数据源
export const getTestDataSource = (): DataSource => {
  return (global as any).testDataSource;
};
