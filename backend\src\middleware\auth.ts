import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AppDataSource } from '../config/database';
import { User, Role } from '../models';

// 扩展Request接口以包含用户信息
declare global {
  namespace Express {
    interface Request {
      user?: User;
      permissions?: Record<string, any>;
    }
  }
}

export interface JWTPayload {
  userId: string;
  username: string;
  roles: string[];
  iat?: number;
  exp?: number;
}

/**
 * JWT认证中间件
 */
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'NO_TOKEN',
          message: '访问令牌缺失',
        },
      });
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error('JWT_SECRET未配置');
    }

    // 验证token
    const decoded = jwt.verify(token, jwtSecret) as JWTPayload;
    
    // 从数据库获取用户信息
    const userRepository = AppDataSource.getRepository(User);
    const user = await userRepository.findOne({
      where: { id: decoded.userId },
      relations: ['roles'],
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: '用户不存在',
        },
      });
    }

    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        error: {
          code: 'USER_DISABLED',
          message: '用户账户已被禁用',
        },
      });
    }

    // 合并用户权限
    const permissions = mergePermissions(user.roles);

    req.user = user;
    req.permissions = permissions;
    next();
  } catch (error: any) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: '无效的访问令牌',
        },
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: {
          code: 'TOKEN_EXPIRED',
          message: '访问令牌已过期',
        },
      });
    }

    console.error('认证中间件错误:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'AUTH_ERROR',
        message: '认证过程中发生错误',
      },
    });
  }
};

/**
 * 权限验证中间件工厂
 */
export const requirePermission = (resource: string, action: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user || !req.permissions) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '未授权访问',
        },
      });
    }

    // 检查是否有全部权限
    if (req.permissions.all === true) {
      return next();
    }

    // 检查特定资源权限
    const resourcePermissions = req.permissions[resource];
    if (!resourcePermissions) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: `缺少${resource}资源的访问权限`,
        },
      });
    }

    // 检查特定操作权限
    if (resourcePermissions[action] !== true && resourcePermissions.all !== true) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: `缺少${resource}资源的${action}操作权限`,
        },
      });
    }

    next();
  };
};

/**
 * 角色验证中间件
 */
export const requireRole = (roleName: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '未授权访问',
        },
      });
    }

    const hasRole = req.user.roles.some(role => role.name === roleName);
    if (!hasRole) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_ROLE',
          message: `需要${roleName}角色权限`,
        },
      });
    }

    next();
  };
};

/**
 * 可选认证中间件（不强制要求登录）
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return next(); // 没有token，继续执行但不设置用户信息
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      return next();
    }

    const decoded = jwt.verify(token, jwtSecret) as JWTPayload;
    const userRepository = AppDataSource.getRepository(User);
    const user = await userRepository.findOne({
      where: { id: decoded.userId },
      relations: ['roles'],
    });

    if (user && user.status === 'active') {
      req.user = user;
      req.permissions = mergePermissions(user.roles);
    }

    next();
  } catch (error) {
    // 忽略认证错误，继续执行
    next();
  }
};

/**
 * 合并用户角色权限
 */
function mergePermissions(roles: Role[]): Record<string, any> {
  const mergedPermissions: Record<string, any> = {};

  for (const role of roles) {
    if (role.permissions) {
      // 如果有全部权限，直接返回
      if (role.permissions.all === true) {
        return { all: true };
      }

      // 合并权限
      for (const [resource, permissions] of Object.entries(role.permissions)) {
        if (!mergedPermissions[resource]) {
          mergedPermissions[resource] = {};
        }

        if (typeof permissions === 'object' && permissions !== null) {
          Object.assign(mergedPermissions[resource], permissions);
        }
      }
    }
  }

  return mergedPermissions;
}

/**
 * 生成JWT令牌
 */
export const generateToken = (user: User): string => {
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    throw new Error('JWT_SECRET未配置');
  }

  const payload: JWTPayload = {
    userId: user.id,
    username: user.username,
    roles: user.roles.map(role => role.name),
  };

  const expiresIn = process.env.JWT_EXPIRES_IN || '7d';
  return jwt.sign(payload, jwtSecret, { expiresIn } as jwt.SignOptions);
};

/**
 * 验证JWT令牌
 */
export const verifyToken = (token: string): JWTPayload => {
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    throw new Error('JWT_SECRET未配置');
  }

  return jwt.verify(token, jwtSecret) as JWTPayload;
};
