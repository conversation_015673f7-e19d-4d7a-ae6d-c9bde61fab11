import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { IsNotEmpty, IsNumber, IsOptional } from 'class-validator';
import { Species } from './Species';

@Entity('audio_files')
export class AudioFile {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty({ message: '文件路径不能为空' })
  filePath: string;

  @Column()
  @IsNotEmpty({ message: '文件名不能为空' })
  fileName: string;

  @Column('bigint', { nullable: true })
  fileSize?: number;

  @Column({ nullable: true })
  mimeType?: string;

  @Column({ nullable: true })
  recordingLocation?: string; // 录音地点

  @Column({ nullable: true })
  recordingTime?: Date; // 录音时间

  @Column('text', { nullable: true })
  behaviorDescription?: string; // 行为描述

  @Column({ nullable: true })
  @IsOptional()
  @IsNumber({}, { message: '采样率必须是数字' })
  samplingRate?: number; // 采样率

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  sensitivity?: number; // 灵敏度

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  amplificationFactor?: number; // 放大倍数

  @Column('decimal', { precision: 10, scale: 8, nullable: true })
  latitude?: number; // 纬度

  @Column('decimal', { precision: 11, scale: 8, nullable: true })
  longitude?: number; // 经度

  @ManyToOne(() => Species, species => species.audioFiles, {
    onDelete: 'CASCADE'
  })
  @JoinColumn({ name: 'species_id' })
  species: Species;

  @CreateDateColumn()
  createdAt: Date;
}
