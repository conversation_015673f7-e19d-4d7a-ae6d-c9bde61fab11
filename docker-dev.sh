#!/bin/bash

# Docker开发环境管理脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo "用法: $0 {up|down|restart|logs|status|clean}"
    echo ""
    echo "命令:"
    echo "  up      - 启动开发环境"
    echo "  down    - 停止开发环境"
    echo "  restart - 重启开发环境"
    echo "  logs    - 查看服务日志"
    echo "  status  - 查看服务状态"
    echo "  clean   - 清理开发环境（删除所有数据）"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose未安装，请先安装Docker Compose${NC}"
        exit 1
    fi
}

# 启动服务
start_services() {
    echo -e "${GREEN}🚀 启动开发环境...${NC}"
    docker-compose up -d
    echo -e "${GREEN}✅ 服务启动完成!${NC}"
    echo ""
    echo -e "${YELLOW}📊 服务信息:${NC}"
    echo -e "  ${CYAN}PostgreSQL: localhost:5432${NC}"
    echo -e "  ${CYAN}Redis: localhost:6379${NC}"
    echo -e "  ${CYAN}pgAdmin: http://localhost:8080 (可选)${NC}"
    echo ""
    echo -e "${YELLOW}🔍 查看服务状态: $0 status${NC}"
    echo -e "${YELLOW}📋 查看日志: $0 logs${NC}"
}

# 停止服务
stop_services() {
    echo -e "${RED}🛑 停止开发环境...${NC}"
    docker-compose down
    echo -e "${GREEN}✅ 服务已停止!${NC}"
}

# 重启服务
restart_services() {
    echo -e "${YELLOW}🔄 重启开发环境...${NC}"
    docker-compose restart
    echo -e "${GREEN}✅ 服务已重启!${NC}"
}

# 查看日志
show_logs() {
    echo -e "${BLUE}📋 显示服务日志...${NC}"
    docker-compose logs -f
}

# 查看状态
show_status() {
    echo -e "${YELLOW}📊 服务状态:${NC}"
    docker-compose ps
    echo ""
    echo -e "${YELLOW}🔍 健康检查:${NC}"
    if docker-compose exec postgres pg_isready -U postgres -d marine_bio_platform; then
        echo -e "${GREEN}✅ PostgreSQL连接正常${NC}"
    else
        echo -e "${RED}❌ PostgreSQL连接失败${NC}"
    fi
    
    if docker-compose exec redis redis-cli ping; then
        echo -e "${GREEN}✅ Redis连接正常${NC}"
    else
        echo -e "${RED}❌ Redis连接失败${NC}"
    fi
}

# 清理环境
clean_environment() {
    echo -e "${RED}🧹 清理开发环境...${NC}"
    echo -e "${RED}⚠️  这将删除所有数据！按Enter继续，或Ctrl+C取消...${NC}"
    read
    docker-compose down -v
    docker system prune -f
    echo -e "${GREEN}✅ 清理完成!${NC}"
}

# 主逻辑
check_docker

case "$1" in
    up)
        start_services
        ;;
    down)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    clean)
        clean_environment
        ;;
    *)
        show_help
        exit 1
        ;;
esac
