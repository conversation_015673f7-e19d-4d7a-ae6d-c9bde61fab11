import React, { useEffect, useState } from 'react';
import { GeoJSON, Popup } from 'react-leaflet';
import { Typography, Tag, Space, Button } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import L from 'leaflet';

const { Text, Title } = Typography;

interface DistributionData {
  id: string;
  speciesId: string;
  chineseName: string;
  latinName: string;
  geometry: any; // GeoJSON geometry
}

interface DistributionLayerProps {
  data: DistributionData[];
  onSpeciesClick?: (speciesId: string) => void;
}

const DistributionLayer: React.FC<DistributionLayerProps> = ({
  data,
  onSpeciesClick,
}) => {
  const [geoJsonData, setGeoJsonData] = useState<any>(null);

  useEffect(() => {
    if (data && data.length > 0) {
      // 转换数据为GeoJSON格式
      const features = data.map(item => ({
        type: 'Feature',
        properties: {
          id: item.id,
          speciesId: item.speciesId,
          chineseName: item.chineseName,
          latinName: item.latinName,
        },
        geometry: item.geometry,
      }));

      const geoJson = {
        type: 'FeatureCollection',
        features,
      };

      setGeoJsonData(geoJson);
    }
  }, [data]);

  // 样式函数
  const getFeatureStyle = (feature: any) => {
    return {
      fillColor: '#1890ff',
      weight: 2,
      opacity: 1,
      color: '#096dd9',
      dashArray: '',
      fillOpacity: 0.3,
    };
  };

  // 鼠标悬停样式
  const highlightFeature = (e: any) => {
    const layer = e.target;
    layer.setStyle({
      weight: 3,
      color: '#722ed1',
      dashArray: '',
      fillOpacity: 0.5,
    });
    layer.bringToFront();
  };

  // 重置样式
  const resetHighlight = (e: any) => {
    const layer = e.target;
    layer.setStyle(getFeatureStyle(e.target.feature));
  };

  // 点击事件
  const onEachFeature = (feature: any, layer: any) => {
    layer.on({
      mouseover: highlightFeature,
      mouseout: resetHighlight,
      click: (e: any) => {
        // 缩放到该区域
        const bounds = layer.getBounds();
        e.target._map.fitBounds(bounds);
      },
    });

    // 绑定弹出框
    const popupContent = (
      <div style={{ minWidth: '200px' }}>
        <Title level={5} style={{ margin: '0 0 8px 0' }}>
          {feature.properties.chineseName}
        </Title>
        <Text type="secondary" italic style={{ display: 'block', marginBottom: '12px' }}>
          {feature.properties.latinName}
        </Text>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button
            type="primary"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => onSpeciesClick?.(feature.properties.speciesId)}
            block
          >
            查看物种详情
          </Button>
        </Space>
      </div>
    );

    layer.bindPopup(popupContent);
  };

  if (!geoJsonData) {
    return null;
  }

  return (
    <GeoJSON
      data={geoJsonData}
      style={getFeatureStyle}
      onEachFeature={onEachFeature}
    />
  );
};

export default DistributionLayer;
