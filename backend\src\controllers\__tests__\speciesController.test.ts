import request from 'supertest';
import { DataSource } from 'typeorm';
import app from '../../app';
import { getTestDataSource, createTestSpecies, createTestUser } from '../../__tests__/utils/database';
import { generateTestToken, expectSuccessResponse, expectErrorResponse, expectPaginatedResponse } from '../../__tests__/utils/helpers';
import { Species } from '../../models/Species';
import { User } from '../../models/User';
import { Role } from '../../models/Role';

describe('SpeciesController', () => {
  let dataSource: DataSource;
  let testSpecies: Species;
  let viewerUser: User;
  let editorUser: User;
  let adminUser: User;

  beforeAll(async () => {
    dataSource = getTestDataSource();
  });

  beforeEach(async () => {
    // 创建测试物种
    testSpecies = await createTestSpecies(dataSource, {
      chineseName: '蓝鲸',
      englishName: 'Blue Whale',
      latinName: 'Balaenoptera musculus',
      conservationStatus: '濒危',
    });

    // 创建不同权限的测试用户
    const roleRepository = dataSource.getRepository(Role);
    const viewerRole = await roleRepository.findOne({ where: { name: 'viewer' } });
    const editorRole = await roleRepository.findOne({ where: { name: 'editor' } });
    const adminRole = await roleRepository.findOne({ where: { name: 'admin' } });

    viewerUser = await createTestUser(dataSource, {
      username: 'viewer',
      roles: viewerRole ? [viewerRole] : [],
    });

    editorUser = await createTestUser(dataSource, {
      username: 'editor',
      roles: editorRole ? [editorRole] : [],
    });

    adminUser = await createTestUser(dataSource, {
      username: 'admin',
      roles: adminRole ? [adminRole] : [],
    });
  });

  describe('GET /api/species/search', () => {
    it('应该返回物种搜索结果', async () => {
      const response = await request(app)
        .get('/api/species/search')
        .expect(200);

      expectPaginatedResponse(response);
      expect(response.body.data.items).toHaveLength(1);
      expect(response.body.data.items[0].chineseName).toBe('蓝鲸');
    });

    it('应该支持关键词搜索', async () => {
      const response = await request(app)
        .get('/api/species/search')
        .query({ keyword: '蓝鲸' })
        .expect(200);

      expectPaginatedResponse(response);
      expect(response.body.data.items).toHaveLength(1);
      expect(response.body.data.items[0].chineseName).toBe('蓝鲸');
    });

    it('应该支持拉丁名搜索', async () => {
      const response = await request(app)
        .get('/api/species/search')
        .query({ keyword: 'Balaenoptera' })
        .expect(200);

      expectPaginatedResponse(response);
      expect(response.body.data.items).toHaveLength(1);
    });

    it('应该支持保护状态筛选', async () => {
      const response = await request(app)
        .get('/api/species/search')
        .query({ conservationStatus: '濒危' })
        .expect(200);

      expectPaginatedResponse(response);
      expect(response.body.data.items).toHaveLength(1);
    });

    it('应该支持分类筛选', async () => {
      const response = await request(app)
        .get('/api/species/search')
        .query({ kingdom: '动物界' })
        .expect(200);

      expectPaginatedResponse(response);
      expect(response.body.data.items).toHaveLength(1);
    });

    it('应该支持分页', async () => {
      // 创建更多测试数据
      for (let i = 1; i <= 25; i++) {
        await createTestSpecies(dataSource, {
          chineseName: `测试物种${i}`,
          latinName: `Test species ${i}`,
        });
      }

      const response = await request(app)
        .get('/api/species/search')
        .query({ page: 2, limit: 10 })
        .expect(200);

      expectPaginatedResponse(response);
      expect(response.body.data.page).toBe(2);
      expect(response.body.data.limit).toBe(10);
      expect(response.body.data.items).toHaveLength(10);
    });

    it('应该支持排序', async () => {
      await createTestSpecies(dataSource, {
        chineseName: '座头鲸',
        latinName: 'Megaptera novaeangliae',
      });

      const response = await request(app)
        .get('/api/species/search')
        .query({ sortBy: 'chineseName', sortOrder: 'ASC' })
        .expect(200);

      expectPaginatedResponse(response);
      expect(response.body.data.items[0].chineseName).toBe('座头鲸');
      expect(response.body.data.items[1].chineseName).toBe('蓝鲸');
    });

    it('应该返回空结果当没有匹配时', async () => {
      const response = await request(app)
        .get('/api/species/search')
        .query({ keyword: '不存在的物种' })
        .expect(200);

      expectPaginatedResponse(response);
      expect(response.body.data.items).toHaveLength(0);
      expect(response.body.data.total).toBe(0);
    });
  });

  describe('GET /api/species/:id/details', () => {
    it('应该返回物种详细信息', async () => {
      const response = await request(app)
        .get(`/api/species/${testSpecies.id}/details`)
        .expect(200);

      expectSuccessResponse(response);
      expect(response.body.data.id).toBe(testSpecies.id);
      expect(response.body.data.chineseName).toBe('蓝鲸');
      expect(response.body.data).toHaveProperty('classification');
      expect(response.body.data).toHaveProperty('audioFiles');
      expect(response.body.data).toHaveProperty('imageFiles');
    });

    it('应该返回404当物种不存在时', async () => {
      const response = await request(app)
        .get('/api/species/nonexistent-id/details')
        .expect(404);

      expectErrorResponse(response, '物种不存在');
    });

    it('应该包含关联的媒体文件', async () => {
      // 这里需要先创建关联的音频和图片文件
      // 然后验证它们是否包含在响应中
      const response = await request(app)
        .get(`/api/species/${testSpecies.id}/details`)
        .expect(200);

      expect(response.body.data).toHaveProperty('audioFiles');
      expect(response.body.data).toHaveProperty('imageFiles');
      expect(Array.isArray(response.body.data.audioFiles)).toBe(true);
      expect(Array.isArray(response.body.data.imageFiles)).toBe(true);
    });
  });

  describe('POST /api/species', () => {
    it('应该允许编辑者创建物种', async () => {
      const token = generateTestToken(editorUser);
      const speciesData = {
        chineseName: '座头鲸',
        englishName: 'Humpback Whale',
        latinName: 'Megaptera novaeangliae',
        conservationStatus: '无危',
        description: '座头鲸是一种大型须鲸',
        classification: {
          kingdom: '动物界',
          phylum: '脊索动物门',
          class: '哺乳纲',
          order: '鲸目',
          family: '须鲸科',
          genus: '座头鲸属',
          species: '座头鲸',
        },
      };

      const response = await request(app)
        .post('/api/species')
        .set('Authorization', `Bearer ${token}`)
        .send(speciesData)
        .expect(201);

      expectSuccessResponse(response);
      expect(response.body.data.chineseName).toBe(speciesData.chineseName);
      expect(response.body.data.latinName).toBe(speciesData.latinName);
    });

    it('应该拒绝查看者创建物种', async () => {
      const token = generateTestToken(viewerUser);
      const speciesData = {
        chineseName: '座头鲸',
        latinName: 'Megaptera novaeangliae',
      };

      const response = await request(app)
        .post('/api/species')
        .set('Authorization', `Bearer ${token}`)
        .send(speciesData)
        .expect(403);

      expectErrorResponse(response, '权限不足');
    });

    it('应该验证必填字段', async () => {
      const token = generateTestToken(editorUser);

      const response = await request(app)
        .post('/api/species')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(400);

      expectErrorResponse(response);
    });

    it('应该验证拉丁名唯一性', async () => {
      const token = generateTestToken(editorUser);
      const speciesData = {
        chineseName: '另一个蓝鲸',
        latinName: testSpecies.latinName, // 重复的拉丁名
      };

      const response = await request(app)
        .post('/api/species')
        .set('Authorization', `Bearer ${token}`)
        .send(speciesData)
        .expect(400);

      expectErrorResponse(response, '拉丁名已存在');
    });

    it('应该要求认证', async () => {
      const speciesData = {
        chineseName: '座头鲸',
        latinName: 'Megaptera novaeangliae',
      };

      const response = await request(app)
        .post('/api/species')
        .send(speciesData)
        .expect(401);

      expectErrorResponse(response);
    });
  });

  describe('PUT /api/species/:id', () => {
    it('应该允许编辑者更新物种', async () => {
      const token = generateTestToken(editorUser);
      const updateData = {
        description: '更新后的描述',
        conservationStatus: '近危',
      };

      const response = await request(app)
        .put(`/api/species/${testSpecies.id}`)
        .set('Authorization', `Bearer ${token}`)
        .send(updateData)
        .expect(200);

      expectSuccessResponse(response);
      expect(response.body.data.description).toBe(updateData.description);
      expect(response.body.data.conservationStatus).toBe(updateData.conservationStatus);
    });

    it('应该拒绝查看者更新物种', async () => {
      const token = generateTestToken(viewerUser);
      const updateData = {
        description: '更新后的描述',
      };

      const response = await request(app)
        .put(`/api/species/${testSpecies.id}`)
        .set('Authorization', `Bearer ${token}`)
        .send(updateData)
        .expect(403);

      expectErrorResponse(response, '权限不足');
    });

    it('应该返回404当物种不存在时', async () => {
      const token = generateTestToken(editorUser);
      const updateData = {
        description: '更新后的描述',
      };

      const response = await request(app)
        .put('/api/species/nonexistent-id')
        .set('Authorization', `Bearer ${token}`)
        .send(updateData)
        .expect(404);

      expectErrorResponse(response, '物种不存在');
    });
  });

  describe('DELETE /api/species/:id', () => {
    it('应该允许管理员删除物种', async () => {
      const token = generateTestToken(adminUser);

      const response = await request(app)
        .delete(`/api/species/${testSpecies.id}`)
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expectSuccessResponse(response);

      // 验证物种已被删除
      const checkResponse = await request(app)
        .get(`/api/species/${testSpecies.id}/details`)
        .expect(404);
    });

    it('应该拒绝编辑者删除物种', async () => {
      const token = generateTestToken(editorUser);

      const response = await request(app)
        .delete(`/api/species/${testSpecies.id}`)
        .set('Authorization', `Bearer ${token}`)
        .expect(403);

      expectErrorResponse(response, '权限不足');
    });

    it('应该返回404当物种不存在时', async () => {
      const token = generateTestToken(adminUser);

      const response = await request(app)
        .delete('/api/species/nonexistent-id')
        .set('Authorization', `Bearer ${token}`)
        .expect(404);

      expectErrorResponse(response, '物种不存在');
    });
  });

  describe('GET /api/species/stats/taxonomy', () => {
    it('应该返回分类统计信息', async () => {
      // 创建不同分类的物种
      await createTestSpecies(dataSource, {
        chineseName: '测试鱼类',
        classification: {
          kingdom: '动物界',
          phylum: '脊索动物门',
          class: '硬骨鱼纲',
          order: '鲈形目',
          family: '测试科',
          genus: '测试属',
          species: '测试种',
        },
      });

      const response = await request(app)
        .get('/api/species/stats/taxonomy')
        .expect(200);

      expectSuccessResponse(response);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0]).toHaveProperty('class');
      expect(response.body.data[0]).toHaveProperty('count');
    });
  });

  describe('GET /api/species/stats/conservation', () => {
    it('应该返回保护状态统计信息', async () => {
      // 创建不同保护状态的物种
      await createTestSpecies(dataSource, {
        chineseName: '无危物种',
        conservationStatus: '无危',
      });

      const response = await request(app)
        .get('/api/species/stats/conservation')
        .expect(200);

      expectSuccessResponse(response);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0]).toHaveProperty('status');
      expect(response.body.data[0]).toHaveProperty('count');
    });
  });
});
