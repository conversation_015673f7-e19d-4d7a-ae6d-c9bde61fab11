describe('物种数据功能', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  describe('物种搜索', () => {
    it('应该显示物种列表页面', () => {
      // 导航到物种数据库
      cy.get('[data-testid="species-nav-link"]').click();
      
      // 验证页面加载
      cy.url().should('include', '/species');
      cy.get('[data-testid="species-list-page"]').should('be.visible');
      cy.get('[data-testid="search-box"]').should('be.visible');
      cy.get('[data-testid="species-grid"]').should('be.visible');
      
      // 验证物种卡片显示
      cy.waitForApi('speciesSearch');
      cy.get('[data-testid="species-card"]').should('have.length.at.least', 1);
      cy.get('[data-testid="species-card"]').first().should('contain', '蓝鲸');
    });

    it('应该支持关键词搜索', () => {
      cy.visit('/species');
      
      // 输入搜索关键词
      cy.get('[data-testid="search-input"]').type('蓝鲸');
      cy.get('[data-testid="search-button"]').click();
      
      // 验证搜索结果
      cy.waitForApi('speciesSearch');
      cy.get('[data-testid="species-card"]').should('contain', '蓝鲸');
      cy.get('[data-testid="search-results-count"]').should('contain', '找到 2 个结果');
    });

    it('应该支持高级搜索', () => {
      cy.visit('/species');
      
      // 打开高级搜索
      cy.get('[data-testid="advanced-search-toggle"]').click();
      cy.get('[data-testid="advanced-search-panel"]').should('be.visible');
      
      // 设置筛选条件
      cy.get('[data-testid="conservation-status-select"]').click();
      cy.get('[data-testid="conservation-option-endangered"]').click();
      
      cy.get('[data-testid="kingdom-select"]').click();
      cy.get('[data-testid="kingdom-option-animalia"]').click();
      
      // 执行搜索
      cy.get('[data-testid="advanced-search-button"]').click();
      
      // 验证筛选标签
      cy.get('[data-testid="filter-tag"]').should('contain', '保护状态: 濒危');
      cy.get('[data-testid="filter-tag"]').should('contain', '界: 动物界');
      
      // 验证搜索结果
      cy.waitForApi('speciesSearch');
      cy.get('[data-testid="species-card"]').should('have.length.at.least', 1);
    });

    it('应该支持分页导航', () => {
      // 模拟多页数据
      cy.intercept('GET', '/api/species/search*', (req) => {
        const page = req.url.searchParams.get('page') || '1';
        req.reply({
          statusCode: 200,
          body: {
            success: true,
            data: {
              items: [
                {
                  id: `page-${page}-1`,
                  chineseName: `第${page}页物种1`,
                  latinName: `Page ${page} Species 1`,
                  conservationStatus: '无危'
                }
              ],
              total: 50,
              page: parseInt(page),
              limit: 20,
              totalPages: 3
            }
          }
        });
      }).as('paginatedSearch');

      cy.visit('/species');
      
      // 等待第一页加载
      cy.wait('@paginatedSearch');
      cy.get('[data-testid="species-card"]').should('contain', '第1页物种1');
      
      // 点击下一页
      cy.get('[data-testid="pagination-next"]').click();
      
      // 验证第二页
      cy.wait('@paginatedSearch');
      cy.get('[data-testid="species-card"]').should('contain', '第2页物种1');
      cy.get('[data-testid="pagination-current"]').should('contain', '2');
    });

    it('应该支持排序功能', () => {
      cy.visit('/species');
      
      // 更改排序方式
      cy.get('[data-testid="sort-select"]').click();
      cy.get('[data-testid="sort-option-latin-name"]').click();
      
      // 验证排序参数
      cy.waitForApi('speciesSearch');
      cy.url().should('include', 'sortBy=latinName');
    });

    it('应该处理搜索无结果', () => {
      // 模拟无结果
      cy.intercept('GET', '/api/species/search*', {
        statusCode: 200,
        body: {
          success: true,
          data: {
            items: [],
            total: 0,
            page: 1,
            limit: 20,
            totalPages: 0
          }
        }
      }).as('emptySearch');

      cy.visit('/species');
      cy.get('[data-testid="search-input"]').type('不存在的物种');
      cy.get('[data-testid="search-button"]').click();
      
      cy.wait('@emptySearch');
      cy.get('[data-testid="empty-state"]').should('be.visible');
      cy.get('[data-testid="empty-state"]').should('contain', '暂无数据');
    });
  });

  describe('物种详情', () => {
    beforeEach(() => {
      // 模拟物种详情数据
      cy.fixture('species/species-details.json').then((details) => {
        cy.intercept('GET', '/api/species/*/details', details).as('speciesDetails');
      });
    });

    it('应该显示物种详细信息', () => {
      cy.visit('/species');
      
      // 点击物种卡片
      cy.get('[data-testid="species-card"]').first().click();
      
      // 验证详情页面
      cy.url().should('include', '/species/1');
      cy.waitForApi('speciesDetails');
      
      cy.get('[data-testid="species-detail-page"]').should('be.visible');
      cy.get('[data-testid="species-name"]').should('contain', '蓝鲸');
      cy.get('[data-testid="species-latin-name"]').should('contain', 'Balaenoptera musculus');
      cy.get('[data-testid="conservation-status"]').should('contain', '濒危');
    });

    it('应该显示分类信息', () => {
      cy.visit('/species/1');
      cy.waitForApi('speciesDetails');
      
      // 验证分类信息卡片
      cy.get('[data-testid="classification-card"]').should('be.visible');
      cy.get('[data-testid="classification-kingdom"]').should('contain', '动物界');
      cy.get('[data-testid="classification-phylum"]').should('contain', '脊索动物门');
      cy.get('[data-testid="classification-class"]').should('contain', '哺乳纲');
    });

    it('应该显示音频文件列表', () => {
      cy.visit('/species/1');
      cy.waitForApi('speciesDetails');
      
      // 验证音频播放器
      cy.get('[data-testid="audio-player"]').should('be.visible');
      cy.get('[data-testid="audio-playlist"]').should('be.visible');
      cy.get('[data-testid="audio-file-item"]').should('have.length.at.least', 1);
    });

    it('应该支持音频播放', () => {
      cy.visit('/species/1');
      cy.waitForApi('speciesDetails');
      
      // 点击播放按钮
      cy.get('[data-testid="play-button"]').first().click();
      
      // 验证播放状态
      cy.get('[data-testid="audio-player"]').should('have.class', 'playing');
      cy.get('[data-testid="play-button"]').should('contain', '暂停');
    });

    it('应该显示图片画廊', () => {
      cy.visit('/species/1');
      cy.waitForApi('speciesDetails');
      
      // 验证图片画廊
      cy.get('[data-testid="image-gallery"]').should('be.visible');
      cy.get('[data-testid="gallery-image"]').should('have.length.at.least', 1);
      
      // 点击图片预览
      cy.get('[data-testid="gallery-image"]').first().click();
      cy.get('[data-testid="image-modal"]').should('be.visible');
      
      // 关闭预览
      cy.get('[data-testid="modal-close"]').click();
      cy.get('[data-testid="image-modal"]').should('not.exist');
    });

    it('应该处理物种不存在', () => {
      // 模拟404响应
      cy.intercept('GET', '/api/species/*/details', {
        statusCode: 404,
        body: {
          success: false,
          error: { message: '物种不存在' }
        }
      }).as('speciesNotFound');

      cy.visit('/species/nonexistent');
      
      cy.wait('@speciesNotFound');
      cy.get('[data-testid="not-found-page"]').should('be.visible');
      cy.get('[data-testid="error-message"]').should('contain', '物种不存在');
    });
  });

  describe('搜索历史', () => {
    it('应该保存搜索历史', () => {
      cy.visit('/species');
      
      // 执行搜索
      cy.get('[data-testid="search-input"]').type('蓝鲸');
      cy.get('[data-testid="search-button"]').click();
      cy.waitForApi('speciesSearch');
      
      // 清空搜索框并重新聚焦
      cy.get('[data-testid="search-input"]').clear().click();
      
      // 验证搜索历史显示
      cy.get('[data-testid="search-history"]').should('be.visible');
      cy.get('[data-testid="history-item"]').should('contain', '蓝鲸');
    });

    it('应该支持选择历史项', () => {
      cy.visit('/species');
      
      // 先执行一次搜索
      cy.get('[data-testid="search-input"]').type('座头鲸');
      cy.get('[data-testid="search-button"]').click();
      cy.waitForApi('speciesSearch');
      
      // 清空并显示历史
      cy.get('[data-testid="search-input"]').clear().click();
      
      // 点击历史项
      cy.get('[data-testid="history-item"]').contains('座头鲸').click();
      
      // 验证搜索执行
      cy.get('[data-testid="search-input"]').should('have.value', '座头鲸');
      cy.waitForApi('speciesSearch');
    });

    it('应该支持清除搜索历史', () => {
      cy.visit('/species');
      
      // 执行搜索创建历史
      cy.get('[data-testid="search-input"]').type('虎鲸');
      cy.get('[data-testid="search-button"]').click();
      cy.waitForApi('speciesSearch');
      
      // 显示历史并清除
      cy.get('[data-testid="search-input"]').clear().click();
      cy.get('[data-testid="clear-history-button"]').click();
      
      // 验证历史已清除
      cy.get('[data-testid="search-input"]').click();
      cy.get('[data-testid="search-history"]').should('not.exist');
    });
  });

  describe('响应式设计', () => {
    it('应该在移动设备上正常显示', () => {
      cy.viewport('iphone-6');
      cy.visit('/species');
      
      // 验证移动端布局
      cy.get('[data-testid="mobile-search"]').should('be.visible');
      cy.get('[data-testid="species-grid"]').should('be.visible');
      
      // 验证物种卡片在移动端的显示
      cy.waitForApi('speciesSearch');
      cy.get('[data-testid="species-card"]').should('be.visible');
      
      // 测试搜索功能
      cy.get('[data-testid="search-input"]').type('蓝鲸');
      cy.get('[data-testid="search-button"]').click();
      cy.waitForApi('speciesSearch');
    });

    it('应该在平板设备上正常显示', () => {
      cy.viewport('ipad-2');
      cy.visit('/species');
      
      // 验证平板端布局
      cy.get('[data-testid="species-grid"]').should('be.visible');
      cy.waitForApi('speciesSearch');
      
      // 验证网格布局
      cy.get('[data-testid="species-card"]').should('have.length.at.least', 2);
    });
  });
});
