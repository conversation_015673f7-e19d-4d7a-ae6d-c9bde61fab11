# 部署指南

本文档详细说明了海洋生物声音平台的部署流程和配置要求。

## 🏗️ 部署架构

### 生产环境架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Web Server    │    │    Database     │
│    (Nginx)      │───▶│   (Node.js)     │───▶│  (PostgreSQL)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └─────────────▶│  Static Files   │              │
                        │    (React)      │              │
                        └─────────────────┘              │
                                 │                       │
                        ┌─────────────────┐              │
                        │  File Storage   │              │
                        │   (Uploads)     │◀─────────────┘
                        └─────────────────┘
```

## 🔧 环境要求

### 系统要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Docker
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 50GB，推荐 100GB+
- **网络**: 稳定的互联网连接

### 软件依赖
- **Node.js**: 18.x LTS
- **PostgreSQL**: 14.x + PostGIS 3.x
- **Nginx**: 1.18+
- **PM2**: 5.x (进程管理)
- **Redis**: 6.x (可选，用于缓存)

## 📦 Docker部署 (推荐)

### 1. 准备Docker环境
```bash
# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 创建Docker配置
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  # 数据库服务
  postgres:
    image: postgis/postgis:14-3.2
    environment:
      POSTGRES_DB: marine_bio_platform
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis缓存服务
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      NODE_ENV: production
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_DATABASE: marine_bio_platform
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
    volumes:
      - ./uploads:/app/uploads
    ports:
      - "3001:3001"
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
```

### 3. 创建生产环境配置
```bash
# 创建环境变量文件
cat > .env.prod << EOF
DB_USERNAME=marine_user
DB_PASSWORD=your_secure_password
JWT_SECRET=your_jwt_secret_key
DOMAIN=your-domain.com
EOF
```

### 4. 部署应用
```bash
# 构建并启动服务
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

## 🖥️ 传统部署

### 1. 系统准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装pnpm
npm install -g pnpm

# 安装PostgreSQL和PostGIS
sudo apt install postgresql postgresql-contrib postgis postgresql-14-postgis-3

# 安装Nginx
sudo apt install nginx

# 安装PM2
npm install -g pm2
```

### 2. 数据库配置
```bash
# 切换到postgres用户
sudo -u postgres psql

# 创建数据库和用户
CREATE DATABASE marine_bio_platform;
CREATE USER marine_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE marine_bio_platform TO marine_user;

# 启用PostGIS扩展
\c marine_bio_platform
CREATE EXTENSION postgis;
CREATE EXTENSION postgis_topology;

\q
```

### 3. 应用部署
```bash
# 克隆代码
git clone https://github.com/your-org/marine-bio-platform.git
cd marine-bio-platform

# 安装依赖
cd frontend && pnpm install
cd ../backend && pnpm install

# 构建前端
cd ../frontend
pnpm build

# 配置环境变量
cd ../backend
cp .env.example .env
# 编辑 .env 文件

# 运行数据库迁移
pnpm run db:migration:run

# 构建后端
pnpm build

# 使用PM2启动后端服务
pm2 start ecosystem.config.js --env production
```

### 4. Nginx配置
```nginx
# /etc/nginx/sites-available/marine-bio-platform
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;

    # 前端静态文件
    location / {
        root /path/to/marine-bio-platform/frontend/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理
    location /api {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 文件服务
    location /files {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 5. 启用Nginx配置
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/marine-bio-platform /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## 🔒 SSL证书配置

### 使用Let's Encrypt
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和日志

### PM2监控
```bash
# 查看进程状态
pm2 status

# 查看日志
pm2 logs

# 监控面板
pm2 monit

# 重启应用
pm2 restart all
```

### 系统监控
```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 查看系统资源
htop

# 查看磁盘使用
df -h

# 查看数据库连接
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

## 🔄 更新部署

### Docker更新
```bash
# 拉取最新代码
git pull origin main

# 重新构建并部署
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d
```

### 传统部署更新
```bash
# 拉取最新代码
git pull origin main

# 更新前端
cd frontend
pnpm install
pnpm build

# 更新后端
cd ../backend
pnpm install
pnpm build

# 运行数据库迁移
pnpm run db:migration:run

# 重启服务
pm2 restart all
```

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务状态
   - 验证连接配置
   - 检查防火墙设置

2. **文件上传失败**
   - 检查uploads目录权限
   - 验证磁盘空间
   - 检查Nginx文件大小限制

3. **前端页面无法访问**
   - 检查Nginx配置
   - 验证静态文件路径
   - 检查SSL证书

### 日志位置
- **应用日志**: `~/.pm2/logs/`
- **Nginx日志**: `/var/log/nginx/`
- **PostgreSQL日志**: `/var/log/postgresql/`

## 📞 技术支持

如果在部署过程中遇到问题，请：
1. 查看相关日志文件
2. 检查系统资源使用情况
3. 参考故障排除部分
4. 提交Issue到GitHub仓库
