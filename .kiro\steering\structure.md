# Project Structure & Organization

## Root Directory Layout
```
mbdp/
├── frontend/          # React frontend application
├── backend/           # Node.js backend service
├── .kiro/            # Project specifications and steering
├── docs/             # Project documentation
├── scripts/          # Build and utility scripts
└── README.md         # Main project documentation
```

## Frontend Structure (`frontend/`)
```
frontend/
├── src/
│   ├── components/    # Reusable UI components
│   ├── pages/        # Page-level components
│   ├── store/        # Redux Toolkit state management
│   ├── router/       # React Router configuration
│   ├── types/        # TypeScript type definitions
│   ├── utils/        # Utility functions and helpers
│   └── assets/       # Static assets (images, styles)
├── public/           # Public static files
├── cypress/          # E2E test files
├── package.json      # Dependencies and scripts
├── vite.config.ts    # Vite build configuration
├── tsconfig.json     # TypeScript configuration
└── cypress.config.ts # Cypress test configuration
```

## Backend Structure (`backend/`)
```
backend/
├── src/
│   ├── controllers/   # Request handlers and API endpoints
│   ├── services/     # Business logic layer
│   ├── models/       # TypeORM entity definitions
│   ├── middleware/   # Express middleware functions
│   ├── routes/       # API route definitions
│   ├── migrations/   # Database migration files
│   ├── utils/        # Utility functions and helpers
│   ├── types/        # TypeScript type definitions
│   ├── config/       # Configuration files
│   └── scripts/      # Database and utility scripts
├── uploads/          # File upload storage directory
├── package.json      # Dependencies and scripts
├── tsconfig.json     # TypeScript configuration
└── nodemon.json      # Development server configuration
```

## Configuration Files

### TypeScript Configuration
- **Backend**: Path aliases configured (`@/*` maps to `src/*`)
- **Frontend**: Project references setup with separate app/node configs
- **Decorators**: Enabled for TypeORM entities

### Code Quality
- **ESLint**: Separate configs for frontend/backend with TypeScript rules
- **Prettier**: Consistent formatting across both applications
- **Jest**: Unit testing configuration for both frontend/backend

### Environment Files
- **Frontend**: `.env` for build-time variables
- **Backend**: `.env` for runtime configuration (database, JWT, uploads)
- **Examples**: `.env.example` files provided for setup guidance

## Key Architectural Patterns

### Backend Architecture
- **MVC Pattern**: Controllers handle requests, Services contain business logic
- **Repository Pattern**: TypeORM entities with decorators
- **Middleware Chain**: Authentication, validation, error handling
- **Path Aliases**: Clean imports using `@/` prefix

### Frontend Architecture
- **Component-Based**: Reusable components in `/components`
- **Page-Based Routing**: Route components in `/pages`
- **Centralized State**: Redux Toolkit for global state management
- **Type Safety**: Comprehensive TypeScript coverage

### File Naming Conventions
- **Components**: PascalCase (e.g., `SpeciesCard.tsx`)
- **Pages**: PascalCase (e.g., `SpeciesDetail.tsx`)
- **Utilities**: camelCase (e.g., `formatDate.ts`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS.ts`)

### Import Organization
- **External libraries** first
- **Internal modules** by proximity (utils, types, components)
- **Relative imports** last
- **Path aliases** preferred over relative paths in backend