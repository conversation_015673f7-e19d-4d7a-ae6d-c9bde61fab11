import React, { useState } from 'react';
import { Card, Form, Input, Select, Space, Button, message, Typography, Image, Row, Col } from 'antd';
import { PictureOutlined, SaveOutlined, EyeOutlined } from '@ant-design/icons';
import BaseUpload from './BaseUpload';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

interface ImageUploadProps {
  speciesId?: string;
  onUploadComplete?: (files: any[]) => void;
}

interface ImageMetadata {
  description: string;
  imageType: string;
  photographyLocation?: string;
  photographyTime?: string;
  photographer?: string;
  equipment?: string;
  notes?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  speciesId,
  onUploadComplete,
}) => {
  const [form] = Form.useForm();
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [saving, setSaving] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  // 图片上传成功
  const handleUploadSuccess = (response: any) => {
    if (response.success && response.data) {
      setUploadedFiles(prev => [...prev, response.data]);
    }
  };

  // 上传错误处理
  const handleUploadError = (error: string) => {
    message.error(error);
  };

  // 预览图片
  const handlePreview = (file: any) => {
    setPreviewImage(file.url);
    setPreviewVisible(true);
  };

  // 保存图片信息
  const handleSave = async (values: ImageMetadata) => {
    if (uploadedFiles.length === 0) {
      message.warning('请先上传图片文件');
      return;
    }

    setSaving(true);
    try {
      const promises = uploadedFiles.map(file => 
        fetch(`${import.meta.env.VITE_API_BASE_URL}/image-files`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({
            speciesId,
            fileName: file.originalName,
            filePath: file.path,
            fileSize: file.size,
            mimeType: file.mimeType,
            ...values,
          }),
        })
      );

      const responses = await Promise.all(promises);
      const results = await Promise.all(responses.map(r => r.json()));
      
      const successCount = results.filter(r => r.success).length;
      
      if (successCount === uploadedFiles.length) {
        message.success(`成功保存 ${successCount} 个图片文件`);
        onUploadComplete?.(results.map(r => r.data));
        
        // 重置表单
        form.resetFields();
        setUploadedFiles([]);
      } else {
        message.warning(`保存了 ${successCount}/${uploadedFiles.length} 个文件`);
      }
    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div>
      <Card
        title={
          <Space>
            <PictureOutlined />
            图片文件上传
          </Space>
        }
        style={{ marginBottom: '24px' }}
      >
        <BaseUpload
          accept=".jpg,.jpeg,.png,.gif,.webp,image/*"
          maxSize={10}
          maxCount={10}
          onUploadSuccess={handleUploadSuccess}
          onUploadError={handleUploadError}
          uploadUrl={`${import.meta.env.VITE_API_BASE_URL}/files/upload/image`}
        />

        {/* 图片预览网格 */}
        {uploadedFiles.length > 0 && (
          <div style={{ marginTop: '24px' }}>
            <Title level={5}>图片预览</Title>
            <Row gutter={[16, 16]}>
              {uploadedFiles.map((file, index) => (
                <Col key={index} xs={12} sm={8} md={6} lg={4}>
                  <div style={{ 
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    overflow: 'hidden',
                    position: 'relative'
                  }}>
                    <Image
                      src={file.url}
                      alt={file.originalName}
                      style={{ width: '100%', height: '120px', objectFit: 'cover' }}
                      preview={{
                        mask: (
                          <div style={{ color: 'white' }}>
                            <EyeOutlined /> 预览
                          </div>
                        ),
                      }}
                    />
                    <div style={{ 
                      padding: '8px',
                      background: '#fafafa',
                      fontSize: '12px',
                      textAlign: 'center'
                    }}>
                      <Text ellipsis title={file.originalName}>
                        {file.originalName}
                      </Text>
                    </div>
                  </div>
                </Col>
              ))}
            </Row>
          </div>
        )}
      </Card>

      {/* 图片信息表单 */}
      {uploadedFiles.length > 0 && (
        <Card
          title="图片信息"
          extra={
            <Text type="secondary">
              已上传 {uploadedFiles.length} 个文件
            </Text>
          }
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSave}
          >
            <Form.Item
              name="description"
              label="图片描述"
              rules={[{ required: true, message: '请输入图片描述' }]}
            >
              <TextArea 
                rows={3}
                placeholder="描述图片内容，如物种特征、行为状态等"
              />
            </Form.Item>

            <Form.Item
              name="imageType"
              label="图片类型"
              rules={[{ required: true, message: '请选择图片类型' }]}
            >
              <Select placeholder="选择图片类型">
                <Option value="representative">代表性图片</Option>
                <Option value="behavior">行为图片</Option>
                <Option value="habitat">栖息地图片</Option>
                <Option value="anatomy">解剖结构</Option>
                <Option value="feeding">觅食行为</Option>
                <Option value="reproduction">繁殖行为</Option>
                <Option value="other">其他</Option>
              </Select>
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="photographyLocation"
                  label="拍摄地点"
                >
                  <Input placeholder="拍摄地点" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="photographyTime"
                  label="拍摄时间"
                >
                  <Input placeholder="拍摄时间" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="photographer"
                  label="摄影师"
                >
                  <Input placeholder="摄影师姓名" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="equipment"
                  label="拍摄设备"
                >
                  <Input placeholder="相机型号和参数" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="notes"
              label="备注"
            >
              <TextArea 
                rows={2}
                placeholder="其他需要说明的信息"
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button 
                  type="primary" 
                  htmlType="submit"
                  icon={<SaveOutlined />}
                  loading={saving}
                >
                  保存图片信息
                </Button>
                <Button onClick={() => form.resetFields()}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>
      )}
    </div>
  );
};

export default ImageUpload;
