import React from 'react';
import { Breadcrumb as AntBreadcrumb } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import { HomeOutlined, SearchOutlined, GlobalOutlined, DashboardOutlined, UserOutlined } from '@ant-design/icons';

interface BreadcrumbItem {
  path: string;
  title: string;
  icon?: React.ReactNode;
}

const Breadcrumb: React.FC = () => {
  const location = useLocation();
  const pathSnippets = location.pathname.split('/').filter(i => i);

  // 路由配置映射
  const routeMap: Record<string, BreadcrumbItem> = {
    '': { path: '/', title: '首页', icon: <HomeOutlined /> },
    'species': { path: '/species', title: '物种数据库', icon: <SearchOutlined /> },
    'map': { path: '/map', title: '交互式地图', icon: <GlobalOutlined /> },
    'dashboard': { path: '/dashboard', title: '数据仪表盘', icon: <DashboardOutlined /> },
    'login': { path: '/login', title: '登录', icon: <UserOutlined /> },
    'register': { path: '/register', title: '注册', icon: <UserOutlined /> },
    'profile': { path: '/profile', title: '个人资料', icon: <UserOutlined /> },
  };

  // 构建面包屑项目
  const breadcrumbItems = [
    {
      title: (
        <Link to="/">
          <HomeOutlined />
          <span style={{ marginLeft: '4px' }}>首页</span>
        </Link>
      ),
    },
  ];

  let currentPath = '';
  pathSnippets.forEach((snippet, index) => {
    currentPath += `/${snippet}`;
    const routeConfig = routeMap[snippet];
    
    if (routeConfig) {
      const isLast = index === pathSnippets.length - 1;
      
      breadcrumbItems.push({
        title: isLast ? (
          <span>
            {routeConfig.icon && <span style={{ marginRight: '4px' }}>{routeConfig.icon}</span>}
            {routeConfig.title}
          </span>
        ) : (
          <Link to={currentPath}>
            {routeConfig.icon && <span style={{ marginRight: '4px' }}>{routeConfig.icon}</span>}
            {routeConfig.title}
          </Link>
        ),
      });
    } else {
      // 处理动态路由，如物种详情页
      if (pathSnippets[index - 1] === 'species' && snippet.length > 10) {
        breadcrumbItems.push({
          title: <span>物种详情</span>,
        });
      } else {
        breadcrumbItems.push({
          title: <span>{snippet}</span>,
        });
      }
    }
  });

  // 如果只有首页，不显示面包屑
  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <div style={{ 
      padding: '16px 24px', 
      background: '#fff', 
      borderBottom: '1px solid #f0f0f0' 
    }}>
      <AntBreadcrumb items={breadcrumbItems} />
    </div>
  );
};

export default Breadcrumb;
