#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 生成测试报告
 */
class TestReportGenerator {
  constructor() {
    this.reportData = {
      timestamp: new Date().toISOString(),
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        coverage: {
          frontend: null,
          backend: null,
          overall: null
        }
      },
      frontend: {
        unit: null,
        integration: null,
        e2e: null
      },
      backend: {
        unit: null,
        integration: null
      },
      coverage: {
        frontend: null,
        backend: null
      }
    };
  }

  /**
   * 解析Jest测试结果
   */
  parseJestResults(resultsPath) {
    try {
      if (!fs.existsSync(resultsPath)) {
        console.warn(`Jest results file not found: ${resultsPath}`);
        return null;
      }

      const resultsContent = fs.readFileSync(resultsPath, 'utf8');
      const results = JSON.parse(resultsContent);

      return {
        total: results.numTotalTests,
        passed: results.numPassedTests,
        failed: results.numFailedTests,
        skipped: results.numPendingTests,
        duration: results.testResults.reduce((sum, test) => sum + test.perfStats.runtime, 0),
        testSuites: results.testResults.map(suite => ({
          name: suite.name,
          status: suite.status,
          duration: suite.perfStats.runtime,
          tests: suite.assertionResults.map(test => ({
            name: test.title,
            status: test.status,
            duration: test.duration || 0
          }))
        }))
      };
    } catch (error) {
      console.error(`Error parsing Jest results: ${error.message}`);
      return null;
    }
  }

  /**
   * 解析Cypress测试结果
   */
  parseCypressResults(resultsPath) {
    try {
      if (!fs.existsSync(resultsPath)) {
        console.warn(`Cypress results file not found: ${resultsPath}`);
        return null;
      }

      const resultsContent = fs.readFileSync(resultsPath, 'utf8');
      const results = JSON.parse(resultsContent);

      const totalTests = results.runs.reduce((sum, run) => sum + run.stats.tests, 0);
      const passedTests = results.runs.reduce((sum, run) => sum + run.stats.passes, 0);
      const failedTests = results.runs.reduce((sum, run) => sum + run.stats.failures, 0);
      const skippedTests = results.runs.reduce((sum, run) => sum + run.stats.pending, 0);

      return {
        total: totalTests,
        passed: passedTests,
        failed: failedTests,
        skipped: skippedTests,
        duration: results.totalDuration,
        specs: results.runs.map(run => ({
          name: run.spec.name,
          status: run.stats.failures > 0 ? 'failed' : 'passed',
          duration: run.stats.duration,
          tests: run.tests.map(test => ({
            name: test.title.join(' '),
            status: test.state,
            duration: test.duration || 0
          }))
        }))
      };
    } catch (error) {
      console.error(`Error parsing Cypress results: ${error.message}`);
      return null;
    }
  }

  /**
   * 解析覆盖率报告
   */
  parseCoverageReport(coveragePath) {
    try {
      if (!fs.existsSync(coveragePath)) {
        console.warn(`Coverage report not found: ${coveragePath}`);
        return null;
      }

      const coverageContent = fs.readFileSync(coveragePath, 'utf8');
      const coverage = JSON.parse(coverageContent);

      // 计算总体覆盖率
      let totalStatements = 0;
      let coveredStatements = 0;
      let totalBranches = 0;
      let coveredBranches = 0;
      let totalFunctions = 0;
      let coveredFunctions = 0;
      let totalLines = 0;
      let coveredLines = 0;

      Object.values(coverage).forEach(file => {
        if (file.s) {
          totalStatements += Object.keys(file.s).length;
          coveredStatements += Object.values(file.s).filter(count => count > 0).length;
        }
        if (file.b) {
          Object.values(file.b).forEach(branch => {
            totalBranches += branch.length;
            coveredBranches += branch.filter(count => count > 0).length;
          });
        }
        if (file.f) {
          totalFunctions += Object.keys(file.f).length;
          coveredFunctions += Object.values(file.f).filter(count => count > 0).length;
        }
        if (file.l) {
          totalLines += Object.keys(file.l).length;
          coveredLines += Object.values(file.l).filter(count => count > 0).length;
        }
      });

      return {
        statements: {
          total: totalStatements,
          covered: coveredStatements,
          percentage: totalStatements > 0 ? (coveredStatements / totalStatements * 100).toFixed(2) : 0
        },
        branches: {
          total: totalBranches,
          covered: coveredBranches,
          percentage: totalBranches > 0 ? (coveredBranches / totalBranches * 100).toFixed(2) : 0
        },
        functions: {
          total: totalFunctions,
          covered: coveredFunctions,
          percentage: totalFunctions > 0 ? (coveredFunctions / totalFunctions * 100).toFixed(2) : 0
        },
        lines: {
          total: totalLines,
          covered: coveredLines,
          percentage: totalLines > 0 ? (coveredLines / totalLines * 100).toFixed(2) : 0
        }
      };
    } catch (error) {
      console.error(`Error parsing coverage report: ${error.message}`);
      return null;
    }
  }

  /**
   * 生成报告
   */
  async generateReport() {
    console.log('Generating test report...');

    // 解析前端测试结果
    this.reportData.frontend.unit = this.parseJestResults('frontend/coverage/test-results.json');
    this.reportData.frontend.e2e = this.parseCypressResults('frontend/cypress/results/results.json');

    // 解析后端测试结果
    this.reportData.backend.unit = this.parseJestResults('backend/coverage/test-results.json');

    // 解析覆盖率报告
    this.reportData.coverage.frontend = this.parseCoverageReport('frontend/coverage/coverage-final.json');
    this.reportData.coverage.backend = this.parseCoverageReport('backend/coverage/coverage-final.json');

    // 计算总体统计
    this.calculateSummary();

    // 生成HTML报告
    this.generateHTMLReport();

    // 生成JSON报告
    this.generateJSONReport();

    console.log('Test report generated successfully!');
  }

  /**
   * 计算总体统计
   */
  calculateSummary() {
    const tests = [
      this.reportData.frontend.unit,
      this.reportData.frontend.e2e,
      this.reportData.backend.unit
    ].filter(Boolean);

    this.reportData.summary.total = tests.reduce((sum, test) => sum + test.total, 0);
    this.reportData.summary.passed = tests.reduce((sum, test) => sum + test.passed, 0);
    this.reportData.summary.failed = tests.reduce((sum, test) => sum + test.failed, 0);
    this.reportData.summary.skipped = tests.reduce((sum, test) => sum + test.skipped, 0);

    // 计算覆盖率
    if (this.reportData.coverage.frontend) {
      this.reportData.summary.coverage.frontend = this.reportData.coverage.frontend.lines.percentage;
    }
    if (this.reportData.coverage.backend) {
      this.reportData.summary.coverage.backend = this.reportData.coverage.backend.lines.percentage;
    }

    // 计算总体覆盖率
    const frontendCoverage = parseFloat(this.reportData.summary.coverage.frontend || 0);
    const backendCoverage = parseFloat(this.reportData.summary.coverage.backend || 0);
    if (frontendCoverage > 0 && backendCoverage > 0) {
      this.reportData.summary.coverage.overall = ((frontendCoverage + backendCoverage) / 2).toFixed(2);
    }
  }

  /**
   * 生成HTML报告
   */
  generateHTMLReport() {
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海洋生物声音平台 - 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .summary { display: flex; gap: 20px; margin-bottom: 20px; }
        .metric { background: white; border: 1px solid #ddd; padding: 15px; border-radius: 5px; flex: 1; }
        .metric h3 { margin: 0 0 10px 0; color: #333; }
        .metric .value { font-size: 24px; font-weight: bold; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        .section { margin-bottom: 30px; }
        .section h2 { border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .status-passed { color: #28a745; }
        .status-failed { color: #dc3545; }
        .status-skipped { color: #ffc107; }
    </style>
</head>
<body>
    <div class="header">
        <h1>海洋生物声音平台 - 测试报告</h1>
        <p>生成时间: ${this.reportData.timestamp}</p>
    </div>

    <div class="summary">
        <div class="metric">
            <h3>总测试数</h3>
            <div class="value">${this.reportData.summary.total}</div>
        </div>
        <div class="metric">
            <h3>通过</h3>
            <div class="value passed">${this.reportData.summary.passed}</div>
        </div>
        <div class="metric">
            <h3>失败</h3>
            <div class="value failed">${this.reportData.summary.failed}</div>
        </div>
        <div class="metric">
            <h3>跳过</h3>
            <div class="value skipped">${this.reportData.summary.skipped}</div>
        </div>
        <div class="metric">
            <h3>总体覆盖率</h3>
            <div class="value">${this.reportData.summary.coverage.overall || 'N/A'}%</div>
        </div>
    </div>

    ${this.generateTestSectionHTML('前端单元测试', this.reportData.frontend.unit)}
    ${this.generateTestSectionHTML('后端单元测试', this.reportData.backend.unit)}
    ${this.generateTestSectionHTML('E2E测试', this.reportData.frontend.e2e)}
    ${this.generateCoverageSectionHTML()}

</body>
</html>`;

    fs.writeFileSync('test-report.html', html);
  }

  /**
   * 生成测试部分HTML
   */
  generateTestSectionHTML(title, testData) {
    if (!testData) return '';

    return `
    <div class="section">
        <h2>${title}</h2>
        <p>总计: ${testData.total}, 通过: ${testData.passed}, 失败: ${testData.failed}, 跳过: ${testData.skipped}</p>
        <p>耗时: ${testData.duration}ms</p>
    </div>`;
  }

  /**
   * 生成覆盖率部分HTML
   */
  generateCoverageSectionHTML() {
    return `
    <div class="section">
        <h2>代码覆盖率</h2>
        <table>
            <tr>
                <th>模块</th>
                <th>语句</th>
                <th>分支</th>
                <th>函数</th>
                <th>行</th>
            </tr>
            ${this.reportData.coverage.frontend ? `
            <tr>
                <td>前端</td>
                <td>${this.reportData.coverage.frontend.statements.percentage}%</td>
                <td>${this.reportData.coverage.frontend.branches.percentage}%</td>
                <td>${this.reportData.coverage.frontend.functions.percentage}%</td>
                <td>${this.reportData.coverage.frontend.lines.percentage}%</td>
            </tr>` : ''}
            ${this.reportData.coverage.backend ? `
            <tr>
                <td>后端</td>
                <td>${this.reportData.coverage.backend.statements.percentage}%</td>
                <td>${this.reportData.coverage.backend.branches.percentage}%</td>
                <td>${this.reportData.coverage.backend.functions.percentage}%</td>
                <td>${this.reportData.coverage.backend.lines.percentage}%</td>
            </tr>` : ''}
        </table>
    </div>`;
  }

  /**
   * 生成JSON报告
   */
  generateJSONReport() {
    fs.writeFileSync('test-report.json', JSON.stringify(this.reportData, null, 2));
  }
}

// 运行报告生成器
if (require.main === module) {
  const generator = new TestReportGenerator();
  generator.generateReport().catch(console.error);
}

module.exports = TestReportGenerator;
