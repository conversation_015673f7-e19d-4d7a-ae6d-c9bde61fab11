// 内存监控相关的类型定义

export interface MemoryInfo {
  used: number;
  total: number;
  percentage: number;
  trend: 'increasing' | 'decreasing' | 'stable';
  timestamp: number;
}

export interface MemoryConfig {
  warningThreshold: number; // 警告阈值
  criticalThreshold: number; // 紧急阈值
  checkInterval: number; // 检查间隔（毫秒）
  historySize: number; // 历史记录大小
  trendSensitivity: number; // 趋势敏感度
}

export interface MemoryStats {
  average: number;
  max: number;
  min: number;
  trend: 'increasing' | 'decreasing' | 'stable';
}

export interface UseMemoryMonitorOptions {
  enableWarnings?: boolean;
  enableCriticalAlerts?: boolean;
  onWarning?: (info: MemoryInfo) => void;
  onCritical?: (info: MemoryInfo) => void;
}

export interface MemoryMonitorState {
  currentMemory: MemoryInfo | null;
  history: MemoryInfo[];
  stats: MemoryStats;
  isMonitoring: boolean;
}

export interface MemoryStatus {
  isHealthy: boolean;
  level: 'low' | 'medium' | 'high' | 'critical';
  percentage: number;
  trend: 'increasing' | 'decreasing' | 'stable';
}
