# Requirements Document

## Introduction

构建一个专业、易用且可扩展的海洋生物发声数据平台。该平台将系统地管理和存储珍贵的海洋生物声音数据，通过交互式地图和多媒体页面向科研人员、教育工作者及公众直观地展示这些信息，促进学术交流与科学普及。平台包含前台展示系统、后台管理系统和音频识别功能。

## Requirements

### Requirement 1: 交互式全球数据地图

**User Story:** 作为一个用户，我希望通过交互式世界地图探索海洋生物的分布和数据采集点，并能直接搜索感兴趣的物种。

#### Acceptance Criteria

1. WHEN 用户访问首页 THEN 系统 SHALL 显示一个带有卫星图底图的世界地图
2. WHEN 用户在搜索框输入关键词 THEN 系统 SHALL 实时显示匹配的物种中文名下拉列表
3. WHEN 用户点击搜索列表中的物种 THEN 系统 SHALL 直接跳转到该物种的详情页
4. WHEN 用户点击模式切换按钮 THEN 系统 SHALL 在物种分布范围图和声音数据采集点图之间切换
5. WHEN 地图处于物种分布范围模式 THEN 系统 SHALL 渲染所有物种的分布范围（基于KML文件）
6. WHEN 用户点击单一物种分布区域 THEN 系统 SHALL 直接跳转到该物种详情页
7. WHEN 用户点击重叠物种分布区域 THEN 系统 SHALL 展开侧边栏显示物种列表
8. WHEN 侧边栏显示物种列表 THEN 系统 SHALL 提供搜索框和分页功能
9. WHEN 地图处于采集点模式且缩放级别较高 THEN 系统 SHALL 将临近采集点聚合显示
10. WHEN 用户点击聚合点 THEN 系统 SHALL 自动放大并聚焦到该区域
11. WHEN 地图缩放到较低级别 THEN 系统 SHALL 显示独立的采集点标记
12. WHEN 用户点击独立采集点 THEN 系统 SHALL 弹出物种卡片

### Requirement 2: 物种详情页展示

**User Story:** 作为一个用户，我希望看到类似维基百科风格的物种详情页，全面了解该物种的信息。

#### Acceptance Criteria

1. WHEN 用户访问物种详情页 THEN 系统 SHALL 显示左右分栏布局
2. WHEN 显示右侧信息框 THEN 系统 SHALL 包含物种代表性照片、保护状况、科学分类、名称和地理分布图
3. WHEN 显示左侧主内容区 THEN 系统 SHALL 包含详细介绍、媒体图集、视频资料和音频播放分析
4. WHEN 用户查看音频列表 THEN 系统 SHALL 为每个音频文件提供独立播放器
5. WHEN 用户播放音频 THEN 系统 SHALL 在时域图和时频图上同步显示进度条
6. WHEN 系统预生成图片功能启用 THEN 系统 SHALL 展示频谱图和全息谱
7. WHEN 用户点击图集中的图片 THEN 系统 SHALL 支持点击放大浏览
8. WHEN 用户查看视频资料 THEN 系统 SHALL 提供内嵌播放器

### Requirement 3: 后台用户权限管理

**User Story:** 作为管理员，我希望能够管理系统用户账户、角色权限，并追踪系统操作日志。

#### Acceptance Criteria

1. WHEN 管理员访问用户管理页面 THEN 系统 SHALL 显示用户列表包含用户名、角色、状态等信息
2. WHEN 管理员创建或编辑用户 THEN 系统 SHALL 支持分配角色、重置密码等操作
3. WHEN 管理员需要控制用户权限 THEN 系统 SHALL 支持用户的禁用/启用功能
4. WHEN 管理员搜索用户 THEN 系统 SHALL 提供用户信息的搜索和筛选功能
5. WHEN 管理员管理角色 THEN 系统 SHALL 支持角色的创建、编辑和删除
6. WHEN 管理员配置权限 THEN 系统 SHALL 提供可视化权限树进行权限勾选
7. WHEN 用户登录后 THEN 系统 SHALL 根据用户权限动态展示操作界面
8. WHEN 系统进行权限验证 THEN 系统 SHALL 对API请求进行自动权限校验
9. WHEN 用户执行重要操作 THEN 系统 SHALL 自动记录操作日志
10. WHEN 用户登录系统 THEN 系统 SHALL 记录登录时间、IP和登录状态
11. WHEN 管理员查询日志 THEN 系统 SHALL 支持按时间、操作人等条件筛选

### Requirement 4: 后台物种数据管理

**User Story:** 作为管理员，我希望能够全面管理物种的基础信息、详细介绍、媒体资源和地理分布数据。

#### Acceptance Criteria

1. WHEN 管理员管理物种基础信息 THEN 系统 SHALL 提供中文名、英文名、拉丁学名的输入字段
2. WHEN 管理员设置科学分类 THEN 系统 SHALL 提供界、门、纲、目、科、属、种的输入字段
3. WHEN 管理员设置保护状况 THEN 系统 SHALL 提供IUCN红色名录等级的下拉选择
4. WHEN 管理员设置代表性图片 THEN 系统 SHALL 允许从已上传图集中指定一张作为代表照片
5. WHEN 管理员编辑物种介绍 THEN 系统 SHALL 提供富文本编辑器支持格式化编辑
6. WHEN 管理员管理图集 THEN 系统 SHALL 支持批量上传、删除和替换图片操作
7. WHEN 管理员管理视频 THEN 系统 SHALL 支持上传视频文件并添加标题描述
8. WHEN 管理员管理音频 THEN 系统 SHALL 支持上传音频文件并添加元数据
9. WHEN 管理员设置音频元数据 THEN 系统 SHALL 包含录音地点、时间、行为描述、采样位数等字段
10. WHEN 管理员管理地理分布 THEN 系统 SHALL 支持上传KML文件并解析坐标
11. WHEN 管理员管理采集点 THEN 系统 SHALL 支持为音频文件附加地理坐标
12. WHEN 管理员输入坐标 THEN 系统 SHALL 支持手动输入经度纬度

### Requirement 5: 音频识别功能

**User Story:** 作为管理员，我希望能够上传音频文件进行物种识别，并管理识别结果。

#### Acceptance Criteria

1. WHEN 管理员上传音频文件 THEN 系统 SHALL 支持WAV和MP3格式
2. WHEN 系统接收音频文件 THEN 系统 SHALL 调用识别模型接口进行物种识别
3. WHEN 识别完成 THEN 系统 SHALL 展示对应的物种识别结果
4. WHEN 管理员查看识别记录 THEN 系统 SHALL 以列表形式展示上传的音频文件和识别结果
5. WHEN 管理员播放识别音频 THEN 系统 SHALL 提供音频播放功能
6. WHEN 系统超出试用期 THEN 系统 SHALL 限制继续使用识别功能
7. WHEN 系统处理上传数据 THEN 系统 SHALL 对数据进行加密处理

### Requirement 6: 系统安全与性能

**User Story:** 作为系统用户，我希望系统具有良好的安全性和性能表现。

#### Acceptance Criteria

1. WHEN 系统处理用户数据 THEN 系统 SHALL 实施数据加密保护
2. WHEN 系统显示大量数据列表 THEN 系统 SHALL 使用分页或虚拟滚动提升性能
3. WHEN 用户访问受限功能 THEN 系统 SHALL 进行权限验证
4. WHEN 系统记录操作日志 THEN 系统 SHALL 确保所有关键操作可追溯
5. WHEN 用户上传大文件 THEN 系统 SHALL 提供上传进度显示
6. WHEN 系统显示地图数据 THEN 系统 SHALL 根据缩放级别优化数据加载