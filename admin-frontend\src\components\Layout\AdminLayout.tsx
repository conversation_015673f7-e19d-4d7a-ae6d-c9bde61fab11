import React, { useEffect, useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout,
  Menu,
  Button,
  Space,
  Dropdown,
  Avatar,
  Typography,
  Breadcrumb,
  theme
} from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  TeamOutlined,
  FileOutlined,
  SettingOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  HomeOutlined
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../../hooks';
import { logout, fetchUserProfile } from '../../store/slices/authSlice';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const AdminLayout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, token } = useAppSelector(state => state.auth);
  const [collapsed, setCollapsed] = useState(false);
  
  const {
    token: { colorBgContainer },
  } = theme.useToken();

  // 如果有token但没有用户信息，尝试获取用户信息
  useEffect(() => {
    if (token && !user && !isAuthenticated) {
      dispatch(fetchUserProfile());
    }
  }, [token, user, isAuthenticated, dispatch]);

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '数据仪表盘',
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: '用户管理',
      children: [
        {
          key: '/users',
          label: '用户列表',
        },
        {
          key: '/users/roles',
          label: '角色管理',
        },
      ],
    },
    {
      key: '/species',
      icon: <TeamOutlined />,
      label: '物种管理',
      children: [
        {
          key: '/species',
          label: '物种列表',
        },
        {
          key: '/species/create',
          label: '新增物种',
        },
      ],
    },
    {
      key: '/media',
      icon: <FileOutlined />,
      label: '媒体管理',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  // 生成面包屑
  const generateBreadcrumb = () => {
    const pathSnippets = location.pathname.split('/').filter(i => i);
    const breadcrumbItems = [
      {
        title: <HomeOutlined />,
        href: '/dashboard',
      },
    ];

    pathSnippets.forEach((snippet, index) => {
      const url = `/${pathSnippets.slice(0, index + 1).join('/')}`;
      const isLast = index === pathSnippets.length - 1;
      
      let title = snippet;
      switch (snippet) {
        case 'dashboard':
          title = '数据仪表盘';
          break;
        case 'users':
          title = '用户管理';
          break;
        case 'species':
          title = '物种管理';
          break;
        case 'media':
          title = '媒体管理';
          break;
        case 'settings':
          title = '系统设置';
          break;
        case 'create':
          title = '新增';
          break;
        case 'edit':
          title = '编辑';
          break;
        case 'roles':
          title = '角色管理';
          break;
      }

      breadcrumbItems.push({
        title,
        href: isLast ? undefined : url,
      });
    });

    return breadcrumbItems;
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: colorBgContainer,
          borderRight: '1px solid #f0f0f0',
        }}
      >
        <div style={{ 
          height: '64px', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0',
          marginBottom: '8px'
        }}>
          {!collapsed ? (
            <Text strong style={{ color: '#1890ff' }}>
              管理系统
            </Text>
          ) : (
            <SettingOutlined style={{ color: '#1890ff', fontSize: '20px' }} />
          )}
        </div>
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ border: 'none' }}
        />
      </Sider>
      
      <Layout>
        <Header 
          style={{ 
            padding: '0 24px', 
            background: colorBgContainer,
            borderBottom: '1px solid #f0f0f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: '16px',
                width: 64,
                height: 64,
              }}
            />
            <Text strong style={{ fontSize: '18px' }}>
              海洋生物声音平台 - 管理端
            </Text>
          </Space>

          <div>
            {isAuthenticated && user ? (
              <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
                <Button type="text" style={{ height: 'auto', padding: '4px 8px' }}>
                  <Space>
                    <Avatar size="small" icon={<UserOutlined />} />
                    <span>{user.username}</span>
                  </Space>
                </Button>
              </Dropdown>
            ) : (
              <Button type="primary" onClick={() => navigate('/login')}>
                登录
              </Button>
            )}
          </div>
        </Header>
        
        <Content style={{ margin: '16px' }}>
          <Breadcrumb 
            items={generateBreadcrumb()}
            style={{ marginBottom: '16px' }}
          />
          <div style={{ 
            background: colorBgContainer, 
            minHeight: 'calc(100vh - 112px)',
            borderRadius: '8px'
          }}>
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default AdminLayout;
