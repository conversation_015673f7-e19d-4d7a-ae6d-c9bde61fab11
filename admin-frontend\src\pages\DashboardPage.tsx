import React, { useState, useEffect } from 'react';
import { 
  Typography, 
  Row, 
  Col, 
  Card, 
  Button, 
  Space, 
  DatePicker, 
  Select,
  message,
  Statistic
} from 'antd';
import { 
  DashboardOutlined, 
  DownloadOutlined, 
  ReloadOutlined,
  FilterOutlined,
  UserOutlined,
  TeamOutlined,
  FileOutlined,
  DatabaseOutlined
} from '@ant-design/icons';
import { SpeciesStatsChart } from '../components/Charts';
import { useAppDispatch, useAppSelector } from '../hooks';
import { fetchSystemStats } from '../store/slices/systemSlice';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const DashboardPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { systemStats, loading } = useAppSelector(state => state.system);
  
  const [dateRange, setDateRange] = useState<any>(null);
  const [filterType, setFilterType] = useState<string>('all');

  useEffect(() => {
    dispatch(fetchSystemStats());
  }, [dispatch]);

  // 刷新数据
  const handleRefresh = async () => {
    try {
      await dispatch(fetchSystemStats()).unwrap();
      message.success('数据已刷新');
    } catch (error) {
      message.error('数据刷新失败');
    }
  };

  // 导出数据
  const handleExport = () => {
    // TODO: 实现数据导出功能
    message.info('数据导出功能开发中...');
  };

  // 应用筛选
  const handleFilter = () => {
    // TODO: 实现筛选逻辑
    message.info('筛选功能开发中...');
  };

  // 格式化存储大小
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <div>
          <Title level={2} style={{ margin: 0, marginBottom: '8px' }}>
            <DashboardOutlined /> 管理仪表盘
          </Title>
          <Text type="secondary">
            海洋生物声音平台管理端数据统计与分析
          </Text>
        </div>

        <Space>
          {/* 筛选控件 */}
          <RangePicker
            placeholder={['开始日期', '结束日期']}
            onChange={setDateRange}
            style={{ width: '240px' }}
          />
          <Select
            value={filterType}
            onChange={setFilterType}
            style={{ width: '120px' }}
            placeholder="数据类型"
          >
            <Option value="all">全部数据</Option>
            <Option value="species">物种数据</Option>
            <Option value="audio">音频数据</Option>
            <Option value="image">图片数据</Option>
          </Select>
          <Button 
            icon={<FilterOutlined />}
            onClick={handleFilter}
          >
            应用筛选
          </Button>
          <Button 
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading.stats}
          >
            刷新
          </Button>
          <Button 
            type="primary"
            icon={<DownloadOutlined />}
            onClick={handleExport}
          >
            导出数据
          </Button>
        </Space>
      </div>

      {/* 系统概览统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="用户总数"
              value={systemStats?.totalUsers || 0}
              prefix={<UserOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="物种总数"
              value={systemStats?.totalSpecies || 0}
              prefix={<TeamOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="媒体文件"
              value={systemStats?.totalMediaFiles || 0}
              prefix={<FileOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="存储使用"
              value={systemStats ? `${((systemStats.storageUsed / systemStats.storageLimit) * 100).toFixed(1)}%` : '0%'}
              prefix={<DatabaseOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
              suffix={
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {systemStats ? `${formatBytes(systemStats.storageUsed)} / ${formatBytes(systemStats.storageLimit)}` : ''}
                </Text>
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 统计图表区域 */}
      <div style={{ marginBottom: '24px' }}>
        <SpeciesStatsChart loading={loading.stats} />
      </div>

      {/* 其他管理信息 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} xl={16}>
          <Card title="系统状态监控">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a', marginBottom: '8px' }}>
                    正常
                  </div>
                  <div style={{ color: '#666' }}>数据库连接</div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a', marginBottom: '8px' }}>
                    正常
                  </div>
                  <div style={{ color: '#666' }}>API服务</div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
        
        <Col xs={24} xl={8}>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            {/* 最近活动 */}
            <Card title="最近活动" size="small">
              <div style={{ fontSize: '12px' }}>
                <div style={{ marginBottom: '8px', paddingBottom: '8px', borderBottom: '1px solid #f0f0f0' }}>
                  <Text strong>新增用户:</Text> admin_user_001
                  <br />
                  <Text type="secondary">2小时前</Text>
                </div>
                <div style={{ marginBottom: '8px', paddingBottom: '8px', borderBottom: '1px solid #f0f0f0' }}>
                  <Text strong>新增物种:</Text> 蓝鲸 (Balaenoptera musculus)
                  <br />
                  <Text type="secondary">4小时前</Text>
                </div>
                <div style={{ marginBottom: '8px', paddingBottom: '8px', borderBottom: '1px solid #f0f0f0' }}>
                  <Text strong>上传音频:</Text> 座头鲸歌声录音
                  <br />
                  <Text type="secondary">1天前</Text>
                </div>
                <div>
                  <Text strong>系统维护:</Text> 清理临时文件
                  <br />
                  <Text type="secondary">2天前</Text>
                </div>
              </div>
            </Card>

            {/* 快速操作 */}
            <Card title="快速操作" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button type="primary" block>
                  新增物种
                </Button>
                <Button block>
                  用户管理
                </Button>
                <Button block>
                  系统设置
                </Button>
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardPage;
