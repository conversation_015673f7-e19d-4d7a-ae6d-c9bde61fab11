# Dependencies
node_modules/
.pnpm-store/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
.next/
out/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Vite cache
.vite

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Upload directories (keep structure but ignore content)
backend/uploads/*
!backend/uploads/.gitkeep
!backend/uploads/species/
!backend/uploads/kml/
!backend/uploads/temp/

# Database
*.sqlite
*.db

# Test files and reports
test-report.html
test-report.json
cypress/videos/
cypress/screenshots/
cypress/downloads/
.nyc_output/
junit.xml

# Docker
.dockerignore
docker-compose.override.yml

# IDE specific files
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
*.code-workspace

# TypeScript
*.tsbuildinfo
.tscache/

# ESLint
.eslintcache

# Prettier
.prettierignore

# Husky
.husky/_/

# Local development
.env.development
.env.staging
.env.production

# Backup files
*.backup
*.bak
*.orig

# Archive files
*.zip
*.tar.gz
*.rar

# System files
*.log
*.pid
*.seed
*.pid.lock

# Frontend specific
frontend/node_modules/
frontend/dist/
frontend/.vite/
frontend/storybook-static/

# Backend specific
backend/node_modules/
backend/dist/
backend/uploads/audio/*
backend/uploads/images/*
backend/uploads/videos/*
backend/uploads/kml/*
!backend/uploads/audio/.gitkeep
!backend/uploads/images/.gitkeep
!backend/uploads/videos/.gitkeep
!backend/uploads/kml/.gitkeep

# Keep upload directory structure
!backend/uploads/
!backend/uploads/audio/
!backend/uploads/images/
!backend/uploads/videos/
!backend/uploads/kml/
!backend/uploads/temp/