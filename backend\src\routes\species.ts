import { Router } from 'express';
import { SpeciesController } from '../controllers/SpeciesController';
import { authenticateToken, requirePermission, optionalAuth } from '../middleware/auth';

const router = Router();
const speciesController = new SpeciesController();

// 公开路由（不需要认证）
router.get('/search', optionalAuth, speciesController.searchSpecies);
router.get('/recent', optionalAuth, speciesController.getRecentSpecies);
router.get('/random', optionalAuth, speciesController.getRandomSpecies);
router.get('/stats/taxonomy', optionalAuth, speciesController.getTaxonomyStats);
router.get('/stats/conservation', optionalAuth, speciesController.getConservationStats);
router.get('/:id', optionalAuth, speciesController.getSpecies);
router.get('/:id/details', optionalAuth, speciesController.getSpeciesDetails);

// 需要认证的路由
router.use(authenticateToken);

// 物种管理（需要物种写权限）
router.post('/', requirePermission('species', 'write'), speciesController.createSpecies);
router.put('/:id', requirePermission('species', 'write'), speciesController.updateSpecies);
router.delete('/:id', requirePermission('species', 'write'), speciesController.deleteSpecies);

export default router;
