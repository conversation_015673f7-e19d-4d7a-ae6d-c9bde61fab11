describe('文件上传功能', () => {
  beforeEach(() => {
    // 设置认证状态
    cy.setAuthState(true);
    cy.visit('/');
  });

  describe('音频文件上传', () => {
    it('应该成功上传音频文件', () => {
      // 导航到物种详情页面
      cy.visit('/species/1');
      cy.waitForApi('speciesDetails');
      
      // 切换到文件上传标签
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="audio-upload-tab"]').click();
      
      // 验证上传界面
      cy.get('[data-testid="audio-upload-area"]').should('be.visible');
      cy.get('[data-testid="upload-dropzone"]').should('contain', '点击或拖拽文件到此区域上传');
      
      // 上传音频文件
      cy.fixture('test-audio.wav', 'base64').then(fileContent => {
        cy.get('[data-testid="file-input"]').selectFile({
          contents: Cypress.Buffer.from(fileContent, 'base64'),
          fileName: 'test-audio.wav',
          mimeType: 'audio/wav'
        });
      });
      
      // 验证文件已添加到列表
      cy.get('[data-testid="file-list-item"]').should('contain', 'test-audio.wav');
      cy.get('[data-testid="upload-progress"]').should('be.visible');
      
      // 等待上传完成
      cy.waitForApi('fileUpload');
      cy.get('[data-testid="upload-success"]').should('be.visible');
      
      // 填写音频信息
      cy.get('[data-testid="audio-info-form"]').should('be.visible');
      cy.get('[data-testid="recording-location-input"]').type('太平洋');
      cy.get('[data-testid="recording-time-picker"]').click();
      cy.get('[data-testid="date-today"]').click();
      cy.get('[data-testid="behavior-select"]').click();
      cy.get('[data-testid="behavior-option-feeding"]').click();
      
      // 保存音频信息
      cy.get('[data-testid="save-audio-button"]').click();
      
      // 验证保存成功
      cy.get('[data-testid="success-message"]').should('contain', '音频文件保存成功');
    });

    it('应该验证音频文件类型', () => {
      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="audio-upload-tab"]').click();
      
      // 尝试上传非音频文件
      cy.fixture('test-image.jpg', 'base64').then(fileContent => {
        cy.get('[data-testid="file-input"]').selectFile({
          contents: Cypress.Buffer.from(fileContent, 'base64'),
          fileName: 'test-image.jpg',
          mimeType: 'image/jpeg'
        });
      });
      
      // 验证错误提示
      cy.get('[data-testid="file-type-error"]').should('contain', '不支持的文件类型');
      cy.get('[data-testid="file-type-error"]').should('contain', '仅支持音频文件');
    });

    it('应该验证文件大小限制', () => {
      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="audio-upload-tab"]').click();
      
      // 创建超大文件
      const largeFileContent = 'x'.repeat(60 * 1024 * 1024); // 60MB
      cy.get('[data-testid="file-input"]').selectFile({
        contents: largeFileContent,
        fileName: 'large-audio.wav',
        mimeType: 'audio/wav'
      });
      
      // 验证大小限制错误
      cy.get('[data-testid="file-size-error"]').should('contain', '文件大小不能超过');
    });

    it('应该支持拖拽上传', () => {
      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="audio-upload-tab"]').click();
      
      // 模拟拖拽事件
      cy.fixture('test-audio.wav', 'base64').then(fileContent => {
        const file = new File([Cypress.Buffer.from(fileContent, 'base64')], 'test-audio.wav', {
          type: 'audio/wav'
        });
        
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        
        cy.get('[data-testid="upload-dropzone"]')
          .trigger('dragenter', { dataTransfer })
          .trigger('dragover', { dataTransfer })
          .trigger('drop', { dataTransfer });
      });
      
      // 验证文件已添加
      cy.get('[data-testid="file-list-item"]').should('contain', 'test-audio.wav');
    });

    it('应该显示上传进度', () => {
      // 模拟慢速上传
      cy.intercept('POST', '/api/files/upload/audio', (req) => {
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({
              statusCode: 200,
              body: {
                success: true,
                data: {
                  originalName: 'test-audio.wav',
                  path: 'uploads/audio/test-audio.wav',
                  url: 'http://localhost:3001/files/uploads/audio/test-audio.wav'
                }
              }
            });
          }, 2000);
        });
      }).as('slowUpload');
      
      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="audio-upload-tab"]').click();
      
      // 上传文件
      cy.fixture('test-audio.wav', 'base64').then(fileContent => {
        cy.get('[data-testid="file-input"]').selectFile({
          contents: Cypress.Buffer.from(fileContent, 'base64'),
          fileName: 'test-audio.wav',
          mimeType: 'audio/wav'
        });
      });
      
      // 验证进度显示
      cy.get('[data-testid="upload-progress"]').should('be.visible');
      cy.get('[data-testid="progress-bar"]').should('be.visible');
      cy.get('[data-testid="upload-status"]').should('contain', '正在上传');
      
      // 等待上传完成
      cy.wait('@slowUpload');
      cy.get('[data-testid="upload-success"]').should('be.visible');
    });
  });

  describe('图片文件上传', () => {
    it('应该成功上传图片文件', () => {
      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="image-upload-tab"]').click();
      
      // 上传图片文件
      cy.fixture('test-image.jpg', 'base64').then(fileContent => {
        cy.get('[data-testid="file-input"]').selectFile({
          contents: Cypress.Buffer.from(fileContent, 'base64'),
          fileName: 'test-image.jpg',
          mimeType: 'image/jpeg'
        });
      });
      
      // 验证图片预览
      cy.waitForApi('fileUpload');
      cy.get('[data-testid="image-preview"]').should('be.visible');
      cy.get('[data-testid="preview-image"]').should('have.attr', 'src');
      
      // 填写图片信息
      cy.get('[data-testid="image-description-input"]').type('蓝鲸游泳照片');
      cy.get('[data-testid="image-type-select"]').click();
      cy.get('[data-testid="image-type-representative"]').click();
      cy.get('[data-testid="photographer-input"]').type('测试摄影师');
      
      // 保存图片信息
      cy.get('[data-testid="save-image-button"]').click();
      
      // 验证保存成功
      cy.get('[data-testid="success-message"]').should('contain', '图片文件保存成功');
    });

    it('应该支持多图片上传', () => {
      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="image-upload-tab"]').click();
      
      // 上传多个图片
      cy.fixture('test-image-1.jpg', 'base64').then(fileContent1 => {
        cy.fixture('test-image-2.jpg', 'base64').then(fileContent2 => {
          cy.get('[data-testid="file-input"]').selectFile([
            {
              contents: Cypress.Buffer.from(fileContent1, 'base64'),
              fileName: 'test-image-1.jpg',
              mimeType: 'image/jpeg'
            },
            {
              contents: Cypress.Buffer.from(fileContent2, 'base64'),
              fileName: 'test-image-2.jpg',
              mimeType: 'image/jpeg'
            }
          ]);
        });
      });
      
      // 验证多个预览
      cy.waitForApi('fileUpload');
      cy.get('[data-testid="preview-image"]').should('have.length', 2);
      cy.get('[data-testid="file-count"]').should('contain', '已上传 2 个文件');
    });
  });

  describe('文件管理', () => {
    beforeEach(() => {
      // 模拟已有文件数据
      cy.intercept('GET', '/api/files*', { fixture: 'files/file-list.json' }).as('fileList');
    });

    it('应该显示文件列表', () => {
      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="file-manager-tab"]').click();
      
      // 验证文件管理界面
      cy.waitForApi('fileList');
      cy.get('[data-testid="file-table"]').should('be.visible');
      cy.get('[data-testid="file-row"]').should('have.length.at.least', 1);
      
      // 验证文件信息显示
      cy.get('[data-testid="file-name"]').first().should('contain', 'test-audio.wav');
      cy.get('[data-testid="file-type"]').first().should('contain', '音频');
      cy.get('[data-testid="file-size"]').first().should('contain', 'MB');
    });

    it('应该支持文件搜索', () => {
      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="file-manager-tab"]').click();
      
      // 搜索文件
      cy.get('[data-testid="file-search-input"]').type('audio');
      cy.get('[data-testid="search-button"]').click();
      
      // 验证搜索结果
      cy.waitForApi('fileList');
      cy.get('[data-testid="file-row"]').each($row => {
        cy.wrap($row).should('contain', 'audio');
      });
    });

    it('应该支持文件类型筛选', () => {
      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="file-manager-tab"]').click();
      
      // 筛选音频文件
      cy.get('[data-testid="file-type-filter"]').click();
      cy.get('[data-testid="filter-audio"]').click();
      
      // 验证筛选结果
      cy.waitForApi('fileList');
      cy.get('[data-testid="file-type"]').each($type => {
        cy.wrap($type).should('contain', '音频');
      });
    });

    it('应该支持文件删除', () => {
      cy.intercept('DELETE', '/api/files/*', {
        statusCode: 200,
        body: { success: true, data: {} }
      }).as('deleteFile');

      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="file-manager-tab"]').click();
      cy.waitForApi('fileList');
      
      // 点击删除按钮
      cy.get('[data-testid="delete-file-button"]').first().click();
      
      // 确认删除
      cy.get('[data-testid="confirm-delete-modal"]').should('be.visible');
      cy.get('[data-testid="confirm-delete-button"]').click();
      
      // 验证删除成功
      cy.wait('@deleteFile');
      cy.get('[data-testid="success-message"]').should('contain', '文件删除成功');
    });

    it('应该支持批量删除', () => {
      cy.intercept('POST', '/api/files/batch-delete', {
        statusCode: 200,
        body: { success: true, data: {} }
      }).as('batchDelete');

      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="file-manager-tab"]').click();
      cy.waitForApi('fileList');
      
      // 选择多个文件
      cy.get('[data-testid="file-checkbox"]').first().check();
      cy.get('[data-testid="file-checkbox"]').eq(1).check();
      
      // 批量删除
      cy.get('[data-testid="batch-delete-button"]').click();
      cy.get('[data-testid="confirm-batch-delete-button"]').click();
      
      // 验证批量删除成功
      cy.wait('@batchDelete');
      cy.get('[data-testid="success-message"]').should('contain', '成功删除 2 个文件');
    });

    it('应该支持文件下载', () => {
      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="file-manager-tab"]').click();
      cy.waitForApi('fileList');
      
      // 点击下载按钮
      cy.get('[data-testid="download-file-button"]').first().click();
      
      // 验证下载链接
      cy.get('[data-testid="download-link"]').should('have.attr', 'href');
    });
  });

  describe('错误处理', () => {
    it('应该处理上传失败', () => {
      // 模拟上传失败
      cy.intercept('POST', '/api/files/upload/audio', {
        statusCode: 500,
        body: {
          success: false,
          error: { message: '服务器错误' }
        }
      }).as('uploadError');

      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="audio-upload-tab"]').click();
      
      // 尝试上传文件
      cy.fixture('test-audio.wav', 'base64').then(fileContent => {
        cy.get('[data-testid="file-input"]').selectFile({
          contents: Cypress.Buffer.from(fileContent, 'base64'),
          fileName: 'test-audio.wav',
          mimeType: 'audio/wav'
        });
      });
      
      // 验证错误提示
      cy.wait('@uploadError');
      cy.get('[data-testid="upload-error"]').should('contain', '服务器错误');
      cy.get('[data-testid="retry-upload-button"]').should('be.visible');
    });

    it('应该处理网络错误', () => {
      // 模拟网络错误
      cy.intercept('POST', '/api/files/upload/audio', { forceNetworkError: true }).as('networkError');

      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="audio-upload-tab"]').click();
      
      // 尝试上传文件
      cy.fixture('test-audio.wav', 'base64').then(fileContent => {
        cy.get('[data-testid="file-input"]').selectFile({
          contents: Cypress.Buffer.from(fileContent, 'base64'),
          fileName: 'test-audio.wav',
          mimeType: 'audio/wav'
        });
      });
      
      // 验证网络错误提示
      cy.wait('@networkError');
      cy.get('[data-testid="network-error"]').should('contain', '网络错误');
    });
  });

  describe('权限控制', () => {
    it('应该限制查看者上传文件', () => {
      // 设置查看者权限
      cy.window().then((win) => {
        const user = {
          id: '1',
          username: 'viewer',
          roles: [{ name: 'viewer', permissions: { files: { create: false } } }]
        };
        win.localStorage.setItem('user', JSON.stringify(user));
      });

      cy.visit('/species/1');
      cy.get('[data-testid="upload-tab"]').click();
      
      // 验证上传功能被禁用
      cy.get('[data-testid="upload-disabled-message"]').should('contain', '您没有上传文件的权限');
      cy.get('[data-testid="file-input"]').should('be.disabled');
    });
  });
});
