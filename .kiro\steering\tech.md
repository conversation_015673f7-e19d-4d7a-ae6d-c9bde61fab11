# Technology Stack & Build System

## Frontend Stack
- **Framework**: React 19 + TypeScript
- **Build Tool**: Vite
- **State Management**: Redux Toolkit
- **UI Library**: Ant Design + Ant Design Charts
- **Routing**: React Router v7
- **Maps**: Leaflet + React-Leaflet
- **Audio**: WaveSurfer.js
- **Charts**: ECharts + ECharts-for-React
- **HTTP Client**: Axios

## Backend Stack
- **Runtime**: Node.js 18+
- **Framework**: Express 4 + TypeScript
- **Database**: PostgreSQL 13+ with PostGIS extension
- **ORM**: TypeORM with decorators
- **Authentication**: JWT + RBAC
- **File Upload**: Multer
- **Security**: Helmet, CORS, bcryptjs

## Package Management
- **Tool**: pnpm 8+
- **Workspaces**: Separate frontend/backend packages
- **Lock Files**: pnpm-lock.yaml for reproducible builds

## Common Commands

### Development
```bash
# Frontend development server (port 5173)
cd frontend && pnpm run dev

# Backend development server (port 3001)
cd backend && pnpm run dev

# Install dependencies
cd frontend && pnpm install
cd backend && pnpm install
```

### Building & Production
```bash
# Frontend build
cd frontend && pnpm run build

# Backend build
cd backend && pnpm run build

# Start production backend
cd backend && pnpm start
```

### Code Quality
```bash
# Frontend linting
cd frontend && pnpm run lint

# Backend linting & formatting
cd backend && pnpm run lint
cd backend && pnpm run format

# Type checking
cd frontend && pnpm run type-check
cd backend && pnpm run type-check
```

### Testing
```bash
# Frontend tests
cd frontend && pnpm test
cd frontend && pnpm test:coverage
cd frontend && pnpm test:e2e

# Backend tests
cd backend && pnpm test
cd backend && pnpm test:coverage
cd backend && pnpm test:integration
```

### Database Management
```bash
# Initialize database with migrations and seed data
cd backend && pnpm run db:init

# Run migrations
cd backend && pnpm run db:migration:run

# Revert migrations
cd backend && pnpm run db:migration:revert

# Generate new migration
cd backend && pnpm run db:migration:generate
```

## Development Tools
- **Code Style**: ESLint + Prettier (consistent across frontend/backend)
- **Testing**: Jest (unit), Cypress (E2E), Supertest (API)
- **CI/CD**: GitHub Actions with Codecov integration
- **Database**: TypeORM CLI for migrations and schema management