import React from 'react';
import { Card, Typography, Space } from 'antd';
import ReactECharts from 'echarts-for-react';
import type { EChartsOption } from 'echarts';

const { Title, Text } = Typography;

interface BaseChartProps {
  title?: string;
  subtitle?: string;
  option: EChartsOption;
  height?: number;
  loading?: boolean;
  style?: React.CSSProperties;
  onChartReady?: (chart: any) => void;
  onEvents?: Record<string, (params: any) => void>;
}

const BaseChart: React.FC<BaseChartProps> = ({
  title,
  subtitle,
  option,
  height = 400,
  loading = false,
  style,
  onChartReady,
  onEvents,
}) => {
  return (
    <Card
      title={
        title && (
          <Space direction="vertical" size={0}>
            <Title level={4} style={{ margin: 0 }}>
              {title}
            </Title>
            {subtitle && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {subtitle}
              </Text>
            )}
          </Space>
        )
      }
      loading={loading}
      style={style}
    >
      <ReactECharts
        option={option}
        style={{ height: `${height}px`, width: '100%' }}
        onChartReady={onChartReady}
        onEvents={onEvents}
        opts={{ renderer: 'canvas' }}
      />
    </Card>
  );
};

export default BaseChart;
