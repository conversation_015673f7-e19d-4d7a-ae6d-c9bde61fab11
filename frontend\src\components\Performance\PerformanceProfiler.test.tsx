import React, { useState } from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import PerformanceProfiler from './PerformanceProfiler';

// 测试组件，用于触发重新渲染
const TestComponent: React.FC = () => {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <p data-testid="count">Count: {count}</p>
      <button 
        data-testid="increment" 
        onClick={() => setCount(c => c + 1)}
      >
        Increment
      </button>
    </div>
  );
};

describe('PerformanceProfiler', () => {
  // 模拟console方法
  const originalConsoleLog = console.log;
  const originalConsoleWarn = console.warn;
  
  beforeEach(() => {
    console.log = jest.fn();
    console.warn = jest.fn();
  });
  
  afterEach(() => {
    console.log = originalConsoleLog;
    console.warn = originalConsoleWarn;
  });

  test('should not cause infinite re-renders', async () => {
    const onRender = jest.fn();
    
    render(
      <PerformanceProfiler id="test-profiler" enabled={true} onRender={onRender}>
        <TestComponent />
      </PerformanceProfiler>
    );

    // 验证组件正常渲染
    expect(screen.getByTestId('count')).toBeInTheDocument();
    expect(screen.getByText('Count: 0')).toBeInTheDocument();

    // 触发几次重新渲染
    const incrementButton = screen.getByTestId('increment');
    
    fireEvent.click(incrementButton);
    await waitFor(() => {
      expect(screen.getByText('Count: 1')).toBeInTheDocument();
    });

    fireEvent.click(incrementButton);
    await waitFor(() => {
      expect(screen.getByText('Count: 2')).toBeInTheDocument();
    });

    fireEvent.click(incrementButton);
    await waitFor(() => {
      expect(screen.getByText('Count: 3')).toBeInTheDocument();
    });

    // 验证onRender回调被调用了合理的次数（不应该是无限次）
    // 应该有初始渲染 + 3次更新 = 4次调用
    expect(onRender).toHaveBeenCalledTimes(4);
    
    // 验证没有出现性能警告（说明没有无限循环导致的性能问题）
    expect(console.warn).not.toHaveBeenCalledWith(
      expect.stringContaining('Slow render detected')
    );
  });

  test('should work when disabled', () => {
    const onRender = jest.fn();
    
    render(
      <PerformanceProfiler id="test-profiler" enabled={false} onRender={onRender}>
        <TestComponent />
      </PerformanceProfiler>
    );

    // 验证组件正常渲染
    expect(screen.getByTestId('count')).toBeInTheDocument();
    
    // 当disabled时，onRender不应该被调用
    expect(onRender).not.toHaveBeenCalled();
  });

  test('should handle custom onRender callback', async () => {
    const customOnRender = jest.fn();
    
    render(
      <PerformanceProfiler id="test-profiler" enabled={true} onRender={customOnRender}>
        <TestComponent />
      </PerformanceProfiler>
    );

    // 触发重新渲染
    fireEvent.click(screen.getByTestId('increment'));
    
    await waitFor(() => {
      expect(screen.getByText('Count: 1')).toBeInTheDocument();
    });

    // 验证自定义回调被调用
    expect(customOnRender).toHaveBeenCalled();
    
    // 验证回调参数
    const lastCall = customOnRender.mock.calls[customOnRender.mock.calls.length - 1];
    expect(lastCall[0]).toBe('test-profiler'); // id
    expect(lastCall[1]).toBe('update'); // phase
    expect(typeof lastCall[2]).toBe('number'); // actualDuration
    expect(typeof lastCall[3]).toBe('number'); // baseDuration
  });
});
