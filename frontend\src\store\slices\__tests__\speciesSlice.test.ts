import { configureStore } from '@reduxjs/toolkit';
import speciesSlice, {
  searchSpecies,
  fetchSpeciesDetails,
  setSearchParams,
  clearCurrentSpecies,
  clearError,
} from '../speciesSlice';
import { server } from '../../../__mocks__/server';
import { rest } from 'msw';

// 创建测试store
const createTestStore = () => {
  return configureStore({
    reducer: {
      species: speciesSlice,
    },
  });
};

describe('speciesSlice', () => {
  let store: ReturnType<typeof createTestStore>;

  beforeEach(() => {
    store = createTestStore();
  });

  describe('初始状态', () => {
    it('应该返回正确的初始状态', () => {
      const state = store.getState().species;
      expect(state).toEqual({
        speciesList: [],
        searchParams: {
          page: 1,
          limit: 20,
          sortBy: 'chineseName',
          sortOrder: 'ASC',
        },
        pagination: {
          current: 1,
          pageSize: 20,
          total: 0,
          totalPages: 0,
        },
        currentSpecies: null,
        speciesDetails: null,
        taxonomyStats: null,
        conservationStats: null,
        loading: {
          list: false,
          detail: false,
          stats: false,
        },
        error: null,
      });
    });
  });

  describe('同步actions', () => {
    it('应该处理setSearchParams action', () => {
      const newParams = { keyword: '蓝鲸', page: 2 };
      store.dispatch(setSearchParams(newParams));
      
      const state = store.getState().species;
      expect(state.searchParams).toEqual({
        page: 2,
        limit: 20,
        sortBy: 'chineseName',
        sortOrder: 'ASC',
        keyword: '蓝鲸',
      });
    });

    it('应该处理clearCurrentSpecies action', () => {
      // 先设置当前物种
      store.dispatch({
        type: 'species/fetchSpeciesDetails/fulfilled',
        payload: { id: '1', chineseName: '蓝鲸' }
      });
      
      // 清除当前物种
      store.dispatch(clearCurrentSpecies());
      
      const state = store.getState().species;
      expect(state.currentSpecies).toBeNull();
      expect(state.speciesDetails).toBeNull();
    });

    it('应该处理clearError action', () => {
      // 先设置错误状态
      store.dispatch({ type: 'species/searchSpecies/rejected', payload: 'Test error' });
      
      // 清除错误
      store.dispatch(clearError());
      
      const state = store.getState().species;
      expect(state.error).toBeNull();
    });
  });

  describe('异步actions', () => {
    describe('searchSpecies', () => {
      it('应该成功搜索物种', async () => {
        const searchParams = { keyword: '蓝鲸', page: 1, limit: 20 };
        
        const result = await store.dispatch(searchSpecies(searchParams));
        
        expect(result.type).toBe('species/searchSpecies/fulfilled');
        
        const state = store.getState().species;
        expect(state.loading.list).toBe(false);
        expect(state.speciesList).toHaveLength(1);
        expect(state.speciesList[0].chineseName).toBe('蓝鲸');
        expect(state.pagination.total).toBe(1);
        expect(state.error).toBeNull();
      });

      it('应该处理搜索失败', async () => {
        // 模拟搜索失败
        server.use(
          rest.get('http://localhost:3001/api/species/search', (req, res, ctx) => {
            return res(
              ctx.status(500),
              ctx.json({
                success: false,
                error: { message: '服务器错误' }
              })
            );
          })
        );

        const searchParams = { keyword: '不存在的物种' };
        
        const result = await store.dispatch(searchSpecies(searchParams));
        
        expect(result.type).toBe('species/searchSpecies/rejected');
        
        const state = store.getState().species;
        expect(state.loading.list).toBe(false);
        expect(state.error).toBe('服务器错误');
      });

      it('应该正确处理搜索参数', async () => {
        const searchParams = {
          keyword: '鲸',
          conservationStatus: '濒危',
          kingdom: '动物界',
          page: 2,
          limit: 10,
          sortBy: 'latinName',
          sortOrder: 'DESC' as const
        };
        
        // 监听请求参数
        let requestUrl: string = '';
        server.use(
          rest.get('http://localhost:3001/api/species/search', (req, res, ctx) => {
            requestUrl = req.url.href;
            return res(
              ctx.status(200),
              ctx.json({
                success: true,
                data: {
                  species: [],
                  total: 0,
                  page: 2,
                  limit: 10,
                  totalPages: 0
                }
              })
            );
          })
        );
        
        await store.dispatch(searchSpecies(searchParams));
        
        // 验证请求参数
        expect(requestUrl).toContain('keyword=%E9%B2%B8'); // URL编码的'鲸'
        expect(requestUrl).toContain('conservationStatus=%E6%BF%92%E5%8D%B1'); // URL编码的'濒危'
        expect(requestUrl).toContain('page=2');
        expect(requestUrl).toContain('limit=10');
      });
    });

    describe('fetchSpeciesDetails', () => {
      it('应该成功获取物种详情', async () => {
        const speciesId = '1';
        
        const result = await store.dispatch(fetchSpeciesDetails(speciesId));
        
        expect(result.type).toBe('species/fetchSpeciesDetails/fulfilled');
        
        const state = store.getState().species;
        expect(state.loading.detail).toBe(false);
        expect(state.speciesDetails).toBeTruthy();
        expect(state.currentSpecies).toBeTruthy();
        expect(state.currentSpecies?.id).toBe(speciesId);
        expect(state.error).toBeNull();
      });

      it('应该处理获取详情失败', async () => {
        // 模拟获取详情失败
        server.use(
          rest.get('http://localhost:3001/api/species/:id/details', (req, res, ctx) => {
            return res(
              ctx.status(404),
              ctx.json({
                success: false,
                error: { message: '物种不存在' }
              })
            );
          })
        );

        const speciesId = 'nonexistent';
        
        const result = await store.dispatch(fetchSpeciesDetails(speciesId));
        
        expect(result.type).toBe('species/fetchSpeciesDetails/rejected');
        
        const state = store.getState().species;
        expect(state.loading.detail).toBe(false);
        expect(state.error).toBe('物种不存在');
      });
    });
  });

  describe('loading状态', () => {
    it('应该在搜索期间设置list loading状态', async () => {
      const searchParams = { keyword: '蓝鲸' };
      
      // 开始搜索
      const searchPromise = store.dispatch(searchSpecies(searchParams));
      
      // 检查loading状态
      let state = store.getState().species;
      expect(state.loading.list).toBe(true);
      expect(state.loading.detail).toBe(false);
      
      // 等待完成
      await searchPromise;
      
      // 检查loading状态已重置
      state = store.getState().species;
      expect(state.loading.list).toBe(false);
    });

    it('应该在获取详情期间设置detail loading状态', async () => {
      const speciesId = '1';
      
      // 开始获取详情
      const detailPromise = store.dispatch(fetchSpeciesDetails(speciesId));
      
      // 检查loading状态
      let state = store.getState().species;
      expect(state.loading.detail).toBe(true);
      expect(state.loading.list).toBe(false);
      
      // 等待完成
      await detailPromise;
      
      // 检查loading状态已重置
      state = store.getState().species;
      expect(state.loading.detail).toBe(false);
    });
  });

  describe('分页处理', () => {
    it('应该正确更新分页信息', async () => {
      // 模拟分页数据
      server.use(
        rest.get('http://localhost:3001/api/species/search', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json({
              success: true,
              data: {
                species: [
                  { id: '1', chineseName: '蓝鲸' },
                  { id: '2', chineseName: '座头鲸' }
                ],
                total: 50,
                page: 2,
                limit: 20,
                totalPages: 3
              }
            })
          );
        })
      );

      const searchParams = { page: 2, limit: 20 };
      
      await store.dispatch(searchSpecies(searchParams));
      
      const state = store.getState().species;
      expect(state.pagination).toEqual({
        current: 2,
        pageSize: 20,
        total: 50,
        totalPages: 3,
      });
    });
  });
});
