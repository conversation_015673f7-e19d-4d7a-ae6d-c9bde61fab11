import request from 'supertest';
import { DataSource } from 'typeorm';
import bcrypt from 'bcrypt';
import app from '../../app';
import { getTestDataSource, createTestUser } from '../../__tests__/utils/database';
import { generateTestToken, expectSuccessResponse, expectErrorResponse } from '../../__tests__/utils/helpers';
import { User } from '../../models/User';
import { Role } from '../../models/Role';

describe('AuthController', () => {
  let dataSource: DataSource;
  let testUser: User;
  let adminUser: User;

  beforeAll(async () => {
    dataSource = getTestDataSource();
  });

  beforeEach(async () => {
    // 创建测试用户
    const userRepository = dataSource.getRepository(User);
    const roleRepository = dataSource.getRepository(Role);
    
    const viewerRole = await roleRepository.findOne({ where: { name: 'viewer' } });
    const adminRole = await roleRepository.findOne({ where: { name: 'admin' } });
    
    testUser = await createTestUser(dataSource, {
      username: 'testuser',
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('password123', 10),
      roles: viewerRole ? [viewerRole] : [],
    });
    
    adminUser = await createTestUser(dataSource, {
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('admin123', 10),
      roles: adminRole ? [adminRole] : [],
    });
  });

  describe('POST /api/auth/register', () => {
    it('应该成功注册新用户', async () => {
      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expectSuccessResponse(response);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.username).toBe(userData.username);
      expect(response.body.data.email).toBe(userData.email);
      expect(response.body.data).not.toHaveProperty('passwordHash');
    });

    it('应该验证必填字段', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({})
        .expect(400);

      expectErrorResponse(response);
      expect(response.body.error.message).toContain('用户名');
    });

    it('应该验证用户名唯一性', async () => {
      const userData = {
        username: testUser.username,
        email: '<EMAIL>',
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expectErrorResponse(response, '用户名已存在');
    });

    it('应该验证邮箱唯一性', async () => {
      const userData = {
        username: 'differentuser',
        email: testUser.email,
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expectErrorResponse(response, '邮箱已存在');
    });

    it('应该验证密码强度', async () => {
      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: '123', // 太短
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expectErrorResponse(response);
      expect(response.body.error.message).toContain('密码');
    });

    it('应该验证邮箱格式', async () => {
      const userData = {
        username: 'newuser',
        email: 'invalid-email',
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expectErrorResponse(response);
      expect(response.body.error.message).toContain('邮箱');
    });
  });

  describe('POST /api/auth/login', () => {
    it('应该成功登录用户', async () => {
      const loginData = {
        username: testUser.username,
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expectSuccessResponse(response);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user.username).toBe(testUser.username);
      expect(response.body.data.user).not.toHaveProperty('passwordHash');
    });

    it('应该支持邮箱登录', async () => {
      const loginData = {
        username: testUser.email,
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expectSuccessResponse(response);
      expect(response.body.data.user.email).toBe(testUser.email);
    });

    it('应该拒绝错误的用户名', async () => {
      const loginData = {
        username: 'nonexistent',
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expectErrorResponse(response, '用户名或密码错误');
    });

    it('应该拒绝错误的密码', async () => {
      const loginData = {
        username: testUser.username,
        password: 'wrongpassword',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expectErrorResponse(response, '用户名或密码错误');
    });

    it('应该验证必填字段', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({})
        .expect(400);

      expectErrorResponse(response);
    });

    it('应该更新最后登录时间', async () => {
      const loginData = {
        username: testUser.username,
        password: 'password123',
      };

      const beforeLogin = new Date();
      
      await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      // 验证数据库中的最后登录时间已更新
      const userRepository = dataSource.getRepository(User);
      const updatedUser = await userRepository.findOne({ where: { id: testUser.id } });
      
      expect(updatedUser?.lastLogin).toBeTruthy();
      expect(new Date(updatedUser!.lastLogin!)).toBeInstanceOf(Date);
      expect(new Date(updatedUser!.lastLogin!).getTime()).toBeGreaterThanOrEqual(beforeLogin.getTime());
    });
  });

  describe('GET /api/auth/profile', () => {
    it('应该返回当前用户信息', async () => {
      const token = generateTestToken(testUser);

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expectSuccessResponse(response);
      expect(response.body.data.id).toBe(testUser.id);
      expect(response.body.data.username).toBe(testUser.username);
      expect(response.body.data).toHaveProperty('roles');
      expect(response.body.data).not.toHaveProperty('passwordHash');
    });

    it('应该拒绝无效的token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expectErrorResponse(response, '无效的访问令牌');
    });

    it('应该拒绝缺失的token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .expect(401);

      expectErrorResponse(response, '未找到访问令牌');
    });

    it('应该拒绝过期的token', async () => {
      // 这里需要生成一个过期的token进行测试
      const expiredToken = 'expired.jwt.token';

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      expectErrorResponse(response);
    });
  });

  describe('POST /api/auth/change-password', () => {
    it('应该成功修改密码', async () => {
      const token = generateTestToken(testUser);
      const passwordData = {
        oldPassword: 'password123',
        newPassword: 'newpassword123',
      };

      const response = await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send(passwordData)
        .expect(200);

      expectSuccessResponse(response);

      // 验证新密码可以登录
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: testUser.username,
          password: 'newpassword123',
        })
        .expect(200);

      expectSuccessResponse(loginResponse);
    });

    it('应该验证旧密码', async () => {
      const token = generateTestToken(testUser);
      const passwordData = {
        oldPassword: 'wrongpassword',
        newPassword: 'newpassword123',
      };

      const response = await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send(passwordData)
        .expect(400);

      expectErrorResponse(response, '当前密码错误');
    });

    it('应该验证新密码强度', async () => {
      const token = generateTestToken(testUser);
      const passwordData = {
        oldPassword: 'password123',
        newPassword: '123', // 太短
      };

      const response = await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send(passwordData)
        .expect(400);

      expectErrorResponse(response);
      expect(response.body.error.message).toContain('密码');
    });

    it('应该要求认证', async () => {
      const passwordData = {
        oldPassword: 'password123',
        newPassword: 'newpassword123',
      };

      const response = await request(app)
        .post('/api/auth/change-password')
        .send(passwordData)
        .expect(401);

      expectErrorResponse(response);
    });
  });
});
