import React, { useState, useEffect } from 'react';
import { Card, Typography, Space, Alert, Statistic, Row, Col } from 'antd';
import BaseMap from '../components/Map/BaseMap';
import HeatmapLayer, { generateMockHeatmapData } from '../components/Map/HeatmapLayer';

const { Title, Text } = Typography;

interface HeatmapPoint {
  lat: number;
  lng: number;
  intensity: number;
  species_count?: number;
  recording_count?: number;
}

const HeatmapTestPage: React.FC = () => {
  const [heatmapData, setHeatmapData] = useState<HeatmapPoint[]>([]);
  const [currentZoom, setCurrentZoom] = useState<number>(6);
  const [gridSize, setGridSize] = useState<number>(0.5);

  // 加载测试数据
  useEffect(() => {
    const mockData = generateMockHeatmapData(1000);
    setHeatmapData(mockData);
  }, []);

  // 计算当前网格大小（模拟 HeatmapLayer 内部逻辑）
  const calculateGridSize = (zoom: number, baseGridSize: number = 0.3): number => {
    if (zoom <= 4) return baseGridSize * 6.0;
    if (zoom <= 6) return baseGridSize * 3.0;
    if (zoom <= 8) return baseGridSize * 1.5;
    if (zoom <= 10) return baseGridSize * 0.8;
    if (zoom <= 12) return baseGridSize * 0.4;
    return baseGridSize * 0.2;
  };

  // 地图就绪回调
  const handleMapReady = (mapInstance: L.Map) => {
    // 监听缩放事件
    mapInstance.on('zoomend', () => {
      const zoom = mapInstance.getZoom();
      setCurrentZoom(zoom);
      setGridSize(calculateGridSize(zoom, 0.3));
    });
    
    // 初始化缩放级别
    setCurrentZoom(mapInstance.getZoom());
    setGridSize(calculateGridSize(mapInstance.getZoom(), 0.3));
  };

  // 获取缩放级别描述
  const getZoomDescription = (zoom: number): string => {
    if (zoom <= 4) return '大洲级别 - 大网格 (×6.0)';
    if (zoom <= 6) return '国家级别 - 中大网格 (×3.0)';
    if (zoom <= 8) return '地区级别 - 基础网格 (×1.5)';
    if (zoom <= 10) return '城市级别 - 小网格 (×0.8)';
    if (zoom <= 12) return '详细级别 - 很小网格 (×0.4)';
    return '最精细级别 - 最小网格 (×0.2)';
  };

  return (
    <div style={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      background: '#f5f5f5'
    }}>
      {/* 标题栏 */}
      <div style={{
        background: 'white',
        padding: '16px 24px',
        borderBottom: '1px solid #e8e8e8',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
          热力图缩放适应性测试
        </Title>
        <Text type="secondary">
          基于 eBird 实现的动态缩放热力图 - 热力块大小会根据缩放级别自动调整
        </Text>
      </div>

      {/* 主要内容区域 */}
      <div style={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        {/* 地图区域 */}
        <div style={{ flex: 1, position: 'relative' }}>
          <BaseMap
            center={[35.0, 139.0]}
            zoom={6}
            style={{ height: '100%', width: '100%' }}
            onMapReady={handleMapReady}
          >
            {heatmapData.length > 0 && (
              <HeatmapLayer
                data={heatmapData}
                options={{
                  gridSize: 0.3,
                  minOpacity: 0.2,
                  maxOpacity: 0.8,
                  gradient: {
                    '0.0': '#313695',
                    '0.1': '#4575b4',
                    '0.2': '#74add1',
                    '0.3': '#abd9e9',
                    '0.4': '#e0f3f8',
                    '0.5': '#ffffbf',
                    '0.6': '#fee090',
                    '0.7': '#fdae61',
                    '0.8': '#f46d43',
                    '0.9': '#d73027',
                    '1.0': '#a50026'
                  }
                }}
              />
            )}
          </BaseMap>
        </div>

        {/* 右侧信息面板 */}
        <div style={{
          width: '350px',
          background: 'white',
          borderLeft: '1px solid #e8e8e8',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'auto'
        }}>
          <div style={{ padding: '16px' }}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {/* 使用说明 */}
              <Alert
                message="使用说明"
                description="使用鼠标滚轮或地图缩放控件来缩放地图，观察热力块大小如何动态调整。"
                type="info"
                showIcon
              />

              {/* 当前状态 */}
              <Card size="small" title="当前状态">
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Statistic
                      title="缩放级别"
                      value={currentZoom}
                      precision={1}
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="网格大小"
                      value={gridSize}
                      precision={3}
                      suffix="°"
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Col>
                  <Col span={24}>
                    <div>
                      <Text strong>级别描述：</Text>
                      <br />
                      <Text type="secondary">{getZoomDescription(currentZoom)}</Text>
                    </div>
                  </Col>
                </Row>
              </Card>

              {/* 缩放级别说明 */}
              <Card size="small" title="缩放级别说明">
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <div>
                    <Text strong>1-4级：</Text>
                    <Text type="secondary"> 大洲级别，大网格 (×6.0)</Text>
                  </div>
                  <div>
                    <Text strong>5-6级：</Text>
                    <Text type="secondary"> 国家级别，中大网格 (×3.0)</Text>
                  </div>
                  <div>
                    <Text strong>7-8级：</Text>
                    <Text type="secondary"> 地区级别，基础网格 (×1.5)</Text>
                  </div>
                  <div>
                    <Text strong>9-10级：</Text>
                    <Text type="secondary"> 城市级别，小网格 (×0.8)</Text>
                  </div>
                  <div>
                    <Text strong>11-12级：</Text>
                    <Text type="secondary"> 详细级别，很小网格 (×0.4)</Text>
                  </div>
                  <div>
                    <Text strong>13+级：</Text>
                    <Text type="secondary"> 最精细级别，最小网格 (×0.2)</Text>
                  </div>
                </Space>
              </Card>

              {/* 数据统计 */}
              <Card size="small" title="数据统计">
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Statistic
                      title="数据点数"
                      value={heatmapData.length}
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="热点区域"
                      value={6}
                      valueStyle={{ color: '#eb2f96' }}
                    />
                  </Col>
                </Row>
              </Card>

              {/* 技术特性 */}
              <Card size="small" title="技术特性">
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <div>✅ 动态网格大小调整</div>
                  <div>✅ 250ms 防抖优化</div>
                  <div>✅ 缓存机制提升性能</div>
                  <div>✅ 平滑过渡效果</div>
                  <div>✅ 类似 eBird 的专业体验</div>
                </Space>
              </Card>
            </Space>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeatmapTestPage;
