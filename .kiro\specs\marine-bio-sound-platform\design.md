# Design Document

## Overview

海洋生物发声数据平台采用前后端分离的架构设计，包含用户前端、管理前端和后端API服务。系统核心围绕海洋生物数据管理、地理信息展示、多媒体处理和音频识别功能构建。

### 系统架构图

```mermaid
graph TB
    subgraph "用户端"
        A[交互式地图页面] --> B[物种详情页面]
        A --> C[搜索功能]
    end
    
    subgraph "管理端"
        D[用户权限管理] --> E[物种数据管理]
        E --> F[音频识别功能]
        D --> G[系统审计日志]
    end
    
    subgraph "后端服务"
        H[API网关] --> I[用户服务]
        H --> J[物种数据服务]
        H --> K[地理信息服务]
        H --> L[多媒体服务]
        H --> M[音频识别服务]
    end
    
    subgraph "数据存储"
        N[关系数据库] --> O[本地文件存储]
        N --> P[地理数据库]
    end
    
    subgraph "外部服务"
        Q[地图服务API]
        R[音频识别模型API]
    end
    
    A --> H
    B --> H
    D --> H
    E --> H
    F --> H
    
    I --> N
    J --> N
    K --> P
    L --> O
    M --> R
    
    A --> Q
```

## Architecture

### 前端架构

#### 用户前端
- **技术栈**: React + TypeScript + Leaflet/MapBox GL JS
- **状态管理**: Redux Toolkit
- **UI组件库**: Ant Design 或 Material-UI
- **地图组件**: Leaflet 用于交互式地图展示
- **音频处理**: Web Audio API + WaveSurfer.js

#### 管理前端
- **技术栈**: React + TypeScript + Ant Design Pro
- **状态管理**: Redux Toolkit
- **富文本编辑器**: React-Quill 或 TinyMCE
- **文件上传**: Ant Design Upload + 拖拽支持
- **权限控制**: RBAC权限模型

### 后端架构

#### API服务层
- **技术栈**: Node.js + Express 或 Python + FastAPI
- **认证授权**: JWT + RBAC
- **API文档**: OpenAPI/Swagger
- **中间件**: 权限验证、日志记录、错误处理

#### 数据访问层
- **ORM**: TypeORM (Node.js) 或 SQLAlchemy (Python)
- **数据库连接池**: 支持读写分离
- **缓存层**: Redis 用于会话和热点数据缓存

### 数据存储架构

#### 关系数据库 (PostgreSQL)
- 用户账户和权限数据
- 物种基础信息和分类数据
- 音频元数据和识别记录
- 系统操作日志

#### 地理数据库 (PostGIS)
- 物种分布范围数据 (KML解析后的几何数据)
- 音频采集点坐标数据
- 地理索引优化查询性能

#### 文件存储
- **本地文件系统**: 服务器本地磁盘存储
- **目录结构设计**:
  ```
  /uploads/
    ├── species/
    │   ├── {species-id}/
    │   │   ├── images/
    │   │   ├── videos/
    │   │   ├── audio/
    │   │   └── generated/  # 频谱图、全息谱
    │   └── ...
    ├── kml/
    └── temp/  # 临时上传文件
  ```
- **静态文件服务**: 通过Express.static或Nginx提供文件访问
- **文件管理**: 支持文件上传、删除、重命名等操作
- **存储优化**: 定期清理临时文件和无效引用文件
- **备份策略**: 定期备份重要文件到其他存储位置

## Components and Interfaces

### 核心组件设计

#### 1. 地图组件 (MapComponent)
```typescript
interface MapComponentProps {
  mode: 'distribution' | 'collection-points';
  onSpeciesClick: (speciesId: string) => void;
  onRegionClick: (species: Species[]) => void;
  searchQuery?: string;
}

interface MapData {
  distributionRanges: GeoJSON.FeatureCollection;
  collectionPoints: CollectionPoint[];
  clusterConfig: ClusterConfig;
}
```

#### 2. 物种详情组件 (SpeciesDetail)
```typescript
interface SpeciesDetailProps {
  speciesId: string;
}

interface SpeciesData {
  basicInfo: SpeciesBasicInfo;
  classification: TaxonomicClassification;
  description: string;
  mediaGallery: MediaItem[];
  audioFiles: AudioFile[];
  distributionMap: GeoJSON.Feature;
}
```

#### 3. 音频播放器组件 (AudioPlayer)
```typescript
interface AudioPlayerProps {
  audioFile: AudioFile;
  showSpectrogram: boolean;
  onProgressChange: (time: number) => void;
}

interface AudioFile {
  id: string;
  url: string;
  metadata: AudioMetadata;
  spectrogram?: SpectrogramData;
}
```

#### 4. 后台管理组件

##### 用户管理 (UserManagement)
```typescript
interface UserManagementProps {
  permissions: Permission[];
}

interface User {
  id: string;
  username: string;
  roles: Role[];
  status: 'active' | 'disabled';
  lastLogin?: Date;
}
```

##### 物种数据管理 (SpeciesManagement)
```typescript
interface SpeciesManagementProps {
  onSave: (species: SpeciesFormData) => void;
}

interface SpeciesFormData {
  basicInfo: SpeciesBasicInfo;
  description: string;
  mediaFiles: File[];
  kmlFile?: File;
  audioFiles: AudioFileWithMetadata[];
}
```

##### 文件上传组件 (FileUpload)
```typescript
interface FileUploadProps {
  fileType: 'image' | 'video' | 'audio' | 'kml';
  maxSize: number;
  acceptedFormats: string[];
  onUploadSuccess: (fileInfo: UploadedFileInfo) => void;
  onUploadError: (error: string) => void;
}

interface UploadedFileInfo {
  id: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  uploadTime: Date;
}
```

### API接口设计

#### 用户端API
```typescript
// 地图数据API
GET /api/map/distribution-ranges
GET /api/map/collection-points
GET /api/species/search?q={query}

// 物种详情API
GET /api/species/{id}
GET /api/species/{id}/media
GET /api/species/{id}/audio
```

#### 管理端API
```typescript
// 用户管理API
GET /api/admin/users
POST /api/admin/users
PUT /api/admin/users/{id}
DELETE /api/admin/users/{id}

// 物种管理API
GET /api/admin/species
POST /api/admin/species
PUT /api/admin/species/{id}
POST /api/admin/species/{id}/media
POST /api/admin/species/{id}/kml

// 文件管理API
POST /api/admin/upload/image
POST /api/admin/upload/video
POST /api/admin/upload/audio
POST /api/admin/upload/kml
DELETE /api/admin/files/{fileId}
GET /api/files/{filePath}  # 静态文件访问

// 音频识别API
POST /api/admin/audio/identify
GET /api/admin/audio/history
```

## Data Models

### 核心数据模型

#### 物种模型 (Species)
```sql
CREATE TABLE species (
    id UUID PRIMARY KEY,
    chinese_name VARCHAR(255) NOT NULL,
    english_name VARCHAR(255),
    latin_name VARCHAR(255) NOT NULL,
    conservation_status VARCHAR(50),
    description TEXT,
    representative_image_path VARCHAR(500), -- 本地图片路径
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 分类模型 (TaxonomicClassification)
```sql
CREATE TABLE taxonomic_classifications (
    id UUID PRIMARY KEY,
    species_id UUID REFERENCES species(id),
    kingdom VARCHAR(100),
    phylum VARCHAR(100),
    class VARCHAR(100),
    order_name VARCHAR(100),
    family VARCHAR(100),
    genus VARCHAR(100),
    species_name VARCHAR(100)
);
```

#### 音频文件模型 (AudioFile)
```sql
CREATE TABLE audio_files (
    id UUID PRIMARY KEY,
    species_id UUID REFERENCES species(id),
    file_path VARCHAR(500) NOT NULL,  -- 本地文件路径
    file_name VARCHAR(255),
    file_size BIGINT,
    mime_type VARCHAR(100),
    recording_location VARCHAR(255),
    recording_time TIMESTAMP,
    behavior_description TEXT,
    sampling_rate INTEGER,
    sensitivity DECIMAL,
    amplification_factor DECIMAL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 媒体文件模型 (MediaFile)
```sql
CREATE TABLE media_files (
    id UUID PRIMARY KEY,
    species_id UUID REFERENCES species(id),
    file_type VARCHAR(20) NOT NULL, -- 'image', 'video'
    file_path VARCHAR(500) NOT NULL,
    file_name VARCHAR(255),
    file_size BIGINT,
    mime_type VARCHAR(100),
    title VARCHAR(255),
    description TEXT,
    is_representative BOOLEAN DEFAULT FALSE, -- 是否为代表性图片
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_media_species ON media_files(species_id);
CREATE INDEX idx_media_type ON media_files(file_type);
```

#### 地理分布模型 (DistributionRange)
```sql
CREATE TABLE distribution_ranges (
    id UUID PRIMARY KEY,
    species_id UUID REFERENCES species(id),
    geometry GEOMETRY(MULTIPOLYGON, 4326),
    kml_file_path VARCHAR(500),  -- 本地KML文件路径
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_distribution_geometry ON distribution_ranges USING GIST(geometry);
```

#### 用户权限模型
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE roles (
    id UUID PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB
);

CREATE TABLE user_roles (
    user_id UUID REFERENCES users(id),
    role_id UUID REFERENCES roles(id),
    PRIMARY KEY (user_id, role_id)
);
```

## Error Handling

### 前端错误处理策略

#### 1. 全局错误边界
```typescript
class ErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误到监控系统
    logErrorToService(error, errorInfo);
  }
}
```

#### 2. API错误处理
```typescript
const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL,
});

apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // 处理认证失效
      redirectToLogin();
    }
    return Promise.reject(error);
  }
);
```

#### 3. 地图加载错误处理
```typescript
const MapComponent: React.FC = () => {
  const [mapError, setMapError] = useState<string | null>(null);
  
  const handleMapError = (error: Error) => {
    setMapError('地图加载失败，请刷新页面重试');
    console.error('Map loading error:', error);
  };
  
  return mapError ? <ErrorMessage message={mapError} /> : <Map />;
};
```

### 后端错误处理策略

#### 1. 统一错误响应格式
```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

#### 2. 中间件错误处理
```typescript
const errorHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      code: err.name || 'INTERNAL_ERROR',
      message: err.message || '服务器内部错误',
    },
    timestamp: new Date().toISOString(),
  };
  
  // 记录错误日志
  logger.error('API Error:', err);
  
  res.status(500).json(errorResponse);
};
```

#### 3. 数据库错误处理
```typescript
const handleDatabaseError = (error: any) => {
  if (error.code === '23505') { // 唯一约束违反
    throw new ConflictError('数据已存在');
  }
  if (error.code === '23503') { // 外键约束违反
    throw new BadRequestError('关联数据不存在');
  }
  throw new InternalServerError('数据库操作失败');
};
```

#### 4. 文件操作错误处理
```typescript
const handleFileError = (error: any) => {
  if (error.code === 'ENOENT') {
    throw new NotFoundError('文件不存在');
  }
  if (error.code === 'EACCES') {
    throw new ForbiddenError('文件访问权限不足');
  }
  if (error.code === 'ENOSPC') {
    throw new InternalServerError('磁盘空间不足');
  }
  if (error.code === 'LIMIT_FILE_SIZE') {
    throw new BadRequestError('文件大小超出限制');
  }
  throw new InternalServerError('文件操作失败');
};
```

## Testing Strategy

### 前端测试策略

#### 1. 单元测试
- **工具**: Jest + React Testing Library
- **覆盖范围**: 组件逻辑、工具函数、状态管理
- **测试重点**: 用户交互、数据展示、错误处理

```typescript
// 示例：地图组件测试
describe('MapComponent', () => {
  test('should render map with distribution mode by default', () => {
    render(<MapComponent mode="distribution" onSpeciesClick={jest.fn()} />);
    expect(screen.getByTestId('map-container')).toBeInTheDocument();
  });
  
  test('should call onSpeciesClick when species is clicked', async () => {
    const mockOnClick = jest.fn();
    render(<MapComponent mode="distribution" onSpeciesClick={mockOnClick} />);
    
    // 模拟点击地图上的物种区域
    fireEvent.click(screen.getByTestId('species-region-1'));
    expect(mockOnClick).toHaveBeenCalledWith('species-1');
  });
});
```

#### 2. 集成测试
- **工具**: Cypress 或 Playwright
- **测试场景**: 用户完整操作流程
- **重点流程**: 地图交互、物种详情查看、音频播放

```typescript
// 示例：端到端测试
describe('Species Discovery Flow', () => {
  it('should allow user to discover species through map', () => {
    cy.visit('/');
    cy.get('[data-testid="map-container"]').should('be.visible');
    cy.get('[data-testid="species-region"]').first().click();
    cy.url().should('include', '/species/');
    cy.get('[data-testid="species-name"]').should('be.visible');
  });
});
```

### 后端测试策略

#### 1. 单元测试
- **工具**: Jest (Node.js) 或 pytest (Python)
- **覆盖范围**: 业务逻辑、数据处理、工具函数

```typescript
// 示例：物种服务测试
describe('SpeciesService', () => {
  test('should create species with valid data', async () => {
    const speciesData = {
      chineseName: '蓝鲸',
      latinName: 'Balaenoptera musculus',
      conservationStatus: 'EN'
    };
    
    const result = await speciesService.createSpecies(speciesData);
    expect(result.id).toBeDefined();
    expect(result.chineseName).toBe('蓝鲸');
  });
});
```

#### 2. API集成测试
- **工具**: Supertest (Node.js) 或 pytest + httpx (Python)
- **测试范围**: API端点、权限验证、数据验证

```typescript
// 示例：API测试
describe('Species API', () => {
  test('GET /api/species should return species list', async () => {
    const response = await request(app)
      .get('/api/species')
      .expect(200);
      
    expect(response.body.success).toBe(true);
    expect(Array.isArray(response.body.data)).toBe(true);
  });
  
  test('POST /api/admin/species should require authentication', async () => {
    await request(app)
      .post('/api/admin/species')
      .send({ chineseName: '测试物种' })
      .expect(401);
  });
});
```

#### 3. 数据库测试
- **策略**: 使用测试数据库，每个测试后清理数据
- **工具**: 数据库事务回滚或测试容器

```typescript
// 示例：数据库测试设置
beforeEach(async () => {
  await database.beginTransaction();
});

afterEach(async () => {
  await database.rollbackTransaction();
});
```

#### 4. 文件操作测试
- **策略**: 使用临时测试目录，测试后自动清理
- **测试范围**: 文件上传、删除、访问权限

```typescript
// 示例：文件操作测试
describe('File Upload Service', () => {
  const testUploadDir = path.join(__dirname, 'test-uploads');
  
  beforeEach(async () => {
    await fs.ensureDir(testUploadDir);
  });
  
  afterEach(async () => {
    await fs.remove(testUploadDir);
  });
  
  test('should upload image file successfully', async () => {
    const mockFile = {
      originalname: 'test.jpg',
      buffer: Buffer.from('fake image data'),
      mimetype: 'image/jpeg'
    };
    
    const result = await fileService.uploadImage(mockFile, 'species-1');
    expect(result.filePath).toContain('species-1/images/');
    expect(await fs.pathExists(result.filePath)).toBe(true);
  });
});
```

### 性能测试

#### 1. 前端性能测试
- **工具**: Lighthouse、WebPageTest
- **指标**: 首屏加载时间、地图渲染性能、音频播放延迟

#### 2. 后端性能测试
- **工具**: Artillery、JMeter
- **场景**: API响应时间、并发用户处理、数据库查询性能

#### 3. 地理数据查询优化测试
```sql
-- 测试地理查询性能
EXPLAIN ANALYZE 
SELECT s.chinese_name, s.latin_name 
FROM species s 
JOIN distribution_ranges dr ON s.id = dr.species_id 
WHERE ST_Contains(dr.geometry, ST_Point(120.0, 30.0));
```

### 测试数据管理

#### 1. 测试数据准备
- 创建标准测试数据集
- 包含各种边界情况和异常数据
- 地理数据测试用例覆盖全球不同区域

#### 2. 测试环境隔离
- 开发、测试、生产环境完全隔离
- 使用Docker容器化测试环境
- 自动化测试数据重置和清理