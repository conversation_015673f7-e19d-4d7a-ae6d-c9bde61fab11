import React, { useEffect, useRef, useCallback, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { getMapConfig, MapServiceType, getOptimalMapService } from '../../config/mapConfig';

// 修复Leaflet默认图标问题
delete (L.Icon.Default.prototype as unknown)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface BaseMapProps {
  center?: [number, number];
  zoom?: number;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  onMapReady?: (map: L.Map) => void;
  mapService?: MapServiceType;
  enableAutoSwitch?: boolean; // 是否启用自动地图源切换
  onMapServiceChange?: (newService: MapServiceType) => void; // 地图源变化回调
}

// 动态地图源管理组件
const DynamicMapSource: React.FC<{
  mapService: MapServiceType;
  enableAutoSwitch: boolean;
  onMapServiceChange?: (newService: MapServiceType) => void;
}> = ({ mapService, enableAutoSwitch, onMapServiceChange }) => {
  const map = useMap();
  const [currentService, setCurrentService] = useState(mapService);
  const lastBoundsRef = useRef<L.LatLngBounds | null>(null);

  // 检查是否需要切换地图源
  const checkMapServiceSwitch = useCallback(() => {
    if (!enableAutoSwitch || !map) return;

    const bounds = map.getBounds();
    const zoom = map.getZoom();

    // 避免频繁切换，只有当边界变化较大时才检查
    if (lastBoundsRef.current && lastBoundsRef.current.equals(bounds)) {
      return;
    }

    lastBoundsRef.current = bounds;

    const boundsObj = {
      north: bounds.getNorth(),
      south: bounds.getSouth(),
      east: bounds.getEast(),
      west: bounds.getWest(),
    };

    const optimalService = getOptimalMapService(boundsObj, zoom);

    if (optimalService !== currentService) {
      setCurrentService(optimalService);
      onMapServiceChange?.(optimalService);
    }
  }, [map, enableAutoSwitch, currentService, onMapServiceChange]);

  // 监听地图移动和缩放事件
  useEffect(() => {
    if (!map || !enableAutoSwitch) return;

    const handleMoveEnd = () => {
      // 使用防抖避免频繁触发
      setTimeout(checkMapServiceSwitch, 300);
    };

    const handleZoomEnd = () => {
      setTimeout(checkMapServiceSwitch, 300);
    };

    map.on('moveend', handleMoveEnd);
    map.on('zoomend', handleZoomEnd);

    // 初始检查
    checkMapServiceSwitch();

    return () => {
      map.off('moveend', handleMoveEnd);
      map.off('zoomend', handleZoomEnd);
    };
  }, [map, enableAutoSwitch, checkMapServiceSwitch]);

  // 当外部传入的mapService变化时，更新当前服务
  useEffect(() => {
    if (mapService !== currentService) {
      setCurrentService(mapService);
    }
  }, [mapService, currentService]);

  return null;
};

// 地图事件处理组件 - 使用useCallback优化
const MapEventHandler: React.FC<{
  onMapReady?: (map: L.Map) => void;
  mapService: MapServiceType;
  enableAutoSwitch: boolean;
  onMapServiceChange?: (newService: MapServiceType) => void;
}> = ({ onMapReady, mapService, enableAutoSwitch, onMapServiceChange }) => {
  const map = useMap();
  const mapRef = useRef<L.Map | null>(null);

  const handleMapReady = useCallback(() => {
    if (onMapReady && map && mapRef.current !== map) {
      mapRef.current = map;
      onMapReady(map);
    }
  }, [map, onMapReady]);

  useEffect(() => {
    handleMapReady();
  }, [handleMapReady]);

  return (
    <DynamicMapSource
      mapService={mapService}
      enableAutoSwitch={enableAutoSwitch}
      onMapServiceChange={onMapServiceChange}
    />
  );
};

const BaseMap: React.FC<BaseMapProps> = ({
  center = [30.0, 120.0],
  zoom = 5,
  style = { height: '500px', width: '100%' },
  children,
  onMapReady,
  mapService = MapServiceType.AMAP, // 改为高德地图作为默认
  enableAutoSwitch = true, // 默认启用自动切换
  onMapServiceChange,
}) => {
  const [currentMapService, setCurrentMapService] = useState(mapService);

  // 处理地图源变化
  const handleMapServiceChange = useCallback((newService: MapServiceType) => {
    setCurrentMapService(newService);
    onMapServiceChange?.(newService);
  }, [onMapServiceChange]);

  // 当外部传入的mapService变化时，更新当前服务
  useEffect(() => {
    setCurrentMapService(mapService);
  }, [mapService]);

  // 获取地图配置 - 使用当前的地图服务
  const mapConfig = getMapConfig(currentMapService);
  const { base, annotation } = mapConfig;

  return (
    <MapContainer
      center={center}
      zoom={zoom}
      style={style}
      scrollWheelZoom={true}
      zoomControl={true}
    >
      {/* 底图瓦片层 */}
      <TileLayer
        attribution={base.attribution}
        url={base.url}
        subdomains={base.subdomains}
        maxZoom={base.maxZoom}
        minZoom={base.minZoom}
      />

      {/* 注记层（如果存在） */}
      {annotation && (
        <TileLayer
          attribution={annotation.attribution}
          url={annotation.url}
          subdomains={annotation.subdomains}
          maxZoom={annotation.maxZoom}
          minZoom={annotation.minZoom}
        />
      )}

      {/* 地图事件处理 */}
      <MapEventHandler
        onMapReady={onMapReady}
        mapService={currentMapService}
        enableAutoSwitch={enableAutoSwitch}
        onMapServiceChange={handleMapServiceChange}
      />

      {/* 子组件 */}
      {children}
    </MapContainer>
  );
};

export default BaseMap;
