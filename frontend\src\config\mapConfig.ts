/**
 * 地图配置文件
 * 提供符合中国地图审查要求的地图服务配置
 */

export interface MapTileConfig {
  name: string;
  url: string;
  attribution: string;
  subdomains?: string[];
  maxZoom?: number;
  minZoom?: number;
  requiresKey?: boolean;
}

export interface MapLayerConfig {
  base: MapTileConfig;
  annotation?: MapTileConfig;
}

/**
 * 天地图配置 - 国家地理信息公共服务平台
 * 官方地图服务，符合中国地图审查要求
 */
export const getTiandituConfig = (apiKey?: string): MapLayerConfig => {
  const key = apiKey || import.meta.env.VITE_TIANDITU_KEY || '';
  
  return {
    // 天地图影像底图
    base: {
      name: '天地图影像',
      url: `https://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${key}`,
      attribution: '&copy; <a href="https://www.tianditu.gov.cn/">天地图</a>',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      maxZoom: 18,
      minZoom: 1,
      requiresKey: true,
    },
    // 天地图影像注记
    annotation: {
      name: '天地图注记',
      url: `https://t{s}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${key}`,
      attribution: '',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      maxZoom: 18,
      minZoom: 1,
      requiresKey: true,
    },
  };
};

/**
 * 天地图矢量地图配置
 */
export const getTiandituVectorConfig = (apiKey?: string): MapLayerConfig => {
  const key = apiKey || import.meta.env.VITE_TIANDITU_KEY || '';
  
  return {
    // 天地图矢量底图
    base: {
      name: '天地图矢量',
      url: `https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${key}`,
      attribution: '&copy; <a href="https://www.tianditu.gov.cn/">天地图</a>',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      maxZoom: 18,
      minZoom: 1,
      requiresKey: true,
    },
    // 天地图矢量注记
    annotation: {
      name: '天地图矢量注记',
      url: `https://t{s}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${key}`,
      attribution: '',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      maxZoom: 18,
      minZoom: 1,
      requiresKey: true,
    },
  };
};

/**
 * 高德地图配置（需要申请API密钥）
 * 符合中国地图审查要求的商业地图服务
 */
export const getAMapConfig = (apiKey?: string): MapLayerConfig => {
  return {
    base: {
      name: '高德地图',
      url: 'https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
      attribution: '&copy; <a href="https://www.amap.com/">高德地图</a>',
      subdomains: ['1', '2', '3', '4'],
      maxZoom: 18,
      minZoom: 3,
      requiresKey: false, // 基础瓦片服务不需要密钥
    },
  };
};

/**
 * 百度地图配置
 * 符合中国地图审查要求的商业地图服务
 */
export const getBaiduMapConfig = (): MapLayerConfig => {
  return {
    base: {
      name: '百度地图',
      url: 'https://maponline{s}.bdimg.com/tile/?qt=vtile&x={x}&y={y}&z={z}&styles=pl&scaler=1&udt=20210804',
      attribution: '&copy; <a href="https://map.baidu.com/">百度地图</a>',
      subdomains: ['0', '1', '2', '3'],
      maxZoom: 19,
      minZoom: 3,
      requiresKey: false,
    },
  };
};

/**
 * OpenStreetMap配置
 * 开源地图服务，提供全球范围的详细地图数据
 * 主要用于中国境外地区的地图显示
 */
export const getOpenStreetMapConfig = (): MapLayerConfig => {
  return {
    base: {
      name: 'OpenStreetMap',
      url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      subdomains: ['a', 'b', 'c'],
      maxZoom: 19,
      minZoom: 1,
      requiresKey: false,
    },
  };
};

/**
 * CartoDB Positron配置 - 简洁的全球地图样式
 * 适合作为数据可视化的底图
 */
export const getCartoDBConfig = (): MapLayerConfig => {
  return {
    base: {
      name: 'CartoDB Positron',
      url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
      subdomains: ['a', 'b', 'c', 'd'],
      maxZoom: 19,
      minZoom: 1,
      requiresKey: false,
    },
  };
};

/**
 * 地图服务类型枚举
 */
export enum MapServiceType {
  TIANDITU_SATELLITE = 'tianditu_satellite',
  TIANDITU_VECTOR = 'tianditu_vector',
  AMAP = 'amap',
  BAIDU = 'baidu',
  OPENSTREETMAP = 'openstreetmap',
  CARTODB = 'cartodb',
}

/**
 * 获取地图配置
 */
export const getMapConfig = (
  serviceType: MapServiceType = MapServiceType.AMAP, // 改为高德地图作为默认
  apiKey?: string
): MapLayerConfig => {
  switch (serviceType) {
    case MapServiceType.TIANDITU_SATELLITE:
      return getTiandituConfig(apiKey);
    case MapServiceType.TIANDITU_VECTOR:
      return getTiandituVectorConfig(apiKey);
    case MapServiceType.AMAP:
      return getAMapConfig(apiKey);
    case MapServiceType.BAIDU:
      return getBaiduMapConfig();
    case MapServiceType.OPENSTREETMAP:
      return getOpenStreetMapConfig();
    case MapServiceType.CARTODB:
      return getCartoDBConfig();
    default:
      return getAMapConfig(apiKey); // 默认使用高德地图
  }
};

/**
 * 默认地图配置 - 使用高德地图（无需API密钥）
 */
export const DEFAULT_MAP_CONFIG = getMapConfig(MapServiceType.AMAP);

/**
 * 地图中心点配置（中国区域）
 */
export const MAP_CENTER_CHINA: [number, number] = [35.0, 104.0];

/**
 * 地图缩放级别配置
 */
export const MAP_ZOOM_LEVELS = {
  COUNTRY: 4,    // 国家级别
  PROVINCE: 6,   // 省级别
  CITY: 10,      // 城市级别
  DISTRICT: 13,  // 区县级别
  STREET: 16,    // 街道级别
};

/**
 * 中国地理边界定义（包含台湾、香港、澳门）
 * 用于判断当前视图是否在中国境内
 */
export const CHINA_BOUNDS = {
  north: 53.5,   // 最北端（黑龙江）
  south: 3.8,    // 最南端（南海诸岛）
  east: 135.0,   // 最东端（黑龙江）
  west: 73.5,    // 最西端（新疆）
};

/**
 * 判断给定的经纬度是否在中国境内
 * @param lat 纬度
 * @param lng 经度
 * @returns 是否在中国境内
 */
export const isInChina = (lat: number, lng: number): boolean => {
  return (
    lat >= CHINA_BOUNDS.south &&
    lat <= CHINA_BOUNDS.north &&
    lng >= CHINA_BOUNDS.west &&
    lng <= CHINA_BOUNDS.east
  );
};

/**
 * 判断地图视图边界是否主要在中国境内
 * @param bounds 地图视图边界
 * @returns 是否主要在中国境内
 */
export const isViewMainlyInChina = (bounds: {
  north: number;
  south: number;
  east: number;
  west: number;
}): boolean => {
  // 计算视图中心点
  const centerLat = (bounds.north + bounds.south) / 2;
  const centerLng = (bounds.east + bounds.west) / 2;

  // 如果中心点在中国境内，认为主要在中国
  if (isInChina(centerLat, centerLng)) {
    return true;
  }

  // 计算视图与中国边界的重叠面积比例
  const overlapNorth = Math.min(bounds.north, CHINA_BOUNDS.north);
  const overlapSouth = Math.max(bounds.south, CHINA_BOUNDS.south);
  const overlapEast = Math.min(bounds.east, CHINA_BOUNDS.east);
  const overlapWest = Math.max(bounds.west, CHINA_BOUNDS.west);

  // 如果有重叠
  if (overlapNorth > overlapSouth && overlapEast > overlapWest) {
    const overlapArea = (overlapNorth - overlapSouth) * (overlapEast - overlapWest);
    const viewArea = (bounds.north - bounds.south) * (bounds.east - bounds.west);

    // 如果重叠面积超过视图面积的30%，认为主要在中国
    return overlapArea / viewArea > 0.3;
  }

  return false;
};

/**
 * 根据地图视图自动选择最佳地图服务
 * @param bounds 当前地图视图边界
 * @param currentZoom 当前缩放级别
 * @returns 推荐的地图服务类型
 */
export const getOptimalMapService = (
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  },
  currentZoom: number
): MapServiceType => {
  // 如果视图主要在中国境内，使用高德地图
  if (isViewMainlyInChina(bounds)) {
    return MapServiceType.AMAP;
  }

  // 如果在中国境外，根据缩放级别选择合适的地图服务
  if (currentZoom <= MAP_ZOOM_LEVELS.COUNTRY) {
    // 低缩放级别使用CartoDB，样式简洁适合概览
    return MapServiceType.CARTODB;
  } else {
    // 高缩放级别使用OpenStreetMap，数据详细
    return MapServiceType.OPENSTREETMAP;
  }
};
