/**
 * 地图配置文件
 * 提供符合中国地图审查要求的地图服务配置
 */

export interface MapTileConfig {
  name: string;
  url: string;
  attribution: string;
  subdomains?: string[];
  maxZoom?: number;
  minZoom?: number;
  requiresKey?: boolean;
}

export interface MapLayerConfig {
  base: MapTileConfig;
  annotation?: MapTileConfig;
}

/**
 * 天地图配置 - 国家地理信息公共服务平台
 * 官方地图服务，符合中国地图审查要求
 */
export const getTiandituConfig = (apiKey?: string): MapLayerConfig => {
  const key = apiKey || import.meta.env.VITE_TIANDITU_KEY || '';
  
  return {
    // 天地图影像底图
    base: {
      name: '天地图影像',
      url: `https://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${key}`,
      attribution: '&copy; <a href="https://www.tianditu.gov.cn/">天地图</a>',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      maxZoom: 18,
      minZoom: 1,
      requiresKey: true,
    },
    // 天地图影像注记
    annotation: {
      name: '天地图注记',
      url: `https://t{s}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${key}`,
      attribution: '',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      maxZoom: 18,
      minZoom: 1,
      requiresKey: true,
    },
  };
};

/**
 * 天地图矢量地图配置
 */
export const getTiandituVectorConfig = (apiKey?: string): MapLayerConfig => {
  const key = apiKey || import.meta.env.VITE_TIANDITU_KEY || '';
  
  return {
    // 天地图矢量底图
    base: {
      name: '天地图矢量',
      url: `https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${key}`,
      attribution: '&copy; <a href="https://www.tianditu.gov.cn/">天地图</a>',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      maxZoom: 18,
      minZoom: 1,
      requiresKey: true,
    },
    // 天地图矢量注记
    annotation: {
      name: '天地图矢量注记',
      url: `https://t{s}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${key}`,
      attribution: '',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      maxZoom: 18,
      minZoom: 1,
      requiresKey: true,
    },
  };
};

/**
 * 高德地图配置（需要申请API密钥）
 * 符合中国地图审查要求的商业地图服务
 */
export const getAMapConfig = (apiKey?: string): MapLayerConfig => {
  return {
    base: {
      name: '高德地图',
      url: 'https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
      attribution: '&copy; <a href="https://www.amap.com/">高德地图</a>',
      subdomains: ['1', '2', '3', '4'],
      maxZoom: 18,
      minZoom: 3,
      requiresKey: false, // 基础瓦片服务不需要密钥
    },
  };
};

/**
 * 百度地图配置
 * 符合中国地图审查要求的商业地图服务
 */
export const getBaiduMapConfig = (): MapLayerConfig => {
  return {
    base: {
      name: '百度地图',
      url: 'https://maponline{s}.bdimg.com/tile/?qt=vtile&x={x}&y={y}&z={z}&styles=pl&scaler=1&udt=20210804',
      attribution: '&copy; <a href="https://map.baidu.com/">百度地图</a>',
      subdomains: ['0', '1', '2', '3'],
      maxZoom: 19,
      minZoom: 3,
      requiresKey: false,
    },
  };
};

/**
 * 地图服务类型枚举
 */
export enum MapServiceType {
  TIANDITU_SATELLITE = 'tianditu_satellite',
  TIANDITU_VECTOR = 'tianditu_vector',
  AMAP = 'amap',
  BAIDU = 'baidu',
}

/**
 * 获取地图配置
 */
export const getMapConfig = (
  serviceType: MapServiceType = MapServiceType.AMAP, // 改为高德地图作为默认
  apiKey?: string
): MapLayerConfig => {
  switch (serviceType) {
    case MapServiceType.TIANDITU_SATELLITE:
      return getTiandituConfig(apiKey);
    case MapServiceType.TIANDITU_VECTOR:
      return getTiandituVectorConfig(apiKey);
    case MapServiceType.AMAP:
      return getAMapConfig(apiKey);
    case MapServiceType.BAIDU:
      return getBaiduMapConfig();
    default:
      return getAMapConfig(apiKey); // 默认使用高德地图
  }
};

/**
 * 默认地图配置 - 使用高德地图（无需API密钥）
 */
export const DEFAULT_MAP_CONFIG = getMapConfig(MapServiceType.AMAP);

/**
 * 地图中心点配置（中国区域）
 */
export const MAP_CENTER_CHINA: [number, number] = [35.0, 104.0];

/**
 * 地图缩放级别配置
 */
export const MAP_ZOOM_LEVELS = {
  COUNTRY: 4,    // 国家级别
  PROVINCE: 6,   // 省级别
  CITY: 10,      // 城市级别
  DISTRICT: 13,  // 区县级别
  STREET: 16,    // 街道级别
};
