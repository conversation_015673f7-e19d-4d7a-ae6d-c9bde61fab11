import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, mockAuthState } from '../../__tests__/utils/test-utils';
import LoginPage from '../LoginPage';
import { server } from '../../__mocks__/server';
import { rest } from 'msw';

// 模拟react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('LoginPage', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('应该渲染登录表单', () => {
    render(<LoginPage />);
    
    expect(screen.getByText('海洋生物声音平台')).toBeInTheDocument();
    expect(screen.getByText('登录您的账户以访问管理功能')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('用户名')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('密码')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /登录/ })).toBeInTheDocument();
  });

  it('应该显示演示账户信息', () => {
    render(<LoginPage />);
    
    expect(screen.getByText('演示账户：')).toBeInTheDocument();
    expect(screen.getByText('用户名: admin')).toBeInTheDocument();
    expect(screen.getByText('密码: admin123')).toBeInTheDocument();
  });

  it('应该显示注册和返回首页链接', () => {
    render(<LoginPage />);
    
    expect(screen.getByText('立即注册')).toBeInTheDocument();
    expect(screen.getByText('返回首页')).toBeInTheDocument();
  });

  it('应该验证必填字段', async () => {
    const user = userEvent.setup();
    render(<LoginPage />);
    
    const loginButton = screen.getByRole('button', { name: /登录/ });
    
    // 点击登录按钮而不填写任何字段
    await user.click(loginButton);
    
    // 应该显示验证错误
    await waitFor(() => {
      expect(screen.getByText('请输入用户名')).toBeInTheDocument();
      expect(screen.getByText('请输入密码')).toBeInTheDocument();
    });
  });

  it('应该验证用户名最小长度', async () => {
    const user = userEvent.setup();
    render(<LoginPage />);
    
    const usernameInput = screen.getByPlaceholderText('用户名');
    const loginButton = screen.getByRole('button', { name: /登录/ });
    
    // 输入过短的用户名
    await user.type(usernameInput, 'ab');
    await user.click(loginButton);
    
    await waitFor(() => {
      expect(screen.getByText('用户名至少3个字符')).toBeInTheDocument();
    });
  });

  it('应该验证密码最小长度', async () => {
    const user = userEvent.setup();
    render(<LoginPage />);
    
    const usernameInput = screen.getByPlaceholderText('用户名');
    const passwordInput = screen.getByPlaceholderText('密码');
    const loginButton = screen.getByRole('button', { name: /登录/ });
    
    // 输入有效用户名和过短密码
    await user.type(usernameInput, 'testuser');
    await user.type(passwordInput, '12345');
    await user.click(loginButton);
    
    await waitFor(() => {
      expect(screen.getByText('密码至少6个字符')).toBeInTheDocument();
    });
  });

  it('应该成功处理登录', async () => {
    const user = userEvent.setup();
    render(<LoginPage />);
    
    const usernameInput = screen.getByPlaceholderText('用户名');
    const passwordInput = screen.getByPlaceholderText('密码');
    const loginButton = screen.getByRole('button', { name: /登录/ });
    
    // 填写有效的登录信息
    await user.type(usernameInput, 'testuser');
    await user.type(passwordInput, 'password123');
    await user.click(loginButton);
    
    // 应该显示加载状态
    await waitFor(() => {
      expect(loginButton).toBeDisabled();
    });
    
    // 等待登录完成
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/');
    });
  });

  it('应该处理登录失败', async () => {
    const user = userEvent.setup();
    
    // 模拟登录失败
    server.use(
      rest.post('http://localhost:3001/api/auth/login', (req, res, ctx) => {
        return res(
          ctx.status(401),
          ctx.json({
            success: false,
            error: { message: '用户名或密码错误' }
          })
        );
      })
    );
    
    render(<LoginPage />);
    
    const usernameInput = screen.getByPlaceholderText('用户名');
    const passwordInput = screen.getByPlaceholderText('密码');
    const loginButton = screen.getByRole('button', { name: /登录/ });
    
    // 填写错误的登录信息
    await user.type(usernameInput, 'wronguser');
    await user.type(passwordInput, 'wrongpass');
    await user.click(loginButton);
    
    // 等待错误消息显示
    await waitFor(() => {
      expect(screen.getByText('用户名或密码错误')).toBeInTheDocument();
    });
  });

  it('应该在已登录时重定向到首页', () => {
    // 渲染已登录状态的页面
    render(<LoginPage />, {
      preloadedState: {
        auth: {
          ...mockAuthState,
          isAuthenticated: true,
        }
      }
    });
    
    // 应该立即重定向
    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  it('应该在组件卸载时清除错误', () => {
    const { unmount } = render(<LoginPage />, {
      preloadedState: {
        auth: {
          ...mockAuthState,
          error: 'Some error',
        }
      }
    });
    
    // 卸载组件
    unmount();
    
    // 这里我们无法直接测试clearError的调用，
    // 但可以通过集成测试来验证这个行为
  });

  it('应该支持键盘导航', async () => {
    const user = userEvent.setup();
    render(<LoginPage />);
    
    const usernameInput = screen.getByPlaceholderText('用户名');
    const passwordInput = screen.getByPlaceholderText('密码');
    
    // 使用Tab键导航
    await user.tab();
    expect(usernameInput).toHaveFocus();
    
    await user.tab();
    expect(passwordInput).toHaveFocus();
    
    // 在密码框中按Enter应该提交表单
    await user.type(usernameInput, 'testuser');
    await user.type(passwordInput, 'password123');
    await user.keyboard('{Enter}');
    
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/');
    });
  });

  it('应该正确处理表单重置', async () => {
    const user = userEvent.setup();
    render(<LoginPage />);
    
    const usernameInput = screen.getByPlaceholderText('用户名');
    const passwordInput = screen.getByPlaceholderText('密码');
    
    // 填写表单
    await user.type(usernameInput, 'testuser');
    await user.type(passwordInput, 'password123');
    
    // 验证输入值
    expect(usernameInput).toHaveValue('testuser');
    expect(passwordInput).toHaveValue('password123');
    
    // 清空表单（模拟某种重置操作）
    await user.clear(usernameInput);
    await user.clear(passwordInput);
    
    expect(usernameInput).toHaveValue('');
    expect(passwordInput).toHaveValue('');
  });
});
