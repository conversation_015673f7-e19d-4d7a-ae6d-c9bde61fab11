import { Request, Response } from 'express';
import { FileCleanupService } from '../services/FileCleanupService';

export class FileManagementController {
  private fileCleanupService: FileCleanupService;

  constructor() {
    this.fileCleanupService = new FileCleanupService();
  }

  /**
   * 清理临时文件
   */
  cleanupTempFiles = async (req: Request, res: Response) => {
    try {
      const { maxAgeHours = 24 } = req.query;
      const maxAge = parseInt(maxAgeHours as string);

      if (isNaN(maxAge) || maxAge < 1) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_MAX_AGE',
            message: '最大文件年龄必须是大于0的数字（小时）',
          },
        });
      }

      const cleanedCount = await this.fileCleanupService.cleanupTempFiles(maxAge);

      res.json({
        success: true,
        data: {
          cleanedCount,
          maxAgeHours: maxAge,
        },
        message: `成功清理了 ${cleanedCount} 个临时文件`,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'CLEANUP_TEMP_FILES_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 清理孤立文件
   */
  cleanupOrphanedFiles = async (req: Request, res: Response) => {
    try {
      const cleanedCount = await this.fileCleanupService.cleanupOrphanedFiles();

      res.json({
        success: true,
        data: {
          cleanedCount,
        },
        message: `成功清理了 ${cleanedCount} 个孤立文件`,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'CLEANUP_ORPHANED_FILES_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 获取存储统计信息
   */
  getStorageStats = async (req: Request, res: Response) => {
    try {
      const stats = await this.fileCleanupService.getStorageStats();

      res.json({
        success: true,
        data: stats,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'GET_STORAGE_STATS_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * 运行完整的清理任务
   */
  runCleanupTask = async (req: Request, res: Response) => {
    try {
      const result = await this.fileCleanupService.runCleanupTask();

      res.json({
        success: true,
        data: result,
        message: `清理任务完成：清理了 ${result.tempFiles} 个临时文件和 ${result.orphanedFiles} 个孤立文件`,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: {
          code: 'RUN_CLEANUP_TASK_ERROR',
          message: error.message,
        },
      });
    }
  };
}
