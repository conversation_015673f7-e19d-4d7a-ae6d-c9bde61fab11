import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { speciesApi } from '../../services/api/speciesApi';
import type { 
  Species, 
  SpeciesFormData, 
  SpeciesSearchParams, 
  TaxonomyStats, 
  ConservationStats,
  PaginatedResponse 
} from '../../types';

interface SpeciesState {
  // 物种列表
  species: Species[];
  searchParams: SpeciesSearchParams;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  
  // 当前物种详情
  currentSpecies: Species | null;
  speciesDetails: any | null;
  
  // 统计信息
  taxonomyStats: TaxonomyStats | null;
  conservationStats: ConservationStats[] | null;
  
  // 加载状态
  loading: {
    list: boolean;
    detail: boolean;
    create: boolean;
    update: boolean;
    delete: boolean;
    stats: boolean;
    import: boolean;
    export: boolean;
  };
  
  error: string | null;
}

const initialState: SpeciesState = {
  species: [],
  searchParams: {
    page: 1,
    limit: 20,
    sortBy: 'chineseName',
    sortOrder: 'ASC',
  },
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  },
  currentSpecies: null,
  speciesDetails: null,
  taxonomyStats: null,
  conservationStats: null,
  loading: {
    list: false,
    detail: false,
    create: false,
    update: false,
    delete: false,
    stats: false,
    import: false,
    export: false,
  },
  error: null,
};

// 获取物种列表
export const fetchSpecies = createAsyncThunk(
  'species/fetchSpecies',
  async (params: SpeciesSearchParams = {}, { rejectWithValue }) => {
    try {
      const response = await speciesApi.getSpecies(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '获取物种列表失败');
    }
  }
);

// 搜索物种
export const searchSpecies = createAsyncThunk(
  'species/searchSpecies',
  async (params: SpeciesSearchParams = {}, { rejectWithValue }) => {
    try {
      const response = await speciesApi.searchSpecies(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '搜索物种失败');
    }
  }
);

// 获取物种详情
export const fetchSpeciesById = createAsyncThunk(
  'species/fetchSpeciesById',
  async (id: string, { rejectWithValue }) => {
    try {
      const species = await speciesApi.getSpeciesById(id);
      return species;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '获取物种详情失败');
    }
  }
);

// 获取物种详细信息
export const fetchSpeciesDetails = createAsyncThunk(
  'species/fetchSpeciesDetails',
  async (id: string, { rejectWithValue }) => {
    try {
      const details = await speciesApi.getSpeciesDetails(id);
      return details;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '获取物种详细信息失败');
    }
  }
);

// 创建物种
export const createSpecies = createAsyncThunk(
  'species/createSpecies',
  async (data: SpeciesFormData, { rejectWithValue }) => {
    try {
      const species = await speciesApi.createSpecies(data);
      return species;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '创建物种失败');
    }
  }
);

// 更新物种
export const updateSpecies = createAsyncThunk(
  'species/updateSpecies',
  async ({ id, data }: { id: string; data: Partial<SpeciesFormData> }, { rejectWithValue }) => {
    try {
      const species = await speciesApi.updateSpecies(id, data);
      return species;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '更新物种失败');
    }
  }
);

// 删除物种
export const deleteSpecies = createAsyncThunk(
  'species/deleteSpecies',
  async (id: string, { rejectWithValue }) => {
    try {
      await speciesApi.deleteSpecies(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '删除物种失败');
    }
  }
);

// 获取分类统计
export const fetchTaxonomyStats = createAsyncThunk(
  'species/fetchTaxonomyStats',
  async (_, { rejectWithValue }) => {
    try {
      const stats = await speciesApi.getTaxonomyStats();
      return stats;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '获取分类统计失败');
    }
  }
);

// 获取保护状态统计
export const fetchConservationStats = createAsyncThunk(
  'species/fetchConservationStats',
  async (_, { rejectWithValue }) => {
    try {
      const stats = await speciesApi.getConservationStats();
      return stats;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || '获取保护状态统计失败');
    }
  }
);

const speciesSlice = createSlice({
  name: 'species',
  initialState,
  reducers: {
    setSearchParams: (state, action) => {
      state.searchParams = { ...state.searchParams, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentSpecies: (state) => {
      state.currentSpecies = null;
      state.speciesDetails = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取物种列表
      .addCase(fetchSpecies.pending, (state) => {
        state.loading.list = true;
        state.error = null;
      })
      .addCase(fetchSpecies.fulfilled, (state, action) => {
        state.loading.list = false;
        state.species = action.payload.data;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchSpecies.rejected, (state, action) => {
        state.loading.list = false;
        state.error = action.payload as string;
      })
      // 搜索物种
      .addCase(searchSpecies.pending, (state) => {
        state.loading.list = true;
      })
      .addCase(searchSpecies.fulfilled, (state, action) => {
        state.loading.list = false;
        state.species = action.payload.data;
        state.pagination = action.payload.pagination;
      })
      .addCase(searchSpecies.rejected, (state, action) => {
        state.loading.list = false;
        state.error = action.payload as string;
      })
      // 获取物种详情
      .addCase(fetchSpeciesById.pending, (state) => {
        state.loading.detail = true;
      })
      .addCase(fetchSpeciesById.fulfilled, (state, action) => {
        state.loading.detail = false;
        state.currentSpecies = action.payload;
      })
      .addCase(fetchSpeciesById.rejected, (state, action) => {
        state.loading.detail = false;
        state.error = action.payload as string;
      })
      // 获取物种详细信息
      .addCase(fetchSpeciesDetails.fulfilled, (state, action) => {
        state.speciesDetails = action.payload;
      })
      // 创建物种
      .addCase(createSpecies.pending, (state) => {
        state.loading.create = true;
      })
      .addCase(createSpecies.fulfilled, (state, action) => {
        state.loading.create = false;
        state.species.unshift(action.payload);
      })
      .addCase(createSpecies.rejected, (state, action) => {
        state.loading.create = false;
        state.error = action.payload as string;
      })
      // 更新物种
      .addCase(updateSpecies.pending, (state) => {
        state.loading.update = true;
      })
      .addCase(updateSpecies.fulfilled, (state, action) => {
        state.loading.update = false;
        const index = state.species.findIndex(s => s.id === action.payload.id);
        if (index !== -1) {
          state.species[index] = action.payload;
        }
        if (state.currentSpecies?.id === action.payload.id) {
          state.currentSpecies = action.payload;
        }
      })
      .addCase(updateSpecies.rejected, (state, action) => {
        state.loading.update = false;
        state.error = action.payload as string;
      })
      // 删除物种
      .addCase(deleteSpecies.pending, (state) => {
        state.loading.delete = true;
      })
      .addCase(deleteSpecies.fulfilled, (state, action) => {
        state.loading.delete = false;
        state.species = state.species.filter(s => s.id !== action.payload);
      })
      .addCase(deleteSpecies.rejected, (state, action) => {
        state.loading.delete = false;
        state.error = action.payload as string;
      })
      // 获取统计信息
      .addCase(fetchTaxonomyStats.pending, (state) => {
        state.loading.stats = true;
      })
      .addCase(fetchTaxonomyStats.fulfilled, (state, action) => {
        state.loading.stats = false;
        state.taxonomyStats = action.payload;
      })
      .addCase(fetchConservationStats.fulfilled, (state, action) => {
        state.conservationStats = action.payload;
      });
  },
});

export const { setSearchParams, clearError, clearCurrentSpecies } = speciesSlice.actions;
export default speciesSlice.reducer;
