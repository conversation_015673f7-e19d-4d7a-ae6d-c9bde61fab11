import 'reflect-metadata';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import path from 'path';
import { initializeDatabase } from './config/database';
import authRoutes from './routes/auth';
import speciesRoutes from './routes/species';
import mediaRoutes from './routes/media';
import geographyRoutes from './routes/geography';
import fileManagementRoutes from './routes/fileManagement';

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件配置
app.use(helmet());

// CORS配置 - 支持多个允许的源
const corsOrigins = process.env.CORS_ORIGIN 
  ? process.env.CORS_ORIGIN.split(',').map(origin => origin.trim())
  : ['http://localhost:5173'];

app.use(cors({
  origin: (origin, callback) => {
    // 允许没有origin的请求（比如移动应用或Postman）
    if (!origin) return callback(null, true);
    
    // 检查origin是否在允许列表中
    if (corsOrigins.includes(origin)) {
      return callback(null, true);
    }
    
    // 在开发环境中，允许localhost的任何端口
    if (process.env.NODE_ENV === 'development' && origin.startsWith('http://localhost:')) {
      return callback(null, true);
    }
    
    callback(new Error('不允许的CORS源'));
  },
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/files', express.static(path.join(__dirname, '../uploads')));

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/species', speciesRoutes);
app.use('/api/media', mediaRoutes);
app.use('/api/geography', geographyRoutes);
app.use('/api/files', fileManagementRoutes);

app.get('/api', (req, res) => {
  res.json({
    message: '海洋生物声音平台 API',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      auth: '/api/auth',
      species: '/api/species',
      media: '/api/media',
      geography: '/api/geography',
      files: '/api/files',
      api: '/api'
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: '请求的资源不存在'
    }
  });
});

// 全局错误处理
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err);
  
  res.status(err.status || 500).json({
    success: false,
    error: {
      code: err.code || 'INTERNAL_ERROR',
      message: err.message || '服务器内部错误'
    },
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
const startServer = async () => {
  try {
    // 初始化数据库连接
    await initializeDatabase();

    app.listen(PORT, () => {
      console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
      console.log(`📁 静态文件服务: http://localhost:${PORT}/files`);
      console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
      console.log(`📡 API端点: http://localhost:${PORT}/api`);
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
};

startServer();

export default app;
