import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../__tests__/utils/test-utils';
import BaseAudioPlayer from '../BaseAudioPlayer';

// 模拟WaveSurfer
const mockWaveSurfer = {
  create: jest.fn(),
  load: jest.fn(),
  play: jest.fn(),
  pause: jest.fn(),
  stop: jest.fn(),
  playPause: jest.fn(),
  setVolume: jest.fn(),
  getDuration: jest.fn(() => 120),
  getCurrentTime: jest.fn(() => 30),
  on: jest.fn(),
  destroy: jest.fn(),
};

jest.mock('wavesurfer.js', () => ({
  __esModule: true,
  default: {
    create: jest.fn(() => mockWaveSurfer),
  },
}));

// 模拟音频URL
const mockAudioUrl = 'http://localhost:3001/files/test-audio.wav';

describe('BaseAudioPlayer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // 重置WaveSurfer模拟
    mockWaveSurfer.create.mockReturnValue(mockWaveSurfer);
    mockWaveSurfer.on.mockImplementation((event, callback) => {
      // 模拟ready事件
      if (event === 'ready') {
        setTimeout(() => callback(), 100);
      }
    });
  });

  it('应该渲染音频播放器', () => {
    render(
      <BaseAudioPlayer 
        audioUrl={mockAudioUrl}
        title="测试音频"
      />
    );
    
    expect(screen.getByText('测试音频')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /play/ })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /reload/ })).toBeInTheDocument();
  });

  it('应该初始化WaveSurfer', () => {
    render(
      <BaseAudioPlayer 
        audioUrl={mockAudioUrl}
        showWaveform={true}
        height={100}
      />
    );
    
    expect(mockWaveSurfer.create).toHaveBeenCalledWith(
      expect.objectContaining({
        waveColor: '#1890ff',
        progressColor: '#096dd9',
        height: 100,
      })
    );
    
    expect(mockWaveSurfer.load).toHaveBeenCalledWith(mockAudioUrl);
  });

  it('应该处理播放/暂停', async () => {
    const user = userEvent.setup();
    render(<BaseAudioPlayer audioUrl={mockAudioUrl} />);
    
    const playButton = screen.getByRole('button', { name: /play/ });
    
    await user.click(playButton);
    
    expect(mockWaveSurfer.playPause).toHaveBeenCalled();
  });

  it('应该处理停止', async () => {
    const user = userEvent.setup();
    render(<BaseAudioPlayer audioUrl={mockAudioUrl} />);
    
    const stopButton = screen.getByRole('button', { name: /reload/ });
    
    await user.click(stopButton);
    
    expect(mockWaveSurfer.stop).toHaveBeenCalled();
  });

  it('应该处理音量控制', async () => {
    const user = userEvent.setup();
    render(<BaseAudioPlayer audioUrl={mockAudioUrl} />);
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(screen.queryByText('正在加载')).not.toBeInTheDocument();
    });
    
    // 查找音量滑块
    const volumeSlider = screen.getByRole('slider');
    
    // 模拟音量变化
    fireEvent.change(volumeSlider, { target: { value: '50' } });
    
    expect(mockWaveSurfer.setVolume).toHaveBeenCalledWith(0.5);
  });

  it('应该处理静音切换', async () => {
    const user = userEvent.setup();
    render(<BaseAudioPlayer audioUrl={mockAudioUrl} />);
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(screen.queryByText('正在加载')).not.toBeInTheDocument();
    });
    
    // 查找静音按钮
    const muteButton = screen.getByRole('button', { name: /静音|取消静音/ });
    
    await user.click(muteButton);
    
    expect(mockWaveSurfer.setVolume).toHaveBeenCalledWith(0);
  });

  it('应该显示时间信息', async () => {
    // 模拟音频时长和当前时间
    mockWaveSurfer.getDuration.mockReturnValue(120); // 2分钟
    mockWaveSurfer.getCurrentTime.mockReturnValue(30); // 30秒
    
    render(<BaseAudioPlayer audioUrl={mockAudioUrl} />);
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(screen.getByText('0:30 / 2:00')).toBeInTheDocument();
    });
  });

  it('应该处理下载功能', async () => {
    const user = userEvent.setup();
    
    // 模拟document.createElement和click
    const mockLink = {
      href: '',
      download: '',
      click: jest.fn(),
    };
    const createElementSpy = jest.spyOn(document, 'createElement').mockReturnValue(mockLink as any);
    const appendChildSpy = jest.spyOn(document.body, 'appendChild').mockImplementation();
    const removeChildSpy = jest.spyOn(document.body, 'removeChild').mockImplementation();
    
    render(
      <BaseAudioPlayer 
        audioUrl={mockAudioUrl}
        title="测试音频.wav"
      />
    );
    
    const downloadButton = screen.getByRole('button', { name: /下载/ });
    
    await user.click(downloadButton);
    
    expect(createElementSpy).toHaveBeenCalledWith('a');
    expect(mockLink.href).toBe(mockAudioUrl);
    expect(mockLink.download).toBe('测试音频.wav');
    expect(mockLink.click).toHaveBeenCalled();
    
    // 清理模拟
    createElementSpy.mockRestore();
    appendChildSpy.mockRestore();
    removeChildSpy.mockRestore();
  });

  it('应该调用回调函数', () => {
    const onPlay = jest.fn();
    const onPause = jest.fn();
    const onFinish = jest.fn();
    
    render(
      <BaseAudioPlayer 
        audioUrl={mockAudioUrl}
        onPlay={onPlay}
        onPause={onPause}
        onFinish={onFinish}
      />
    );
    
    // 模拟WaveSurfer事件
    const onCall = mockWaveSurfer.on.mock.calls;
    
    // 查找并触发play事件
    const playCallback = onCall.find(call => call[0] === 'play')?.[1];
    if (playCallback) playCallback();
    expect(onPlay).toHaveBeenCalled();
    
    // 查找并触发pause事件
    const pauseCallback = onCall.find(call => call[0] === 'pause')?.[1];
    if (pauseCallback) pauseCallback();
    expect(onPause).toHaveBeenCalled();
    
    // 查找并触发finish事件
    const finishCallback = onCall.find(call => call[0] === 'finish')?.[1];
    if (finishCallback) finishCallback();
    expect(onFinish).toHaveBeenCalled();
  });

  it('应该支持自动播放', () => {
    render(
      <BaseAudioPlayer 
        audioUrl={mockAudioUrl}
        autoPlay={true}
      />
    );
    
    // 模拟ready事件触发自动播放
    const onCall = mockWaveSurfer.on.mock.calls;
    const readyCallback = onCall.find(call => call[0] === 'ready')?.[1];
    
    if (readyCallback) {
      readyCallback();
      expect(mockWaveSurfer.play).toHaveBeenCalled();
    }
  });

  it('应该在组件卸载时清理WaveSurfer', () => {
    const { unmount } = render(<BaseAudioPlayer audioUrl={mockAudioUrl} />);
    
    unmount();
    
    expect(mockWaveSurfer.destroy).toHaveBeenCalled();
  });

  it('应该处理加载状态', () => {
    render(<BaseAudioPlayer audioUrl={mockAudioUrl} />);
    
    // 初始状态应该显示加载
    const playButton = screen.getByRole('button', { name: /play/ });
    expect(playButton).toBeDisabled();
  });

  it('应该格式化时间显示', () => {
    // 测试时间格式化函数
    mockWaveSurfer.getDuration.mockReturnValue(125); // 2分5秒
    mockWaveSurfer.getCurrentTime.mockReturnValue(65); // 1分5秒
    
    render(<BaseAudioPlayer audioUrl={mockAudioUrl} />);
    
    // 触发ready事件以更新时间显示
    const onCall = mockWaveSurfer.on.mock.calls;
    const readyCallback = onCall.find(call => call[0] === 'ready')?.[1];
    if (readyCallback) readyCallback();
    
    // 应该显示格式化的时间
    expect(screen.getByText(/1:05 \/ 2:05/)).toBeInTheDocument();
  });
});
