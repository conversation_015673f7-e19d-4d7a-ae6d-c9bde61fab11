import React, { useState, useEffect } from 'react';
import { Row, Col, Statistic, Card } from 'antd';
import { 
  TeamOutlined, 
  SoundOutlined, 
  PictureOutlined,
  GlobalOutlined 
} from '@ant-design/icons';
import BaseChart from './BaseChart';
import type { EChartsOption } from 'echarts';

interface SpeciesStatsData {
  totalSpecies: number;
  totalAudioFiles: number;
  totalImageFiles: number;
  totalDistributions: number;
  taxonomyStats: Array<{
    name: string;
    count: number;
  }>;
  conservationStats: Array<{
    status: string;
    count: number;
    color: string;
  }>;
  monthlyStats: Array<{
    month: string;
    species: number;
    audio: number;
    image: number;
  }>;
}

interface SpeciesStatsChartProps {
  data?: SpeciesStatsData;
  loading?: boolean;
}

const SpeciesStatsChart: React.FC<SpeciesStatsChartProps> = ({
  data,
  loading = false,
}) => {
  const [statsData, setStatsData] = useState<SpeciesStatsData | null>(data || null);

  // 模拟数据
  useEffect(() => {
    if (!data) {
      // 模拟获取统计数据
      const mockData: SpeciesStatsData = {
        totalSpecies: 156,
        totalAudioFiles: 1248,
        totalImageFiles: 892,
        totalDistributions: 89,
        taxonomyStats: [
          { name: '哺乳动物', count: 45 },
          { name: '鱼类', count: 67 },
          { name: '甲壳动物', count: 23 },
          { name: '软体动物', count: 21 },
        ],
        conservationStats: [
          { status: '无危', count: 78, color: '#52c41a' },
          { status: '近危', count: 34, color: '#faad14' },
          { status: '易危', count: 28, color: '#fa8c16' },
          { status: '濒危', count: 12, color: '#f5222d' },
          { status: '极危', count: 4, color: '#722ed1' },
        ],
        monthlyStats: [
          { month: '1月', species: 12, audio: 89, image: 67 },
          { month: '2月', species: 15, audio: 102, image: 78 },
          { month: '3月', species: 18, audio: 134, image: 89 },
          { month: '4月', species: 22, audio: 156, image: 112 },
          { month: '5月', species: 19, audio: 178, image: 134 },
          { month: '6月', species: 25, audio: 198, image: 156 },
        ],
      };
      setStatsData(mockData);
    }
  }, [data]);

  // 分类学统计饼图配置
  const taxonomyChartOption: EChartsOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
    },
    series: [
      {
        name: '物种分类',
        type: 'pie',
        radius: '50%',
        data: statsData?.taxonomyStats.map(item => ({
          value: item.count,
          name: item.name,
        })) || [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };

  // 保护状态柱状图配置
  const conservationChartOption: EChartsOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    xAxis: {
      type: 'category',
      data: statsData?.conservationStats.map(item => item.status) || [],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '物种数量',
        type: 'bar',
        data: statsData?.conservationStats.map(item => ({
          value: item.count,
          itemStyle: {
            color: item.color,
          },
        })) || [],
        emphasis: {
          focus: 'series',
        },
      },
    ],
  };

  // 月度趋势图配置
  const monthlyTrendOption: EChartsOption = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['新增物种', '音频文件', '图片文件'],
    },
    xAxis: {
      type: 'category',
      data: statsData?.monthlyStats.map(item => item.month) || [],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '新增物种',
        type: 'line',
        data: statsData?.monthlyStats.map(item => item.species) || [],
        smooth: true,
        itemStyle: {
          color: '#1890ff',
        },
      },
      {
        name: '音频文件',
        type: 'line',
        data: statsData?.monthlyStats.map(item => item.audio) || [],
        smooth: true,
        itemStyle: {
          color: '#52c41a',
        },
      },
      {
        name: '图片文件',
        type: 'line',
        data: statsData?.monthlyStats.map(item => item.image) || [],
        smooth: true,
        itemStyle: {
          color: '#fa8c16',
        },
      },
    ],
  };

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="物种总数"
              value={statsData?.totalSpecies || 0}
              prefix={<TeamOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="音频文件"
              value={statsData?.totalAudioFiles || 0}
              prefix={<SoundOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="图片文件"
              value={statsData?.totalImageFiles || 0}
              prefix={<PictureOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="分布区域"
              value={statsData?.totalDistributions || 0}
              prefix={<GlobalOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <BaseChart
            title="物种分类统计"
            subtitle="按分类学分组的物种数量分布"
            option={taxonomyChartOption}
            height={350}
            loading={loading}
          />
        </Col>
        <Col xs={24} lg={12}>
          <BaseChart
            title="保护状态统计"
            subtitle="不同保护等级的物种数量"
            option={conservationChartOption}
            height={350}
            loading={loading}
          />
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col span={24}>
          <BaseChart
            title="月度数据趋势"
            subtitle="新增物种和文件数量的月度变化趋势"
            option={monthlyTrendOption}
            height={300}
            loading={loading}
          />
        </Col>
      </Row>
    </div>
  );
};

export default SpeciesStatsChart;
