// Cypress 自定义命令

// 登录命令
Cypress.Commands.add('login', (username: string, password: string) => {
  cy.visit('/login');
  cy.get('[data-testid="username-input"]').type(username);
  cy.get('[data-testid="password-input"]').type(password);
  cy.get('[data-testid="login-button"]').click();
  cy.wait('@login');
  cy.url().should('eq', Cypress.config().baseUrl + '/');
});

// 登出命令
Cypress.Commands.add('logout', () => {
  cy.get('[data-testid="user-menu"]').click();
  cy.get('[data-testid="logout-button"]').click();
  cy.url().should('include', '/');
  cy.get('[data-testid="login-link"]').should('be.visible');
});

// 等待页面加载完成
Cypress.Commands.add('waitForPageLoad', () => {
  cy.get('[data-testid="loading"]').should('not.exist');
  cy.get('body').should('be.visible');
});

// 文件上传命令
Cypress.Commands.add('uploadFile', (selector: string, fileName: string, fileType: string = 'text/plain') => {
  cy.fixture(fileName, 'base64').then(fileContent => {
    const blob = Cypress.Blob.base64StringToBlob(fileContent, fileType);
    const file = new File([blob], fileName, { type: fileType });
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(file);

    cy.get(selector).then(input => {
      const el = input[0] as HTMLInputElement;
      el.files = dataTransfer.files;
      el.dispatchEvent(new Event('change', { bubbles: true }));
    });
  });
});

// 可访问性检查
Cypress.Commands.add('checkA11y', () => {
  cy.injectAxe();
  cy.checkA11y(undefined, undefined, (violations) => {
    violations.forEach((violation) => {
      cy.task('log', `Accessibility violation: ${violation.description}`);
      cy.task('log', `Help: ${violation.helpUrl}`);
    });
  });
});

// 等待API请求
Cypress.Commands.add('waitForApi', (alias: string) => {
  cy.wait(`@${alias}`).then((interception) => {
    expect(interception.response?.statusCode).to.be.oneOf([200, 201, 204]);
  });
});

// 模拟网络错误
Cypress.Commands.add('mockNetworkError', (url: string) => {
  cy.intercept('GET', url, { forceNetworkError: true }).as('networkError');
});

// 设置认证状态
Cypress.Commands.add('setAuthState', (isAuthenticated: boolean) => {
  if (isAuthenticated) {
    const mockToken = 'mock-jwt-token';
    const mockUser = {
      id: '1',
      username: 'testuser',
      email: '<EMAIL>',
      roles: [{ name: 'viewer' }]
    };
    
    cy.window().then((win) => {
      win.localStorage.setItem('token', mockToken);
      win.localStorage.setItem('user', JSON.stringify(mockUser));
    });
  } else {
    cy.window().then((win) => {
      win.localStorage.removeItem('token');
      win.localStorage.removeItem('user');
    });
  }
});

// 添加拖拽命令
Cypress.Commands.add('drag', { prevSubject: 'element' }, (subject, targetSelector) => {
  cy.wrap(subject)
    .trigger('mousedown', { button: 0 })
    .wait(100);
  
  cy.get(targetSelector)
    .trigger('mousemove')
    .trigger('mouseup');
});

// 添加双击命令
Cypress.Commands.add('doubleClick', { prevSubject: 'element' }, (subject) => {
  cy.wrap(subject).dblclick();
});

// 添加悬停命令
Cypress.Commands.add('hover', { prevSubject: 'element' }, (subject) => {
  cy.wrap(subject).trigger('mouseover');
});

// 添加滚动到元素命令
Cypress.Commands.add('scrollToElement', (selector: string) => {
  cy.get(selector).scrollIntoView();
});

// 添加等待元素可见命令
Cypress.Commands.add('waitForVisible', (selector: string, timeout: number = 10000) => {
  cy.get(selector, { timeout }).should('be.visible');
});

// 添加等待元素消失命令
Cypress.Commands.add('waitForHidden', (selector: string, timeout: number = 10000) => {
  cy.get(selector, { timeout }).should('not.exist');
});

// 添加检查响应式设计命令
Cypress.Commands.add('checkResponsive', () => {
  const viewports = [
    { width: 320, height: 568 },  // iPhone SE
    { width: 768, height: 1024 }, // iPad
    { width: 1024, height: 768 }, // iPad Landscape
    { width: 1280, height: 720 }, // Desktop
  ];

  viewports.forEach(viewport => {
    cy.viewport(viewport.width, viewport.height);
    cy.wait(500); // 等待布局调整
    cy.get('body').should('be.visible');
  });
});

// 添加表单验证命令
Cypress.Commands.add('testFormValidation', (formSelector: string, fields: Array<{selector: string, value: string, error?: string}>) => {
  fields.forEach(field => {
    cy.get(field.selector).clear().type(field.value);
    if (field.error) {
      cy.get(formSelector).submit();
      cy.contains(field.error).should('be.visible');
    }
  });
});

// 添加键盘导航测试命令
Cypress.Commands.add('testKeyboardNavigation', (selectors: string[]) => {
  selectors.forEach((selector, index) => {
    if (index === 0) {
      cy.get(selector).focus();
    } else {
      cy.focused().tab();
      cy.focused().should('match', selector);
    }
  });
});

// 扩展Chainable接口
declare global {
  namespace Cypress {
    interface Chainable {
      drag(targetSelector: string): Chainable<Element>;
      doubleClick(): Chainable<Element>;
      hover(): Chainable<Element>;
      scrollToElement(selector: string): Chainable<void>;
      waitForVisible(selector: string, timeout?: number): Chainable<void>;
      waitForHidden(selector: string, timeout?: number): Chainable<void>;
      checkResponsive(): Chainable<void>;
      testFormValidation(formSelector: string, fields: Array<{selector: string, value: string, error?: string}>): Chainable<void>;
      testKeyboardNavigation(selectors: string[]): Chainable<void>;
    }
  }
}
