import React from 'react';
import { Typography } from 'antd';
import InteractiveMap from '../components/Map/InteractiveMap';

const { Title, Text } = Typography;

const MapPage: React.FC = () => {
  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0, marginBottom: '8px' }}>
          交互式地图
        </Title>
        <Text type="secondary">
          探索海洋生物的地理分布和声音数据采集点位置
        </Text>
      </div>

      <InteractiveMap height="600px" />
    </div>
  );
};

export default MapPage;
