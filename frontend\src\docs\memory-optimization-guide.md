# 内存优化和性能监控指南

## 概述

本指南介绍了前端应用的内存优化和性能监控系统的使用方法。

## 主要改进

### 1. 内存阈值调整
- **警告阈值**: 从 75% 调整到 85%
- **紧急阈值**: 从 90% 调整到 95%
- **PerformanceProfiler 阈值**: 从 80% 调整到 90%

### 2. 监控频率优化
- **内存检查间隔**: 从 30 秒增加到 60 秒
- **性能统计输出**: 从 10 秒增加到 30 秒
- **渲染内存检查**: 从每 50 次增加到每 100 次

### 3. 日志输出优化
- **日志频率**: 从每秒 10 条减少到 5 条
- **总日志数量**: 从 1000 条减少到 500 条
- **性能警告阈值**: 从 50ms 提高到 100ms

## 新增功能

### 1. 内存管理器 (MemoryManager)
```typescript
import { memoryManager } from './utils/memoryManager';

// 启动监控
memoryManager.start();

// 监听内存变化
memoryManager.onMemoryUpdate((info) => {
  console.log('内存使用:', info.percentage);
});

// 监听警告
memoryManager.onWarning((info) => {
  console.warn('内存警告:', info.percentage);
});

// 监听紧急情况
memoryManager.onCritical((info) => {
  console.error('内存紧急:', info.percentage);
});
```

### 2. React Hooks
```typescript
import { useMemoryMonitor, useMemoryStatus } from './hooks/useMemoryMonitor';

// 完整的内存监控
const {
  currentMemory,
  stats,
  isMonitoring,
  startMonitoring,
  stopMonitoring,
  forceGarbageCollection,
} = useMemoryMonitor();

// 简单的内存状态
const memoryStatus = useMemoryStatus();
```

### 3. 调试组件
```typescript
import MemoryMonitor from './components/Debug/MemoryMonitor';

// 在开发环境中显示内存监控
<MemoryMonitor 
  visible={true}
  compact={true}
  position="bottom-right"
/>
```

## URL 参数控制

### 禁用性能分析器
```
http://localhost:5173/?disable-profiler=true
```

### 隐藏内存监控组件
```
http://localhost:5173/?hide-memory-monitor=true
```

## 控制台命令

在浏览器控制台中可以使用以下命令：

```javascript
// 启用性能分析器
window.enableProfiler();

// 禁用性能分析器
window.disableProfiler();

// 查看性能分析器状态
window.profilerStatus();
```

## 内存优化建议

### 1. 组件级别
- 使用 `React.memo` 避免不必要的重新渲染
- 正确使用 `useCallback` 和 `useMemo`
- 及时清理事件监听器和定时器

### 2. 数据管理
- 限制存储在内存中的数据量
- 使用虚拟滚动处理大列表
- 实现数据分页和懒加载

### 3. 性能监控
- 定期检查内存使用趋势
- 监控组件渲染性能
- 使用性能分析器识别瓶颈

## 故障排除

### 内存使用过高
1. 检查是否有内存泄漏
2. 减少同时加载的数据量
3. 清理不必要的事件监听器
4. 使用 `forceGarbageCollection()` 强制垃圾回收

### 性能分析器被禁用
1. 检查内存使用是否超过阈值
2. 使用 `window.enableProfiler()` 重新启用
3. 刷新页面并添加 `?disable-profiler=false` 参数

### 监控组件不显示
1. 确保在开发环境中运行
2. 检查 URL 中是否有 `hide-memory-monitor=true`
3. 检查控制台是否有错误信息

## 配置选项

### MemoryManager 配置
```typescript
const memoryManager = MemoryManager.getInstance({
  warningThreshold: 85,     // 警告阈值
  criticalThreshold: 95,    // 紧急阈值
  checkInterval: 60000,     // 检查间隔（毫秒）
  historySize: 10,          // 历史记录大小
  trendSensitivity: 5,      // 趋势敏感度
});
```

### PerformanceLogger 配置
- `maxLogsPerSecond`: 每秒最大日志数量
- `maxTotalLogs`: 总日志数量限制
- `memoryThreshold`: 内存阈值（字节）

## 最佳实践

1. **开发环境**: 启用所有监控功能
2. **生产环境**: 禁用性能分析器和调试组件
3. **定期检查**: 监控内存使用趋势
4. **及时优化**: 发现问题及时修复
5. **文档更新**: 记录优化措施和效果

## 更新日志

### v1.1.0 (当前版本)
- 调整内存阈值，减少误报
- 优化监控频率，降低性能开销
- 新增内存管理器和 React Hooks
- 添加调试组件和控制台命令
- 改进错误处理和资源清理

### v1.0.0 (原始版本)
- 基础性能监控功能
- 内存压力检测
- 性能分析器集成
