import jwt from 'jsonwebtoken';
import { User } from '../../models/User';
import { Role } from '../../models/Role';

// 生成测试JWT令牌
export const generateTestToken = (user: User): string => {
  const payload = {
    userId: user.id,
    username: user.username,
    roles: user.roles?.map(role => role.name) || [],
  };
  
  return jwt.sign(payload, process.env.JWT_SECRET || 'test-secret', {
    expiresIn: '1h',
  });
};

// 生成过期的JWT令牌
export const generateExpiredToken = (user: User): string => {
  const payload = {
    userId: user.id,
    username: user.username,
    roles: user.roles?.map(role => role.name) || [],
  };
  
  return jwt.sign(payload, process.env.JWT_SECRET || 'test-secret', {
    expiresIn: '-1h', // 已过期
  });
};

// 生成无效的JWT令牌
export const generateInvalidToken = (): string => {
  return 'invalid.jwt.token';
};

// 创建模拟文件对象
export const createMockFile = (
  filename: string = 'test.wav',
  mimetype: string = 'audio/wav',
  size: number = 1024
) => {
  return {
    fieldname: 'file',
    originalname: filename,
    encoding: '7bit',
    mimetype,
    size,
    destination: 'uploads/',
    filename: `${Date.now()}-${filename}`,
    path: `uploads/${Date.now()}-${filename}`,
    buffer: Buffer.from('test file content'),
  };
};

// 创建模拟请求对象
export const createMockRequest = (overrides: any = {}) => {
  return {
    body: {},
    params: {},
    query: {},
    headers: {},
    user: null,
    file: null,
    files: null,
    ...overrides,
  };
};

// 创建模拟响应对象
export const createMockResponse = () => {
  const res: any = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    cookie: jest.fn().mockReturnThis(),
    clearCookie: jest.fn().mockReturnThis(),
    redirect: jest.fn().mockReturnThis(),
    locals: {},
  };
  return res;
};

// 创建模拟next函数
export const createMockNext = () => {
  return jest.fn();
};

// 等待异步操作完成
export const waitFor = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// 模拟数据库错误
export const mockDatabaseError = (message: string = 'Database error') => {
  const error = new Error(message);
  error.name = 'QueryFailedError';
  return error;
};

// 验证API响应格式
export const expectSuccessResponse = (response: any, expectedData?: any) => {
  expect(response.body).toHaveProperty('success', true);
  expect(response.body).toHaveProperty('data');
  
  if (expectedData) {
    expect(response.body.data).toMatchObject(expectedData);
  }
};

export const expectErrorResponse = (response: any, expectedMessage?: string) => {
  expect(response.body).toHaveProperty('success', false);
  expect(response.body).toHaveProperty('error');
  expect(response.body.error).toHaveProperty('message');
  
  if (expectedMessage) {
    expect(response.body.error.message).toBe(expectedMessage);
  }
};

// 验证分页响应
export const expectPaginatedResponse = (response: any) => {
  expect(response.body).toHaveProperty('success', true);
  expect(response.body).toHaveProperty('data');
  expect(response.body.data).toHaveProperty('items');
  expect(response.body.data).toHaveProperty('total');
  expect(response.body.data).toHaveProperty('page');
  expect(response.body.data).toHaveProperty('limit');
  expect(response.body.data).toHaveProperty('totalPages');
};

// 创建测试角色
export const createTestRole = (
  name: string = 'test_role',
  permissions: any = {}
): Partial<Role> => {
  return {
    name,
    description: `Test ${name} role`,
    permissions: {
      species: { create: false, read: true, update: false, delete: false },
      files: { create: false, read: true, update: false, delete: false },
      users: { create: false, read: false, update: false, delete: false },
      ...permissions,
    },
  };
};

// 创建测试用户数据
export const createTestUserData = (overrides: any = {}) => {
  return {
    username: `testuser_${Date.now()}`,
    email: `test_${Date.now()}@example.com`,
    password: 'testpassword123',
    ...overrides,
  };
};

// 创建测试物种数据
export const createTestSpeciesData = (overrides: any = {}) => {
  return {
    chineseName: '测试物种',
    englishName: 'Test Species',
    latinName: 'Testus speciesus',
    conservationStatus: '无危',
    description: '这是一个测试物种',
    classification: {
      kingdom: '动物界',
      phylum: '脊索动物门',
      class: '哺乳纲',
      order: '鲸目',
      family: '测试科',
      genus: '测试属',
      species: '测试种',
    },
    ...overrides,
  };
};

// 创建测试音频文件数据
export const createTestAudioFileData = (overrides: any = {}) => {
  return {
    fileName: 'test-audio.wav',
    filePath: 'uploads/audio/test-audio.wav',
    fileSize: 1024000,
    mimeType: 'audio/wav',
    recordingLocation: '测试海域',
    recordingTime: new Date().toISOString(),
    behaviorDescription: '测试行为',
    ...overrides,
  };
};

// 验证JWT令牌
export const verifyTestToken = (token: string): any => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET || 'test-secret');
  } catch (error) {
    return null;
  }
};

// 模拟文件上传
export const mockFileUpload = (files: any[] = []) => {
  return files.map(file => createMockFile(file.name, file.type, file.size));
};

// 清理测试文件
export const cleanupTestFiles = async (filePaths: string[]) => {
  const fs = require('fs').promises;
  const path = require('path');
  
  for (const filePath of filePaths) {
    try {
      const fullPath = path.join(process.cwd(), filePath);
      await fs.unlink(fullPath);
    } catch (error) {
      // 忽略文件不存在的错误
      if (error.code !== 'ENOENT') {
        console.warn(`Failed to cleanup test file: ${filePath}`, error);
      }
    }
  }
};
