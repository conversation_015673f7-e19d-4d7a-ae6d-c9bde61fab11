// 内存管理工具
import { getMemoryUsage } from './performanceUtils';
import type { MemoryInfo, MemoryConfig, MemoryStats } from '../types/memory';

// 默认配置 - 更保守的设置以减少内存压力
const DEFAULT_CONFIG: MemoryConfig = {
  warningThreshold: 75, // 75% - 降低警告阈值
  criticalThreshold: 85, // 85% - 降低严重阈值
  checkInterval: 30000, // 30秒 - 减少检查间隔以更及时发现问题
  historySize: 5, // 保留5个历史记录 - 减少内存占用
  trendSensitivity: 3, // 3%的变化才认为是趋势 - 更敏感的趋势检测
};

// 内存管理器
export class MemoryManager {
  private static instance: MemoryManager;
  private config: MemoryConfig;
  private history: MemoryInfo[] = [];
  private intervalId: NodeJS.Timeout | null = null;
  private listeners: Array<(info: MemoryInfo) => void> = [];
  private warningListeners: Array<(info: MemoryInfo) => void> = [];
  private criticalListeners: Array<(info: MemoryInfo) => void> = [];

  private constructor(config: Partial<MemoryConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  static getInstance(config?: Partial<MemoryConfig>): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager(config);
    }
    return MemoryManager.instance;
  }

  // 开始监控
  start(): void {
    if (this.intervalId || typeof window === 'undefined') return;

    this.intervalId = setInterval(() => {
      this.checkMemory();
    }, this.config.checkInterval);

    // 立即执行一次检查
    this.checkMemory();

    // 页面卸载时清理
    window.addEventListener('beforeunload', this.cleanup);
  }

  // 停止监控
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  // 检查内存
  private checkMemory(): void {
    const memoryUsage = getMemoryUsage();
    const now = Date.now();
    
    const info: MemoryInfo = {
      ...memoryUsage,
      trend: this.calculateTrend(memoryUsage.percentage),
      timestamp: now,
    };

    // 添加到历史记录
    this.history.push(info);
    if (this.history.length > this.config.historySize) {
      this.history.shift();
    }

    // 通知监听器
    this.notifyListeners(info);

    // 检查阈值
    this.checkThresholds(info);
  }

  // 计算趋势
  private calculateTrend(currentPercentage: number): 'increasing' | 'decreasing' | 'stable' {
    if (this.history.length < 3) return 'stable';

    const recent = this.history.slice(-3);
    const avgRecent = recent.reduce((sum, item) => sum + item.percentage, 0) / recent.length;
    const diff = currentPercentage - avgRecent;

    if (Math.abs(diff) < this.config.trendSensitivity) return 'stable';
    return diff > 0 ? 'increasing' : 'decreasing';
  }

  // 检查阈值
  private checkThresholds(info: MemoryInfo): void {
    if (info.percentage >= this.config.criticalThreshold) {
      this.criticalListeners.forEach(listener => listener(info));
    } else if (info.percentage >= this.config.warningThreshold) {
      this.warningListeners.forEach(listener => listener(info));
    }
  }

  // 通知监听器
  private notifyListeners(info: MemoryInfo): void {
    this.listeners.forEach(listener => listener(info));
  }

  // 添加监听器
  onMemoryUpdate(listener: (info: MemoryInfo) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  onWarning(listener: (info: MemoryInfo) => void): () => void {
    this.warningListeners.push(listener);
    return () => {
      const index = this.warningListeners.indexOf(listener);
      if (index > -1) this.warningListeners.splice(index, 1);
    };
  }

  onCritical(listener: (info: MemoryInfo) => void): () => void {
    this.criticalListeners.push(listener);
    return () => {
      const index = this.criticalListeners.indexOf(listener);
      if (index > -1) this.criticalListeners.splice(index, 1);
    };
  }

  // 获取当前内存信息
  getCurrentMemoryInfo(): MemoryInfo | null {
    return this.history.length > 0 ? this.history[this.history.length - 1] : null;
  }

  // 获取内存历史
  getMemoryHistory(): MemoryInfo[] {
    return [...this.history];
  }

  // 获取内存统计
  getMemoryStats(): MemoryStats {
    if (this.history.length === 0) {
      return { average: 0, max: 0, min: 0, trend: 'stable' };
    }

    const percentages = this.history.map(h => h.percentage);
    const average = percentages.reduce((sum, p) => sum + p, 0) / percentages.length;
    const max = Math.max(...percentages);
    const min = Math.min(...percentages);
    const current = this.getCurrentMemoryInfo();

    return {
      average,
      max,
      min,
      trend: current?.trend || 'stable',
    };
  }

  // 强制垃圾回收（如果可用）
  forceGarbageCollection(): boolean {
    if (typeof window !== 'undefined' && (window as any).gc) {
      try {
        (window as any).gc();
        return true;
      } catch (error) {
        console.warn('无法执行垃圾回收:', error);
        return false;
      }
    }
    return false;
  }

  // 清理资源
  private cleanup = (): void => {
    this.stop();
    this.listeners.length = 0;
    this.warningListeners.length = 0;
    this.criticalListeners.length = 0;
    this.history.length = 0;
    window.removeEventListener('beforeunload', this.cleanup);
  };

  // 更新配置
  updateConfig(newConfig: Partial<MemoryConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // 重置历史记录
  resetHistory(): void {
    this.history.length = 0;
  }
}

// 便捷函数
export const createMemoryManager = (config?: Partial<MemoryConfig>) => {
  return MemoryManager.getInstance(config);
};

// 默认实例
export const memoryManager = MemoryManager.getInstance();

// 重新导出类型以便其他模块使用
export type { MemoryInfo, MemoryConfig, MemoryStats } from '../types/memory';
