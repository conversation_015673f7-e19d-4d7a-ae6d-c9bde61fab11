import React, { useState } from 'react';
import { Upload, message, Progress, Typography, Space, Button } from 'antd';
import { 
  InboxOutlined, 
  UploadOutlined, 
  DeleteOutlined,
  EyeOutlined 
} from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd';

const { Dragger } = Upload;
const { Text } = Typography;

interface BaseUploadProps {
  accept?: string;
  maxSize?: number; // MB
  maxCount?: number;
  multiple?: boolean;
  onUploadSuccess?: (file: any) => void;
  onUploadError?: (error: string) => void;
  uploadUrl?: string;
  showPreview?: boolean;
  disabled?: boolean;
}

const BaseUpload: React.FC<BaseUploadProps> = ({
  accept = '*',
  maxSize = 100,
  maxCount = 10,
  multiple = true,
  onUploadSuccess,
  onUploadError,
  uploadUrl = `${import.meta.env.VITE_API_BASE_URL}/files/upload`,
  showPreview = true,
  disabled = false,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);

  // 文件上传前的验证
  const beforeUpload = (file: File) => {
    // 检查文件大小
    const isLtMaxSize = file.size / 1024 / 1024 < maxSize;
    if (!isLtMaxSize) {
      message.error(`文件大小不能超过 ${maxSize}MB`);
      return false;
    }

    // 检查文件类型
    if (accept !== '*') {
      const acceptTypes = accept.split(',').map(type => type.trim());
      const isAcceptedType = acceptTypes.some(type => {
        if (type.startsWith('.')) {
          return file.name.toLowerCase().endsWith(type.toLowerCase());
        } else {
          return file.type.includes(type.replace('*', ''));
        }
      });
      
      if (!isAcceptedType) {
        message.error(`不支持的文件类型，仅支持: ${accept}`);
        return false;
      }
    }

    return true;
  };

  // 自定义上传逻辑
  const customUpload = async (options: any) => {
    const { file, onProgress, onSuccess, onError } = options;
    
    const formData = new FormData();
    formData.append('file', file);

    try {
      setUploading(true);
      
      const xhr = new XMLHttpRequest();
      
      // 上传进度
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const percent = Math.round((event.loaded / event.total) * 100);
          onProgress({ percent });
        }
      });

      // 上传完成
      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            onSuccess(response);
            onUploadSuccess?.(response);
            message.success(`${file.name} 上传成功`);
          } catch (error) {
            onError(error);
            onUploadError?.('上传响应解析失败');
          }
        } else {
          const error = `上传失败: ${xhr.status}`;
          onError(error);
          onUploadError?.(error);
        }
        setUploading(false);
      });

      // 上传错误
      xhr.addEventListener('error', () => {
        const error = '网络错误，上传失败';
        onError(error);
        onUploadError?.(error);
        setUploading(false);
      });

      // 发送请求
      xhr.open('POST', uploadUrl);
      
      // 添加认证头
      const token = localStorage.getItem('token');
      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      }
      
      xhr.send(formData);
      
    } catch (error) {
      onError(error);
      onUploadError?.('上传失败');
      setUploading(false);
    }
  };

  // 文件列表变化
  const handleChange: UploadProps['onChange'] = (info) => {
    let newFileList = [...info.fileList];

    // 限制文件数量
    newFileList = newFileList.slice(-maxCount);

    // 更新文件状态
    newFileList = newFileList.map(file => {
      if (file.response) {
        file.url = file.response.data?.url;
      }
      return file;
    });

    setFileList(newFileList);
  };

  // 移除文件
  const handleRemove = (file: UploadFile) => {
    const index = fileList.indexOf(file);
    const newFileList = fileList.slice();
    newFileList.splice(index, 1);
    setFileList(newFileList);
  };

  // 预览文件
  const handlePreview = (file: UploadFile) => {
    if (file.url || file.preview) {
      window.open(file.url || file.preview, '_blank');
    }
  };

  const uploadProps: UploadProps = {
    name: 'file',
    multiple,
    fileList,
    beforeUpload,
    customRequest: customUpload,
    onChange: handleChange,
    onRemove: handleRemove,
    onPreview: showPreview ? handlePreview : undefined,
    disabled: disabled || uploading,
    showUploadList: {
      showPreviewIcon: showPreview,
      showRemoveIcon: true,
      showDownloadIcon: false,
    },
  };

  return (
    <div>
      <Dragger {...uploadProps} style={{ marginBottom: '16px' }}>
        <p className="ant-upload-drag-icon">
          <InboxOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
        </p>
        <p className="ant-upload-text">
          点击或拖拽文件到此区域上传
        </p>
        <p className="ant-upload-hint">
          <Space direction="vertical" size={4}>
            <Text type="secondary">
              支持单个或批量上传，最多 {maxCount} 个文件
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              文件大小限制: {maxSize}MB | 支持格式: {accept}
            </Text>
          </Space>
        </p>
      </Dragger>

      {/* 上传进度 */}
      {uploading && (
        <div style={{ marginBottom: '16px' }}>
          <Text>正在上传...</Text>
        </div>
      )}

      {/* 操作按钮 */}
      <Space>
        <Button 
          icon={<UploadOutlined />} 
          disabled={disabled || uploading || fileList.length >= maxCount}
        >
          选择文件
        </Button>
        <Button 
          icon={<DeleteOutlined />} 
          onClick={() => setFileList([])}
          disabled={fileList.length === 0}
        >
          清空列表
        </Button>
      </Space>
    </div>
  );
};

export default BaseUpload;
