import { 
  logProfilerData, 
  performanceLogger, 
  getMemoryUsage,
  emergencyDisableProfiler 
} from './performanceUtils';

// Mock console methods
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

describe('Performance Utils - Memory Leak Fix', () => {
  beforeEach(() => {
    console.log = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
    performanceLogger.reset();
    performanceLogger.enable();
  });

  afterEach(() => {
    console.log = originalConsoleLog;
    console.warn = originalConsoleWarn;
    console.error = originalConsoleError;
  });

  describe('logProfilerData frequency limiting', () => {
    test('should limit log frequency to prevent memory overflow', () => {
      // 快速调用多次
      for (let i = 0; i < 20; i++) {
        logProfilerData('test', 'update', 5, 10, 100, 105);
      }

      // 由于频率限制，实际输出的日志应该少于20条
      expect(console.log).toHaveBeenCalledTimes(10); // 每秒最多10条
    });

    test('should stop logging after reaching max total logs', () => {
      const status = performanceLogger.getStatus();
      expect(status.enabled).toBe(true);
      expect(status.logCount).toBe(0);

      // 模拟大量日志输出
      for (let i = 0; i < 1100; i++) {
        logProfilerData('test', 'update', 5, 10, 100, 105);
        // 添加小延迟以绕过频率限制
        if (i % 10 === 0) {
          jest.advanceTimersByTime(1000);
        }
      }

      const finalStatus = performanceLogger.getStatus();
      expect(finalStatus.logCount).toBeLessThanOrEqual(1000);
    });

    test('should only log meaningful performance data', () => {
      // 渲染时间很短的更新不应该被记录
      logProfilerData('test', 'update', 0.5, 10, 100, 105);
      expect(console.log).not.toHaveBeenCalled();

      // mount阶段应该被记录
      logProfilerData('test', 'mount', 0.5, 10, 100, 105);
      expect(console.log).toHaveBeenCalledTimes(1);

      // 渲染时间超过1ms的更新应该被记录
      logProfilerData('test', 'update', 2, 10, 100, 105);
      expect(console.log).toHaveBeenCalledTimes(2);
    });
  });

  describe('Memory monitoring', () => {
    test('should provide memory usage information', () => {
      const memoryUsage = getMemoryUsage();
      
      expect(memoryUsage).toHaveProperty('used');
      expect(memoryUsage).toHaveProperty('total');
      expect(memoryUsage).toHaveProperty('percentage');
      
      expect(typeof memoryUsage.used).toBe('number');
      expect(typeof memoryUsage.total).toBe('number');
      expect(typeof memoryUsage.percentage).toBe('number');
    });

    test('should handle missing memory API gracefully', () => {
      // Mock missing memory API
      const originalPerformance = global.performance;
      (global as any).performance = {};

      const memoryUsage = getMemoryUsage();
      expect(memoryUsage).toEqual({ used: 0, total: 0, percentage: 0 });

      global.performance = originalPerformance;
    });
  });

  describe('Emergency disable functionality', () => {
    test('should disable profiler and provide console commands', () => {
      const originalWindow = global.window;
      (global as any).window = {};

      emergencyDisableProfiler();

      expect(performanceLogger.getStatus().enabled).toBe(false);
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('性能分析器已紧急禁用')
      );

      // 检查全局函数是否被添加
      expect(typeof (global.window as any).enableProfiler).toBe('function');
      expect(typeof (global.window as any).disableProfiler).toBe('function');
      expect(typeof (global.window as any).profilerStatus).toBe('function');

      global.window = originalWindow;
    });
  });

  describe('Performance logger controls', () => {
    test('should allow enabling and disabling', () => {
      expect(performanceLogger.getStatus().enabled).toBe(true);

      performanceLogger.disable();
      expect(performanceLogger.getStatus().enabled).toBe(false);

      performanceLogger.enable();
      expect(performanceLogger.getStatus().enabled).toBe(true);
    });

    test('should reset log count', () => {
      // 生成一些日志
      logProfilerData('test', 'mount', 5, 10, 100, 105);
      expect(performanceLogger.getStatus().logCount).toBeGreaterThan(0);

      performanceLogger.reset();
      expect(performanceLogger.getStatus().logCount).toBe(0);
    });
  });
});
