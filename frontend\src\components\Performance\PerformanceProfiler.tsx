import React, { Profiler, ProfilerOnRenderCallback, useRef, useEffect, useState } from 'react';
import { logProfilerData, performanceLogger, getMemoryUsage } from '../../utils/performanceUtils';

interface PerformanceProfilerProps {
  id: string;
  children: React.ReactNode;
  enabled?: boolean;
  onRender?: ProfilerOnRenderCallback;
}

interface RenderMetrics {
  id: string;
  phase: 'mount' | 'update';
  actualDuration: number;
  baseDuration: number;
  startTime: number;
  commitTime: number;
  timestamp: number;
}

const PerformanceProfiler: React.FC<PerformanceProfilerProps> = ({
  id,
  children,
  enabled = process.env.NODE_ENV === 'development',
  onRender,
}) => {
  const metricsRef = useRef<RenderMetrics[]>([]);
  const statsTimerRef = useRef<NodeJS.Timeout | null>(null);
  const renderCountRef = useRef(0);
  const lastMemoryCheckRef = useRef(0);
  const [isActive, setIsActive] = useState(enabled);

  const handleRender = (
    id,
    phase,
    actualDuration,
    baseDuration,
    startTime,
    commitTime,
    interactions
  ) => {
    if (!isActive) return;

    renderCountRef.current++;

    // 进一步减少内存检查频率：每500次渲染检查一次
    if (renderCountRef.current % 500 === 0) {
      const now = Date.now();
      if (now - lastMemoryCheckRef.current > 20000) { // 增加到至少间隔20秒
        lastMemoryCheckRef.current = now;
        const memoryUsage = getMemoryUsage();

        // 调整阈值：如果内存使用超过85%，自动禁用性能分析（使用更准确的内存限制计算）
        if (memoryUsage.percentage > 85) {
          console.warn(`⚠️ 内存使用过高 (${memoryUsage.percentage.toFixed(1)}% of ${(memoryUsage.limit / 1024 / 1024).toFixed(0)}MB)，自动禁用性能分析器 [${id}]`);
          setIsActive(false);
          performanceLogger.disable();
          return;
        }
      }
    }

    // 记录性能数据
    const metric: RenderMetrics = {
      id,
      phase,
      actualDuration,
      baseDuration,
      startTime,
      commitTime,
      timestamp: Date.now(),
    };

    // 使用ref存储数据，避免触发重新渲染，进一步限制存储数量
    metricsRef.current = [...metricsRef.current.slice(-2), metric]; // 只保留最近3次渲染数据，减少内存占用

    // 输出到控制台（现在有频率和内存限制）
    logProfilerData(id, phase, actualDuration, baseDuration, startTime, commitTime);

    // 调用自定义回调
    if (onRender) {
      onRender(id, phase, actualDuration, baseDuration, startTime, commitTime, interactions);
    }

    // 性能警告 - 进一步提高阈值，减少警告频率
    if (actualDuration > 100) { // 从50ms提高到100ms才警告
      console.warn(`⚠️ Slow render detected in ${id}: ${actualDuration.toFixed(1)}ms`);
    }
  };

  // 在开发环境中定期显示性能统计 - 进一步优化版本
  useEffect(() => {
    if (!enabled || !isActive) return;

    // 清除之前的定时器
    if (statsTimerRef.current) {
      clearInterval(statsTimerRef.current);
    }

    // 设置定时器，每60秒输出一次统计信息（进一步减少频率和内存占用）
    statsTimerRef.current = setInterval(() => {
      const metrics = metricsRef.current;
      if (metrics.length > 0) {
        const avgDuration = metrics.reduce((sum, m) => sum + m.actualDuration, 0) / metrics.length;
        const maxDuration = Math.max(...metrics.map(m => m.actualDuration));

        // 只在有明显性能问题时输出，提高阈值
        if (avgDuration > 10 || maxDuration > 100) {
          console.log(`📊 ${id}: avg ${avgDuration.toFixed(1)}ms, max ${maxDuration.toFixed(1)}ms, renders ${renderCountRef.current}`);
        }
      }
    }, 60000); // 每60秒输出一次

    // 清理函数
    return () => {
      if (statsTimerRef.current) {
        clearInterval(statsTimerRef.current);
        statsTimerRef.current = null;
      }
    };
  }, [id, enabled, isActive]);

  if (!enabled) {
    return <>{children}</>;
  }

  return (
    <Profiler
      id={id}
      onRender={handleRender as ProfilerOnRenderCallback}
    >
      {children}
    </Profiler>
  );
};

export default PerformanceProfiler;
