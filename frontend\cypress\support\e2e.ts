// Cypress E2E 支持文件

// 导入命令
import './commands';

// 全局配置
Cypress.on('uncaught:exception', (err, runnable) => {
  // 忽略某些不影响测试的错误
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false;
  }
  if (err.message.includes('Non-Error promise rejection captured')) {
    return false;
  }
  return true;
});

// 测试前设置
beforeEach(() => {
  // 清除本地存储
  cy.clearLocalStorage();
  cy.clearCookies();
  
  // 设置视窗大小
  cy.viewport(1280, 720);
  
  // 拦截API请求
  cy.intercept('GET', '/health', { fixture: 'health.json' }).as('healthCheck');
  
  // 模拟认证API
  cy.intercept('POST', '/api/auth/login', { fixture: 'auth/login-success.json' }).as('login');
  cy.intercept('POST', '/api/auth/register', { fixture: 'auth/register-success.json' }).as('register');
  cy.intercept('GET', '/api/auth/profile', { fixture: 'auth/profile.json' }).as('profile');
  
  // 模拟物种API
  cy.intercept('GET', '/api/species/search*', { fixture: 'species/search-results.json' }).as('speciesSearch');
  cy.intercept('GET', '/api/species/*/details', { fixture: 'species/species-details.json' }).as('speciesDetails');
  
  // 模拟地理API
  cy.intercept('GET', '/api/geography/distributions', { fixture: 'geography/distributions.json' }).as('distributions');
  cy.intercept('GET', '/api/geography/collection-points', { fixture: 'geography/collection-points.json' }).as('collectionPoints');
  
  // 模拟文件上传API
  cy.intercept('POST', '/api/files/upload*', { fixture: 'files/upload-success.json' }).as('fileUpload');
});

// 测试后清理
afterEach(() => {
  // 清理测试数据
  cy.task('clearTestData', null, { log: false });
});

// 添加自定义命令类型声明
declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * 登录用户
       * @param username 用户名
       * @param password 密码
       */
      login(username: string, password: string): Chainable<void>;
      
      /**
       * 登出用户
       */
      logout(): Chainable<void>;
      
      /**
       * 等待页面加载完成
       */
      waitForPageLoad(): Chainable<void>;
      
      /**
       * 上传文件
       * @param selector 文件输入选择器
       * @param fileName 文件名
       * @param fileType 文件类型
       */
      uploadFile(selector: string, fileName: string, fileType?: string): Chainable<void>;
      
      /**
       * 检查可访问性
       */
      checkA11y(): Chainable<void>;
      
      /**
       * 等待API请求完成
       * @param alias API别名
       */
      waitForApi(alias: string): Chainable<void>;
      
      /**
       * 模拟网络错误
       * @param url URL模式
       */
      mockNetworkError(url: string): Chainable<void>;
      
      /**
       * 设置认证状态
       * @param isAuthenticated 是否已认证
       */
      setAuthState(isAuthenticated: boolean): Chainable<void>;
    }
  }
}
