import { apiClient } from './config';
import type { 
  Species, 
  SpeciesFormData, 
  SpeciesSearchParams, 
  TaxonomyStats, 
  ConservationStats,
  ApiResponse, 
  PaginatedResponse 
} from '../../types';

export const speciesApi = {
  // 获取物种列表（管理端）
  getSpecies: async (params: SpeciesSearchParams = {}): Promise<PaginatedResponse<Species>> => {
    const response = await apiClient.get<ApiResponse<PaginatedResponse<Species>>>('/species', { params });
    return response.data.data!;
  },

  // 搜索物种（公开接口）
  searchSpecies: async (params: SpeciesSearchParams = {}): Promise<PaginatedResponse<Species>> => {
    const response = await apiClient.get<ApiResponse<PaginatedResponse<Species>>>('/species/search', { params });
    return response.data.data!;
  },

  // 获取物种详情
  getSpeciesById: async (id: string): Promise<Species> => {
    const response = await apiClient.get<ApiResponse<Species>>(`/species/${id}`);
    return response.data.data!;
  },

  // 获取物种详细信息（包含媒体文件等）
  getSpeciesDetails: async (id: string): Promise<any> => {
    const response = await apiClient.get<ApiResponse<any>>(`/species/${id}/details`);
    return response.data.data!;
  },

  // 创建物种
  createSpecies: async (data: SpeciesFormData): Promise<Species> => {
    const response = await apiClient.post<ApiResponse<Species>>('/species', data);
    return response.data.data!;
  },

  // 更新物种
  updateSpecies: async (id: string, data: Partial<SpeciesFormData>): Promise<Species> => {
    const response = await apiClient.put<ApiResponse<Species>>(`/species/${id}`, data);
    return response.data.data!;
  },

  // 删除物种
  deleteSpecies: async (id: string): Promise<void> => {
    await apiClient.delete<ApiResponse>(`/species/${id}`);
  },

  // 批量删除物种
  batchDeleteSpecies: async (ids: string[]): Promise<void> => {
    await apiClient.post<ApiResponse>('/species/batch-delete', { ids });
  },

  // 获取分类统计
  getTaxonomyStats: async (): Promise<TaxonomyStats> => {
    const response = await apiClient.get<ApiResponse<TaxonomyStats>>('/species/stats/taxonomy');
    return response.data.data!;
  },

  // 获取保护状态统计
  getConservationStats: async (): Promise<ConservationStats[]> => {
    const response = await apiClient.get<ApiResponse<ConservationStats[]>>('/species/stats/conservation');
    return response.data.data!;
  },

  // 获取最近添加的物种
  getRecentSpecies: async (limit: number = 10): Promise<Species[]> => {
    const response = await apiClient.get<ApiResponse<Species[]>>('/species/recent', { 
      params: { limit } 
    });
    return response.data.data!;
  },

  // 获取随机物种
  getRandomSpecies: async (limit: number = 5): Promise<Species[]> => {
    const response = await apiClient.get<ApiResponse<Species[]>>('/species/random', { 
      params: { limit } 
    });
    return response.data.data!;
  },

  // 导入物种数据
  importSpecies: async (file: File): Promise<{ success: number; failed: number; errors: string[] }> => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await apiClient.post<ApiResponse<{ success: number; failed: number; errors: string[] }>>(
      '/species/import', 
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data.data!;
  },

  // 导出物种数据
  exportSpecies: async (params: SpeciesSearchParams = {}): Promise<Blob> => {
    const response = await apiClient.get('/species/export', { 
      params,
      responseType: 'blob'
    });
    return response.data;
  },
};
