# 服务器配置
PORT=3001
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=marine_bio_platform

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=50MB

# CORS配置
CORS_ORIGIN=http://localhost:5174,http://localhost:5173

# 音频识别API配置
AUDIO_RECOGNITION_API_URL=https://api.example.com/recognize
AUDIO_RECOGNITION_API_KEY=your-api-key
