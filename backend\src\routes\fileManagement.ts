import { Router } from 'express';
import { FileManagementController } from '../controllers/FileManagementController';
import { authenticateToken, requirePermission } from '../middleware/auth';

const router = Router();
const fileManagementController = new FileManagementController();

// 所有文件管理路由都需要认证和管理员权限
router.use(authenticateToken);
router.use(requirePermission('system', 'write'));

// 文件清理
router.post('/cleanup/temp', fileManagementController.cleanupTempFiles);
router.post('/cleanup/orphaned', fileManagementController.cleanupOrphanedFiles);
router.post('/cleanup/all', fileManagementController.runCleanupTask);

// 存储统计
router.get('/stats', fileManagementController.getStorageStats);

export default router;
