import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateMediaTables1700000003000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建媒体文件表
    await queryRunner.createTable(
      new Table({
        name: 'media_files',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'species_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'fileType',
            type: 'enum',
            enum: ['image', 'video'],
            isNullable: false,
          },
          {
            name: 'filePath',
            type: 'varchar',
            length: '500',
            isNullable: false,
          },
          {
            name: 'fileName',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'fileSize',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'mimeType',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'title',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'isRepresentative',
            type: 'boolean',
            default: false,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['species_id'],
            referencedTableName: 'species',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
        indices: [
          {
            name: 'IDX_MEDIA_SPECIES_ID',
            columnNames: ['species_id'],
          },
          {
            name: 'IDX_MEDIA_FILE_TYPE',
            columnNames: ['fileType'],
          },
          {
            name: 'IDX_MEDIA_IS_REPRESENTATIVE',
            columnNames: ['isRepresentative'],
          },
        ],
      }),
      true
    );

    // 创建音频文件表
    await queryRunner.createTable(
      new Table({
        name: 'audio_files',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'species_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'filePath',
            type: 'varchar',
            length: '500',
            isNullable: false,
          },
          {
            name: 'fileName',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'fileSize',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'mimeType',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'recordingLocation',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'recordingTime',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'behaviorDescription',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'samplingRate',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'sensitivity',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true,
          },
          {
            name: 'amplificationFactor',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true,
          },
          {
            name: 'latitude',
            type: 'decimal',
            precision: 10,
            scale: 8,
            isNullable: true,
          },
          {
            name: 'longitude',
            type: 'decimal',
            precision: 11,
            scale: 8,
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['species_id'],
            referencedTableName: 'species',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
        indices: [
          {
            name: 'IDX_AUDIO_SPECIES_ID',
            columnNames: ['species_id'],
          },
          {
            name: 'IDX_AUDIO_RECORDING_TIME',
            columnNames: ['recordingTime'],
          },
          {
            name: 'IDX_AUDIO_LOCATION',
            columnNames: ['latitude', 'longitude'],
          },
        ],
      }),
      true
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('audio_files');
    await queryRunner.dropTable('media_files');
  }
}
