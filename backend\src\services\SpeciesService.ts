import { Repository, Like, ILike, In } from 'typeorm';
import { AppDataSource } from '../config/database';
import { Species, TaxonomicClassification, MediaFile, AudioFile, DistributionRange } from '../models';

export interface CreateSpeciesData {
  chineseName: string;
  englishName?: string;
  latinName: string;
  conservationStatus?: string;
  description?: string;
  classification?: {
    kingdom?: string;
    phylum?: string;
    class?: string;
    order?: string;
    family?: string;
    genus?: string;
    species?: string;
  };
}

export interface UpdateSpeciesData extends Partial<CreateSpeciesData> {}

export interface SpeciesSearchParams {
  keyword?: string;
  conservationStatus?: string;
  kingdom?: string;
  phylum?: string;
  class?: string;
  order?: string;
  family?: string;
  genus?: string;
  page?: number;
  limit?: number;
  sortBy?: 'chineseName' | 'latinName' | 'createdAt';
  sortOrder?: 'ASC' | 'DESC';
}

export interface SpeciesListResult {
  species: Species[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class SpeciesService {
  private speciesRepository: Repository<Species>;
  private classificationRepository: Repository<TaxonomicClassification>;
  private mediaRepository: Repository<MediaFile>;
  private audioRepository: Repository<AudioFile>;
  private distributionRepository: Repository<DistributionRange>;

  constructor() {
    this.speciesRepository = AppDataSource.getRepository(Species);
    this.classificationRepository = AppDataSource.getRepository(TaxonomicClassification);
    this.mediaRepository = AppDataSource.getRepository(MediaFile);
    this.audioRepository = AppDataSource.getRepository(AudioFile);
    this.distributionRepository = AppDataSource.getRepository(DistributionRange);
  }

  /**
   * 创建物种
   */
  async createSpecies(data: CreateSpeciesData): Promise<Species> {
    // 检查拉丁学名是否已存在
    const existingSpecies = await this.speciesRepository.findOne({
      where: { latinName: data.latinName },
    });

    if (existingSpecies) {
      throw new Error('拉丁学名已存在');
    }

    // 创建物种
    const species = new Species();
    species.chineseName = data.chineseName;
    species.englishName = data.englishName;
    species.latinName = data.latinName;
    species.conservationStatus = data.conservationStatus;
    species.description = data.description;

    const savedSpecies = await this.speciesRepository.save(species);

    // 创建分类学分类
    if (data.classification) {
      const classification = new TaxonomicClassification();
      classification.species = savedSpecies;
      Object.assign(classification, data.classification);
      await this.classificationRepository.save(classification);
    }

    return await this.getSpeciesById(savedSpecies.id);
  }

  /**
   * 更新物种
   */
  async updateSpecies(id: string, data: UpdateSpeciesData): Promise<Species> {
    const species = await this.speciesRepository.findOne({
      where: { id },
      relations: ['classification'],
    });

    if (!species) {
      throw new Error('物种不存在');
    }

    // 检查拉丁学名唯一性
    if (data.latinName && data.latinName !== species.latinName) {
      const existingSpecies = await this.speciesRepository.findOne({
        where: { latinName: data.latinName },
      });

      if (existingSpecies) {
        throw new Error('拉丁学名已存在');
      }
    }

    // 更新物种基本信息
    const { classification, ...speciesData } = data;
    Object.assign(species, speciesData);
    await this.speciesRepository.save(species);

    // 更新分类学分类
    if (classification) {
      if (species.classification) {
        Object.assign(species.classification, classification);
        await this.classificationRepository.save(species.classification);
      } else {
        const newClassification = new TaxonomicClassification();
        newClassification.species = species;
        Object.assign(newClassification, classification);
        await this.classificationRepository.save(newClassification);
      }
    }

    return await this.getSpeciesById(id);
  }

  /**
   * 删除物种
   */
  async deleteSpecies(id: string): Promise<void> {
    const species = await this.speciesRepository.findOne({
      where: { id },
    });

    if (!species) {
      throw new Error('物种不存在');
    }

    // 级联删除会自动处理相关数据
    await this.speciesRepository.remove(species);
  }

  /**
   * 根据ID获取物种详情
   */
  async getSpeciesById(id: string): Promise<Species> {
    const species = await this.speciesRepository.findOne({
      where: { id },
      relations: ['classification'],
    });

    if (!species) {
      throw new Error('物种不存在');
    }

    return species;
  }

  /**
   * 获取物种完整详情（包含媒体文件和分布信息）
   */
  async getSpeciesDetails(id: string): Promise<any> {
    const species = await this.getSpeciesById(id);

    // 获取媒体文件
    const mediaFiles = await this.mediaRepository.find({
      where: { species: { id } },
      order: { createdAt: 'DESC' },
    });

    // 获取音频文件
    const audioFiles = await this.audioRepository.find({
      where: { species: { id } },
      order: { createdAt: 'DESC' },
    });

    // 获取分布范围数据
    const distributionQuery = `
      SELECT 
        dr.id,
        ST_AsGeoJSON(dr.geometry) as geometry,
        dr.kml_file_path,
        dr.created_at
      FROM distribution_ranges dr
      WHERE dr.species_id = $1
      AND dr.geometry IS NOT NULL
    `;
    const distributions = await AppDataSource.query(distributionQuery, [id]);

    // 统计信息
    const stats = {
      imageCount: mediaFiles.filter(f => f.fileType === 'image').length,
      videoCount: mediaFiles.filter(f => f.fileType === 'video').length,
      audioCount: audioFiles.length,
      distributionCount: distributions.length,
    };

    return {
      ...species,
      mediaFiles: mediaFiles.map(file => ({
        id: file.id,
        fileType: file.fileType,
        fileName: file.fileName,
        title: file.title,
        description: file.description,
        isRepresentative: file.isRepresentative,
        createdAt: file.createdAt,
      })),
      audioFiles: audioFiles.map(file => ({
        id: file.id,
        fileName: file.fileName,
        recordingLocation: file.recordingLocation,
        recordingTime: file.recordingTime,
        behaviorDescription: file.behaviorDescription,
        latitude: file.latitude,
        longitude: file.longitude,
        createdAt: file.createdAt,
      })),
      distributions: distributions.map((row: any) => ({
        id: row.id,
        geometry: row.geometry ? JSON.parse(row.geometry) : null,
        kmlFilePath: row.kml_file_path,
        createdAt: row.created_at,
      })),
      stats,
    };
  }

  /**
   * 搜索和筛选物种
   */
  async searchSpecies(params: SpeciesSearchParams): Promise<SpeciesListResult> {
    const {
      keyword,
      conservationStatus,
      kingdom,
      phylum,
      class: className,
      order,
      family,
      genus,
      page = 1,
      limit = 20,
      sortBy = 'chineseName',
      sortOrder = 'ASC',
    } = params;

    const queryBuilder = this.speciesRepository
      .createQueryBuilder('species')
      .leftJoinAndSelect('species.classification', 'classification');

    // 关键词搜索
    if (keyword) {
      queryBuilder.andWhere(
        '(species.chineseName ILIKE :keyword OR species.englishName ILIKE :keyword OR species.latinName ILIKE :keyword)',
        { keyword: `%${keyword}%` }
      );
    }

    // 保护状态筛选
    if (conservationStatus) {
      queryBuilder.andWhere('species.conservationStatus = :conservationStatus', {
        conservationStatus,
      });
    }

    // 分类学筛选
    if (kingdom) {
      queryBuilder.andWhere('classification.kingdom = :kingdom', { kingdom });
    }
    if (phylum) {
      queryBuilder.andWhere('classification.phylum = :phylum', { phylum });
    }
    if (className) {
      queryBuilder.andWhere('classification.class = :class', { class: className });
    }
    if (order) {
      queryBuilder.andWhere('classification.order = :order', { order });
    }
    if (family) {
      queryBuilder.andWhere('classification.family = :family', { family });
    }
    if (genus) {
      queryBuilder.andWhere('classification.genus = :genus', { genus });
    }

    // 排序
    queryBuilder.orderBy(`species.${sortBy}`, sortOrder);

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [species, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      species,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * 获取分类学统计信息
   */
  async getTaxonomyStats(): Promise<any> {
    const stats = await AppDataSource.query(`
      SELECT 
        COUNT(DISTINCT tc.kingdom) as kingdom_count,
        COUNT(DISTINCT tc.phylum) as phylum_count,
        COUNT(DISTINCT tc.class) as class_count,
        COUNT(DISTINCT tc.order) as order_count,
        COUNT(DISTINCT tc.family) as family_count,
        COUNT(DISTINCT tc.genus) as genus_count,
        COUNT(s.id) as species_count
      FROM species s
      LEFT JOIN taxonomic_classifications tc ON s.id = tc.species_id
    `);

    return stats[0];
  }

  /**
   * 获取保护状态统计
   */
  async getConservationStats(): Promise<any> {
    const stats = await AppDataSource.query(`
      SELECT 
        conservation_status,
        COUNT(*) as count
      FROM species
      WHERE conservation_status IS NOT NULL
      GROUP BY conservation_status
      ORDER BY count DESC
    `);

    return stats;
  }

  /**
   * 获取最近添加的物种
   */
  async getRecentSpecies(limit: number = 10): Promise<Species[]> {
    return await this.speciesRepository.find({
      relations: ['classification'],
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * 获取随机物种（用于首页展示）
   */
  async getRandomSpecies(limit: number = 6): Promise<Species[]> {
    const query = `
      SELECT s.*, tc.*
      FROM species s
      LEFT JOIN taxonomic_classifications tc ON s.id = tc.species_id
      ORDER BY RANDOM()
      LIMIT $1
    `;

    const results = await AppDataSource.query(query, [limit]);
    
    // 手动构建物种对象（因为原生查询不会自动映射关系）
    return results.map((row: any) => {
      const species = new Species();
      species.id = row.id;
      species.chineseName = row.chinese_name;
      species.englishName = row.english_name;
      species.latinName = row.latin_name;
      species.conservationStatus = row.conservation_status;
      species.description = row.description;
      species.representativeImagePath = row.representative_image_path;
      species.createdAt = row.created_at;
      species.updatedAt = row.updated_at;

      if (row.kingdom || row.phylum || row.class) {
        const classification = new TaxonomicClassification();
        classification.kingdom = row.kingdom;
        classification.phylum = row.phylum;
        classification.class = row.class;
        classification.order = row.order;
        classification.family = row.family;
        classification.genus = row.genus;
        classification.speciesName = row.species_name;
        species.classification = classification;
      }

      return species;
    });
  }
}
